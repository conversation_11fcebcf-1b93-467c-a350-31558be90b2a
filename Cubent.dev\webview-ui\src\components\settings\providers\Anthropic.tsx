import { useCallback, useState } from "react"
import { Checkbox } from "vscrui"
import { VSCodeTextField } from "@vscode/webview-ui-toolkit/react"

import type { ProviderSettings } from "@cubent/types"

import { useAppTranslation } from "@src/i18n/TranslationContext"
import { VSCodeButtonLink } from "@src/components/common/VSCodeButtonLink"

import { inputEventTransform, noTransform } from "../transforms"

type AnthropicProps = {
	apiConfiguration: ProviderSettings
	setApiConfigurationField: (field: keyof ProviderSettings, value: ProviderSettings[keyof ProviderSettings]) => void
	isByakProfile?: boolean
}

export const Anthropic = ({ apiConfiguration, setApiConfigurationField, isByakProfile = false }: AnthropicProps) => {
	const { t } = useAppTranslation()

	const [anthropicBaseUrlSelected, setAnthropicBaseUrlSelected] = useState(!!apiConfiguration?.anthropicBaseUrl)

	const handleInputChange = useCallback(
		<K extends keyof ProviderSettings, E>(
			field: K,
			transform: (event: E) => ProviderSettings[K] = inputEventTransform,
		) =>
			(event: E | Event) => {
				setApiConfigurationField(field, transform(event as E))
			},
		[setApiConfigurationField],
	)

	return (
		<>
			{/* API key input hidden for BYAK profiles as requested */}
			{/* {isByakProfile && (
				<>
					<VSCodeTextField
						value={apiConfiguration?.apiKey || ""}
						type="password"
						onInput={handleInputChange("apiKey")}
						placeholder={t("settings:placeholders.apiKey")}
						className="w-full">
						<label className="block font-medium mb-1">{t("settings:providers.anthropicApiKey")}</label>
					</VSCodeTextField>
					<div className="text-sm text-vscode-descriptionForeground -mt-2">
						{t("settings:providers.apiKeyStorageNotice")}
					</div>
					{!apiConfiguration?.apiKey && (
						<VSCodeButtonLink href="https://console.anthropic.com/settings/keys" appearance="secondary">
							{t("settings:providers.getAnthropicApiKey")}
						</VSCodeButtonLink>
					)}
				</>
			)} */}
			{!isByakProfile && (
				<div className="text-sm text-vscode-descriptionForeground mb-3">
					API key is pre-configured for this model.
				</div>
			)}
			{isByakProfile && (
				<div>
					<Checkbox
						checked={anthropicBaseUrlSelected}
						onChange={(checked: boolean) => {
							setAnthropicBaseUrlSelected(checked)

							if (!checked) {
								setApiConfigurationField("anthropicBaseUrl", "")
								setApiConfigurationField("anthropicUseAuthToken", false)
							}
						}}>
						{t("settings:providers.useCustomBaseUrl")}
					</Checkbox>
					{anthropicBaseUrlSelected && (
						<>
							<VSCodeTextField
								value={apiConfiguration?.anthropicBaseUrl || ""}
								type="url"
								onInput={handleInputChange("anthropicBaseUrl")}
								placeholder="https://api.anthropic.com"
								className="w-full mt-1"
							/>
							<Checkbox
								checked={apiConfiguration?.anthropicUseAuthToken ?? false}
								onChange={handleInputChange("anthropicUseAuthToken", noTransform)}
								className="w-full mt-1">
								{t("settings:providers.anthropicUseAuthToken")}
							</Checkbox>
						</>
					)}
				</div>
			)}
		</>
	)
}
