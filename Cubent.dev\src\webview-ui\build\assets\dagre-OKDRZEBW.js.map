{"version": 3, "file": "dagre-OKDRZEBW.js", "sources": ["../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/graphlib/json.js", "../../../../node_modules/.pnpm/mermaid@11.6.0/node_modules/mermaid/dist/chunks/mermaid.core/dagre-OKDRZEBW.mjs"], "sourcesContent": ["import * as _ from 'lodash-es';\nimport { Graph } from './graph.js';\n\nexport { write, read };\n\nfunction write(g) {\n  var json = {\n    options: {\n      directed: g.isDirected(),\n      multigraph: g.isMultigraph(),\n      compound: g.isCompound(),\n    },\n    nodes: writeNodes(g),\n    edges: writeEdges(g),\n  };\n  if (!_.isUndefined(g.graph())) {\n    json.value = _.clone(g.graph());\n  }\n  return json;\n}\n\nfunction writeNodes(g) {\n  return _.map(g.nodes(), function (v) {\n    var nodeValue = g.node(v);\n    var parent = g.parent(v);\n    var node = { v: v };\n    if (!_.isUndefined(nodeValue)) {\n      node.value = nodeValue;\n    }\n    if (!_.isUndefined(parent)) {\n      node.parent = parent;\n    }\n    return node;\n  });\n}\n\nfunction writeEdges(g) {\n  return _.map(g.edges(), function (e) {\n    var edgeValue = g.edge(e);\n    var edge = { v: e.v, w: e.w };\n    if (!_.isUndefined(e.name)) {\n      edge.name = e.name;\n    }\n    if (!_.isUndefined(edgeValue)) {\n      edge.value = edgeValue;\n    }\n    return edge;\n  });\n}\n\nfunction read(json) {\n  var g = new Graph(json.options).setGraph(json.value);\n  _.each(json.nodes, function (entry) {\n    g.setNode(entry.v, entry.value);\n    if (entry.parent) {\n      g.setParent(entry.v, entry.parent);\n    }\n  });\n  _.each(json.edges, function (entry) {\n    g.setEdge({ v: entry.v, w: entry.w, name: entry.name }, entry.value);\n  });\n  return g;\n}\n", "import {\n  clear as clear2,\n  insertEdge,\n  insertEdgeLabel,\n  markers_default,\n  positionEdgeLabel\n} from \"./chunk-IIMUDSI4.mjs\";\nimport \"./chunk-VV3M67IP.mjs\";\nimport {\n  clear,\n  clear2 as clear3,\n  insertCluster,\n  insertNode,\n  positionNode,\n  setNodeElem,\n  updateNodeBounds\n} from \"./chunk-HRU6DDCH.mjs\";\nimport {\n  getSubGraphTitleMargins\n} from \"./chunk-K557N5IZ.mjs\";\nimport \"./chunk-H2D2JQ3I.mjs\";\nimport \"./chunk-C3MQ5ANM.mjs\";\nimport \"./chunk-O4NI6UNU.mjs\";\nimport {\n  __name,\n  getConfig2 as getConfig,\n  log\n} from \"./chunk-YTJNT7DU.mjs\";\n\n// src/rendering-util/layout-algorithms/dagre/index.js\nimport { layout as dagreLayout } from \"dagre-d3-es/src/dagre/index.js\";\nimport * as graphlibJson2 from \"dagre-d3-es/src/graphlib/json.js\";\nimport * as graphlib2 from \"dagre-d3-es/src/graphlib/index.js\";\n\n// src/rendering-util/layout-algorithms/dagre/mermaid-graphlib.js\nimport * as graphlib from \"dagre-d3-es/src/graphlib/index.js\";\nimport * as graphlibJson from \"dagre-d3-es/src/graphlib/json.js\";\nvar clusterDb = /* @__PURE__ */ new Map();\nvar descendants = /* @__PURE__ */ new Map();\nvar parents = /* @__PURE__ */ new Map();\nvar clear4 = /* @__PURE__ */ __name(() => {\n  descendants.clear();\n  parents.clear();\n  clusterDb.clear();\n}, \"clear\");\nvar isDescendant = /* @__PURE__ */ __name((id, ancestorId) => {\n  const ancestorDescendants = descendants.get(ancestorId) || [];\n  log.trace(\"In isDescendant\", ancestorId, \" \", id, \" = \", ancestorDescendants.includes(id));\n  return ancestorDescendants.includes(id);\n}, \"isDescendant\");\nvar edgeInCluster = /* @__PURE__ */ __name((edge, clusterId) => {\n  const clusterDescendants = descendants.get(clusterId) || [];\n  log.info(\"Descendants of \", clusterId, \" is \", clusterDescendants);\n  log.info(\"Edge is \", edge);\n  if (edge.v === clusterId || edge.w === clusterId) {\n    return false;\n  }\n  if (!clusterDescendants) {\n    log.debug(\"Tilt, \", clusterId, \",not in descendants\");\n    return false;\n  }\n  return clusterDescendants.includes(edge.v) || isDescendant(edge.v, clusterId) || isDescendant(edge.w, clusterId) || clusterDescendants.includes(edge.w);\n}, \"edgeInCluster\");\nvar copy = /* @__PURE__ */ __name((clusterId, graph, newGraph, rootId) => {\n  log.warn(\n    \"Copying children of \",\n    clusterId,\n    \"root\",\n    rootId,\n    \"data\",\n    graph.node(clusterId),\n    rootId\n  );\n  const nodes = graph.children(clusterId) || [];\n  if (clusterId !== rootId) {\n    nodes.push(clusterId);\n  }\n  log.warn(\"Copying (nodes) clusterId\", clusterId, \"nodes\", nodes);\n  nodes.forEach((node) => {\n    if (graph.children(node).length > 0) {\n      copy(node, graph, newGraph, rootId);\n    } else {\n      const data = graph.node(node);\n      log.info(\"cp \", node, \" to \", rootId, \" with parent \", clusterId);\n      newGraph.setNode(node, data);\n      if (rootId !== graph.parent(node)) {\n        log.warn(\"Setting parent\", node, graph.parent(node));\n        newGraph.setParent(node, graph.parent(node));\n      }\n      if (clusterId !== rootId && node !== clusterId) {\n        log.debug(\"Setting parent\", node, clusterId);\n        newGraph.setParent(node, clusterId);\n      } else {\n        log.info(\"In copy \", clusterId, \"root\", rootId, \"data\", graph.node(clusterId), rootId);\n        log.debug(\n          \"Not Setting parent for node=\",\n          node,\n          \"cluster!==rootId\",\n          clusterId !== rootId,\n          \"node!==clusterId\",\n          node !== clusterId\n        );\n      }\n      const edges = graph.edges(node);\n      log.debug(\"Copying Edges\", edges);\n      edges.forEach((edge) => {\n        log.info(\"Edge\", edge);\n        const data2 = graph.edge(edge.v, edge.w, edge.name);\n        log.info(\"Edge data\", data2, rootId);\n        try {\n          if (edgeInCluster(edge, rootId)) {\n            log.info(\"Copying as \", edge.v, edge.w, data2, edge.name);\n            newGraph.setEdge(edge.v, edge.w, data2, edge.name);\n            log.info(\"newGraph edges \", newGraph.edges(), newGraph.edge(newGraph.edges()[0]));\n          } else {\n            log.info(\n              \"Skipping copy of edge \",\n              edge.v,\n              \"-->\",\n              edge.w,\n              \" rootId: \",\n              rootId,\n              \" clusterId:\",\n              clusterId\n            );\n          }\n        } catch (e) {\n          log.error(e);\n        }\n      });\n    }\n    log.debug(\"Removing node\", node);\n    graph.removeNode(node);\n  });\n}, \"copy\");\nvar extractDescendants = /* @__PURE__ */ __name((id, graph) => {\n  const children = graph.children(id);\n  let res = [...children];\n  for (const child of children) {\n    parents.set(child, id);\n    res = [...res, ...extractDescendants(child, graph)];\n  }\n  return res;\n}, \"extractDescendants\");\nvar findCommonEdges = /* @__PURE__ */ __name((graph, id1, id2) => {\n  const edges1 = graph.edges().filter((edge) => edge.v === id1 || edge.w === id1);\n  const edges2 = graph.edges().filter((edge) => edge.v === id2 || edge.w === id2);\n  const edges1Prim = edges1.map((edge) => {\n    return { v: edge.v === id1 ? id2 : edge.v, w: edge.w === id1 ? id1 : edge.w };\n  });\n  const edges2Prim = edges2.map((edge) => {\n    return { v: edge.v, w: edge.w };\n  });\n  const result = edges1Prim.filter((edgeIn1) => {\n    return edges2Prim.some((edge) => edgeIn1.v === edge.v && edgeIn1.w === edge.w);\n  });\n  return result;\n}, \"findCommonEdges\");\nvar findNonClusterChild = /* @__PURE__ */ __name((id, graph, clusterId) => {\n  const children = graph.children(id);\n  log.trace(\"Searching children of id \", id, children);\n  if (children.length < 1) {\n    return id;\n  }\n  let reserve;\n  for (const child of children) {\n    const _id = findNonClusterChild(child, graph, clusterId);\n    const commonEdges = findCommonEdges(graph, clusterId, _id);\n    if (_id) {\n      if (commonEdges.length > 0) {\n        reserve = _id;\n      } else {\n        return _id;\n      }\n    }\n  }\n  return reserve;\n}, \"findNonClusterChild\");\nvar getAnchorId = /* @__PURE__ */ __name((id) => {\n  if (!clusterDb.has(id)) {\n    return id;\n  }\n  if (!clusterDb.get(id).externalConnections) {\n    return id;\n  }\n  if (clusterDb.has(id)) {\n    return clusterDb.get(id).id;\n  }\n  return id;\n}, \"getAnchorId\");\nvar adjustClustersAndEdges = /* @__PURE__ */ __name((graph, depth) => {\n  if (!graph || depth > 10) {\n    log.debug(\"Opting out, no graph \");\n    return;\n  } else {\n    log.debug(\"Opting in, graph \");\n  }\n  graph.nodes().forEach(function(id) {\n    const children = graph.children(id);\n    if (children.length > 0) {\n      log.warn(\n        \"Cluster identified\",\n        id,\n        \" Replacement id in edges: \",\n        findNonClusterChild(id, graph, id)\n      );\n      descendants.set(id, extractDescendants(id, graph));\n      clusterDb.set(id, { id: findNonClusterChild(id, graph, id), clusterData: graph.node(id) });\n    }\n  });\n  graph.nodes().forEach(function(id) {\n    const children = graph.children(id);\n    const edges = graph.edges();\n    if (children.length > 0) {\n      log.debug(\"Cluster identified\", id, descendants);\n      edges.forEach((edge) => {\n        const d1 = isDescendant(edge.v, id);\n        const d2 = isDescendant(edge.w, id);\n        if (d1 ^ d2) {\n          log.warn(\"Edge: \", edge, \" leaves cluster \", id);\n          log.warn(\"Descendants of XXX \", id, \": \", descendants.get(id));\n          clusterDb.get(id).externalConnections = true;\n        }\n      });\n    } else {\n      log.debug(\"Not a cluster \", id, descendants);\n    }\n  });\n  for (let id of clusterDb.keys()) {\n    const nonClusterChild = clusterDb.get(id).id;\n    const parent = graph.parent(nonClusterChild);\n    if (parent !== id && clusterDb.has(parent) && !clusterDb.get(parent).externalConnections) {\n      clusterDb.get(id).id = parent;\n    }\n  }\n  graph.edges().forEach(function(e) {\n    const edge = graph.edge(e);\n    log.warn(\"Edge \" + e.v + \" -> \" + e.w + \": \" + JSON.stringify(e));\n    log.warn(\"Edge \" + e.v + \" -> \" + e.w + \": \" + JSON.stringify(graph.edge(e)));\n    let v = e.v;\n    let w = e.w;\n    log.warn(\n      \"Fix XXX\",\n      clusterDb,\n      \"ids:\",\n      e.v,\n      e.w,\n      \"Translating: \",\n      clusterDb.get(e.v),\n      \" --- \",\n      clusterDb.get(e.w)\n    );\n    if (clusterDb.get(e.v) || clusterDb.get(e.w)) {\n      log.warn(\"Fixing and trying - removing XXX\", e.v, e.w, e.name);\n      v = getAnchorId(e.v);\n      w = getAnchorId(e.w);\n      graph.removeEdge(e.v, e.w, e.name);\n      if (v !== e.v) {\n        const parent = graph.parent(v);\n        clusterDb.get(parent).externalConnections = true;\n        edge.fromCluster = e.v;\n      }\n      if (w !== e.w) {\n        const parent = graph.parent(w);\n        clusterDb.get(parent).externalConnections = true;\n        edge.toCluster = e.w;\n      }\n      log.warn(\"Fix Replacing with XXX\", v, w, e.name);\n      graph.setEdge(v, w, edge, e.name);\n    }\n  });\n  log.warn(\"Adjusted Graph\", graphlibJson.write(graph));\n  extractor(graph, 0);\n  log.trace(clusterDb);\n}, \"adjustClustersAndEdges\");\nvar extractor = /* @__PURE__ */ __name((graph, depth) => {\n  log.warn(\"extractor - \", depth, graphlibJson.write(graph), graph.children(\"D\"));\n  if (depth > 10) {\n    log.error(\"Bailing out\");\n    return;\n  }\n  let nodes = graph.nodes();\n  let hasChildren = false;\n  for (const node of nodes) {\n    const children = graph.children(node);\n    hasChildren = hasChildren || children.length > 0;\n  }\n  if (!hasChildren) {\n    log.debug(\"Done, no node has children\", graph.nodes());\n    return;\n  }\n  log.debug(\"Nodes = \", nodes, depth);\n  for (const node of nodes) {\n    log.debug(\n      \"Extracting node\",\n      node,\n      clusterDb,\n      clusterDb.has(node) && !clusterDb.get(node).externalConnections,\n      !graph.parent(node),\n      graph.node(node),\n      graph.children(\"D\"),\n      \" Depth \",\n      depth\n    );\n    if (!clusterDb.has(node)) {\n      log.debug(\"Not a cluster\", node, depth);\n    } else if (!clusterDb.get(node).externalConnections && graph.children(node) && graph.children(node).length > 0) {\n      log.warn(\n        \"Cluster without external connections, without a parent and with children\",\n        node,\n        depth\n      );\n      const graphSettings = graph.graph();\n      let dir = graphSettings.rankdir === \"TB\" ? \"LR\" : \"TB\";\n      if (clusterDb.get(node)?.clusterData?.dir) {\n        dir = clusterDb.get(node).clusterData.dir;\n        log.warn(\"Fixing dir\", clusterDb.get(node).clusterData.dir, dir);\n      }\n      const clusterGraph = new graphlib.Graph({\n        multigraph: true,\n        compound: true\n      }).setGraph({\n        rankdir: dir,\n        nodesep: 50,\n        ranksep: 50,\n        marginx: 8,\n        marginy: 8\n      }).setDefaultEdgeLabel(function() {\n        return {};\n      });\n      log.warn(\"Old graph before copy\", graphlibJson.write(graph));\n      copy(node, graph, clusterGraph, node);\n      graph.setNode(node, {\n        clusterNode: true,\n        id: node,\n        clusterData: clusterDb.get(node).clusterData,\n        label: clusterDb.get(node).label,\n        graph: clusterGraph\n      });\n      log.warn(\"New graph after copy node: (\", node, \")\", graphlibJson.write(clusterGraph));\n      log.debug(\"Old graph after copy\", graphlibJson.write(graph));\n    } else {\n      log.warn(\n        \"Cluster ** \",\n        node,\n        \" **not meeting the criteria !externalConnections:\",\n        !clusterDb.get(node).externalConnections,\n        \" no parent: \",\n        !graph.parent(node),\n        \" children \",\n        graph.children(node) && graph.children(node).length > 0,\n        graph.children(\"D\"),\n        depth\n      );\n      log.debug(clusterDb);\n    }\n  }\n  nodes = graph.nodes();\n  log.warn(\"New list of nodes\", nodes);\n  for (const node of nodes) {\n    const data = graph.node(node);\n    log.warn(\" Now next level\", node, data);\n    if (data?.clusterNode) {\n      extractor(data.graph, depth + 1);\n    }\n  }\n}, \"extractor\");\nvar sorter = /* @__PURE__ */ __name((graph, nodes) => {\n  if (nodes.length === 0) {\n    return [];\n  }\n  let result = Object.assign([], nodes);\n  nodes.forEach((node) => {\n    const children = graph.children(node);\n    const sorted = sorter(graph, children);\n    result = [...result, ...sorted];\n  });\n  return result;\n}, \"sorter\");\nvar sortNodesByHierarchy = /* @__PURE__ */ __name((graph) => sorter(graph, graph.children()), \"sortNodesByHierarchy\");\n\n// src/rendering-util/layout-algorithms/dagre/index.js\nvar recursiveRender = /* @__PURE__ */ __name(async (_elem, graph, diagramType, id, parentCluster, siteConfig) => {\n  log.warn(\"Graph in recursive render:XAX\", graphlibJson2.write(graph), parentCluster);\n  const dir = graph.graph().rankdir;\n  log.trace(\"Dir in recursive render - dir:\", dir);\n  const elem = _elem.insert(\"g\").attr(\"class\", \"root\");\n  if (!graph.nodes()) {\n    log.info(\"No nodes found for\", graph);\n  } else {\n    log.info(\"Recursive render XXX\", graph.nodes());\n  }\n  if (graph.edges().length > 0) {\n    log.info(\"Recursive edges\", graph.edge(graph.edges()[0]));\n  }\n  const clusters = elem.insert(\"g\").attr(\"class\", \"clusters\");\n  const edgePaths = elem.insert(\"g\").attr(\"class\", \"edgePaths\");\n  const edgeLabels = elem.insert(\"g\").attr(\"class\", \"edgeLabels\");\n  const nodes = elem.insert(\"g\").attr(\"class\", \"nodes\");\n  await Promise.all(\n    graph.nodes().map(async function(v) {\n      const node = graph.node(v);\n      if (parentCluster !== void 0) {\n        const data = JSON.parse(JSON.stringify(parentCluster.clusterData));\n        log.trace(\n          \"Setting data for parent cluster XXX\\n Node.id = \",\n          v,\n          \"\\n data=\",\n          data.height,\n          \"\\nParent cluster\",\n          parentCluster.height\n        );\n        graph.setNode(parentCluster.id, data);\n        if (!graph.parent(v)) {\n          log.trace(\"Setting parent\", v, parentCluster.id);\n          graph.setParent(v, parentCluster.id, data);\n        }\n      }\n      log.info(\"(Insert) Node XXX\" + v + \": \" + JSON.stringify(graph.node(v)));\n      if (node?.clusterNode) {\n        log.info(\"Cluster identified XBX\", v, node.width, graph.node(v));\n        const { ranksep, nodesep } = graph.graph();\n        node.graph.setGraph({\n          ...node.graph.graph(),\n          ranksep: ranksep + 25,\n          nodesep\n        });\n        const o = await recursiveRender(\n          nodes,\n          node.graph,\n          diagramType,\n          id,\n          graph.node(v),\n          siteConfig\n        );\n        const newEl = o.elem;\n        updateNodeBounds(node, newEl);\n        node.diff = o.diff || 0;\n        log.info(\n          \"New compound node after recursive render XAX\",\n          v,\n          \"width\",\n          // node,\n          node.width,\n          \"height\",\n          node.height\n          // node.x,\n          // node.y\n        );\n        setNodeElem(newEl, node);\n      } else {\n        if (graph.children(v).length > 0) {\n          log.trace(\n            \"Cluster - the non recursive path XBX\",\n            v,\n            node.id,\n            node,\n            node.width,\n            \"Graph:\",\n            graph\n          );\n          log.trace(findNonClusterChild(node.id, graph));\n          clusterDb.set(node.id, { id: findNonClusterChild(node.id, graph), node });\n        } else {\n          log.trace(\"Node - the non recursive path XAX\", v, nodes, graph.node(v), dir);\n          await insertNode(nodes, graph.node(v), { config: siteConfig, dir });\n        }\n      }\n    })\n  );\n  const processEdges = /* @__PURE__ */ __name(async () => {\n    const edgePromises = graph.edges().map(async function(e) {\n      const edge = graph.edge(e.v, e.w, e.name);\n      log.info(\"Edge \" + e.v + \" -> \" + e.w + \": \" + JSON.stringify(e));\n      log.info(\"Edge \" + e.v + \" -> \" + e.w + \": \", e, \" \", JSON.stringify(graph.edge(e)));\n      log.info(\n        \"Fix\",\n        clusterDb,\n        \"ids:\",\n        e.v,\n        e.w,\n        \"Translating: \",\n        clusterDb.get(e.v),\n        clusterDb.get(e.w)\n      );\n      await insertEdgeLabel(edgeLabels, edge);\n    });\n    await Promise.all(edgePromises);\n  }, \"processEdges\");\n  await processEdges();\n  log.info(\"Graph before layout:\", JSON.stringify(graphlibJson2.write(graph)));\n  log.info(\"############################################# XXX\");\n  log.info(\"###                Layout                 ### XXX\");\n  log.info(\"############################################# XXX\");\n  dagreLayout(graph);\n  log.info(\"Graph after layout:\", JSON.stringify(graphlibJson2.write(graph)));\n  let diff = 0;\n  let { subGraphTitleTotalMargin } = getSubGraphTitleMargins(siteConfig);\n  await Promise.all(\n    sortNodesByHierarchy(graph).map(async function(v) {\n      const node = graph.node(v);\n      log.info(\n        \"Position XBX => \" + v + \": (\" + node.x,\n        \",\" + node.y,\n        \") width: \",\n        node.width,\n        \" height: \",\n        node.height\n      );\n      if (node?.clusterNode) {\n        node.y += subGraphTitleTotalMargin;\n        log.info(\n          \"A tainted cluster node XBX1\",\n          v,\n          node.id,\n          node.width,\n          node.height,\n          node.x,\n          node.y,\n          graph.parent(v)\n        );\n        clusterDb.get(node.id).node = node;\n        positionNode(node);\n      } else {\n        if (graph.children(v).length > 0) {\n          log.info(\n            \"A pure cluster node XBX1\",\n            v,\n            node.id,\n            node.x,\n            node.y,\n            node.width,\n            node.height,\n            graph.parent(v)\n          );\n          node.height += subGraphTitleTotalMargin;\n          graph.node(node.parentId);\n          const halfPadding = node?.padding / 2 || 0;\n          const labelHeight = node?.labelBBox?.height || 0;\n          const offsetY = labelHeight - halfPadding || 0;\n          log.debug(\"OffsetY\", offsetY, \"labelHeight\", labelHeight, \"halfPadding\", halfPadding);\n          await insertCluster(clusters, node);\n          clusterDb.get(node.id).node = node;\n        } else {\n          const parent = graph.node(node.parentId);\n          node.y += subGraphTitleTotalMargin / 2;\n          log.info(\n            \"A regular node XBX1 - using the padding\",\n            node.id,\n            \"parent\",\n            node.parentId,\n            node.width,\n            node.height,\n            node.x,\n            node.y,\n            \"offsetY\",\n            node.offsetY,\n            \"parent\",\n            parent,\n            parent?.offsetY,\n            node\n          );\n          positionNode(node);\n        }\n      }\n    })\n  );\n  graph.edges().forEach(function(e) {\n    const edge = graph.edge(e);\n    log.info(\"Edge \" + e.v + \" -> \" + e.w + \": \" + JSON.stringify(edge), edge);\n    edge.points.forEach((point) => point.y += subGraphTitleTotalMargin / 2);\n    const startNode = graph.node(e.v);\n    var endNode = graph.node(e.w);\n    const paths = insertEdge(edgePaths, edge, clusterDb, diagramType, startNode, endNode, id);\n    positionEdgeLabel(edge, paths);\n  });\n  graph.nodes().forEach(function(v) {\n    const n = graph.node(v);\n    log.info(v, n.type, n.diff);\n    if (n.isGroup) {\n      diff = n.diff;\n    }\n  });\n  log.warn(\"Returning from recursive render XAX\", elem, diff);\n  return { elem, diff };\n}, \"recursiveRender\");\nvar render = /* @__PURE__ */ __name(async (data4Layout, svg) => {\n  const graph = new graphlib2.Graph({\n    multigraph: true,\n    compound: true\n  }).setGraph({\n    rankdir: data4Layout.direction,\n    nodesep: data4Layout.config?.nodeSpacing || data4Layout.config?.flowchart?.nodeSpacing || data4Layout.nodeSpacing,\n    ranksep: data4Layout.config?.rankSpacing || data4Layout.config?.flowchart?.rankSpacing || data4Layout.rankSpacing,\n    marginx: 8,\n    marginy: 8\n  }).setDefaultEdgeLabel(function() {\n    return {};\n  });\n  const element = svg.select(\"g\");\n  markers_default(element, data4Layout.markers, data4Layout.type, data4Layout.diagramId);\n  clear3();\n  clear2();\n  clear();\n  clear4();\n  data4Layout.nodes.forEach((node) => {\n    graph.setNode(node.id, { ...node });\n    if (node.parentId) {\n      graph.setParent(node.id, node.parentId);\n    }\n  });\n  log.debug(\"Edges:\", data4Layout.edges);\n  data4Layout.edges.forEach((edge) => {\n    if (edge.start === edge.end) {\n      const nodeId = edge.start;\n      const specialId1 = nodeId + \"---\" + nodeId + \"---1\";\n      const specialId2 = nodeId + \"---\" + nodeId + \"---2\";\n      const node = graph.node(nodeId);\n      graph.setNode(specialId1, {\n        domId: specialId1,\n        id: specialId1,\n        parentId: node.parentId,\n        labelStyle: \"\",\n        label: \"\",\n        padding: 0,\n        shape: \"labelRect\",\n        // shape: 'rect',\n        style: \"\",\n        width: 10,\n        height: 10\n      });\n      graph.setParent(specialId1, node.parentId);\n      graph.setNode(specialId2, {\n        domId: specialId2,\n        id: specialId2,\n        parentId: node.parentId,\n        labelStyle: \"\",\n        padding: 0,\n        // shape: 'rect',\n        shape: \"labelRect\",\n        label: \"\",\n        style: \"\",\n        width: 10,\n        height: 10\n      });\n      graph.setParent(specialId2, node.parentId);\n      const edge1 = structuredClone(edge);\n      const edgeMid = structuredClone(edge);\n      const edge2 = structuredClone(edge);\n      edge1.label = \"\";\n      edge1.arrowTypeEnd = \"none\";\n      edge1.id = nodeId + \"-cyclic-special-1\";\n      edgeMid.arrowTypeStart = \"none\";\n      edgeMid.arrowTypeEnd = \"none\";\n      edgeMid.id = nodeId + \"-cyclic-special-mid\";\n      edge2.label = \"\";\n      if (node.isGroup) {\n        edge1.fromCluster = nodeId;\n        edge2.toCluster = nodeId;\n      }\n      edge2.id = nodeId + \"-cyclic-special-2\";\n      edge2.arrowTypeStart = \"none\";\n      graph.setEdge(nodeId, specialId1, edge1, nodeId + \"-cyclic-special-0\");\n      graph.setEdge(specialId1, specialId2, edgeMid, nodeId + \"-cyclic-special-1\");\n      graph.setEdge(specialId2, nodeId, edge2, nodeId + \"-cyc<lic-special-2\");\n    } else {\n      graph.setEdge(edge.start, edge.end, { ...edge }, edge.id);\n    }\n  });\n  log.warn(\"Graph at first:\", JSON.stringify(graphlibJson2.write(graph)));\n  adjustClustersAndEdges(graph);\n  log.warn(\"Graph after XAX:\", JSON.stringify(graphlibJson2.write(graph)));\n  const siteConfig = getConfig();\n  await recursiveRender(\n    element,\n    graph,\n    data4Layout.type,\n    data4Layout.diagramId,\n    void 0,\n    siteConfig\n  );\n}, \"render\");\nexport {\n  render\n};\n"], "names": ["write", "g", "json", "writeNodes", "writeEdges", "_.isUndefined", "_.clone", "_.map", "v", "nodeValue", "parent", "node", "e", "edgeValue", "edge", "clusterDb", "descendants", "parents", "clear4", "__name", "isDescendant", "id", "ancestorId", "ancestorDescendants", "log", "edgeInCluster", "clusterId", "clusterDescendants", "copy", "graph", "newGraph", "rootId", "nodes", "data", "edges", "data2", "extractDescendants", "children", "res", "child", "find<PERSON><PERSON><PERSON><PERSON>dges", "id1", "id2", "edges1", "edges2", "edges1Prim", "edges2Prim", "edgeIn1", "findNonClusterChild", "reserve", "_id", "commonEdges", "getAnchorId", "adjustClustersAndEdges", "depth", "d1", "d2", "nonClusterChild", "w", "graphlibJson.write", "extractor", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dir", "_b", "_a", "clusterGraph", "graphlib.Graph", "sorter", "result", "sorted", "sortNodesByHierarchy", "recursiveRender", "_elem", "diagramType", "parentCluster", "siteConfig", "graphlibJson2.write", "elem", "clusters", "edgePaths", "edgeLabels", "ranksep", "nodesep", "o", "newEl", "updateNodeBounds", "setNodeElem", "insertNode", "edgePromises", "insertEdgeLabel", "dagreLayout", "diff", "subGraphTitleTotalMargin", "getSubGraphTitleMargins", "positionNode", "halfPadding", "labelHeight", "offsetY", "insertCluster", "point", "startNode", "endNode", "paths", "insertEdge", "positionEdgeLabel", "n", "render", "data4Layout", "svg", "graphlib2.Graph", "_c", "_d", "_f", "_e", "element", "markers_default", "clear3", "clear2", "clear", "nodeId", "specialId1", "specialId2", "edge1", "edgeMid", "edge2", "getConfig"], "mappings": "2TAKA,SAASA,EAAMC,EAAG,CAChB,IAAIC,EAAO,CACT,QAAS,CACP,SAAUD,EAAE,WAAY,EACxB,WAAYA,EAAE,aAAc,EAC5B,SAAUA,EAAE,WAAY,CACzB,EACD,MAAOE,GAAWF,CAAC,EACnB,MAAOG,GAAWH,CAAC,CACpB,EACD,OAAKI,EAAcJ,EAAE,MAAO,CAAA,IAC1BC,EAAK,MAAQI,EAAQL,EAAE,MAAK,CAAE,GAEzBC,CACT,CAEA,SAASC,GAAWF,EAAG,CACrB,OAAOM,EAAMN,EAAE,MAAO,EAAE,SAAUO,EAAG,CACnC,IAAIC,EAAYR,EAAE,KAAKO,CAAC,EACpBE,EAAST,EAAE,OAAOO,CAAC,EACnBG,EAAO,CAAE,EAAGH,CAAG,EACnB,OAAKH,EAAcI,CAAS,IAC1BE,EAAK,MAAQF,GAEVJ,EAAcK,CAAM,IACvBC,EAAK,OAASD,GAETC,CACX,CAAG,CACH,CAEA,SAASP,GAAWH,EAAG,CACrB,OAAOM,EAAMN,EAAE,MAAO,EAAE,SAAUW,EAAG,CACnC,IAAIC,EAAYZ,EAAE,KAAKW,CAAC,EACpBE,EAAO,CAAE,EAAGF,EAAE,EAAG,EAAGA,EAAE,CAAG,EAC7B,OAAKP,EAAcO,EAAE,IAAI,IACvBE,EAAK,KAAOF,EAAE,MAEXP,EAAcQ,CAAS,IAC1BC,EAAK,MAAQD,GAERC,CACX,CAAG,CACH,CCXA,IAAIC,EAA4B,IAAI,IAChCC,EAA8B,IAAI,IAClCC,EAA0B,IAAI,IAC9BC,GAAyBC,EAAO,IAAM,CACxCH,EAAY,MAAO,EACnBC,EAAQ,MAAO,EACfF,EAAU,MAAO,CACnB,EAAG,OAAO,EACNK,EAA+BD,EAAO,CAACE,EAAIC,IAAe,CAC5D,MAAMC,EAAsBP,EAAY,IAAIM,CAAU,GAAK,CAAE,EAC7D,OAAAE,EAAI,MAAM,kBAAmBF,EAAY,IAAKD,EAAI,MAAOE,EAAoB,SAASF,CAAE,CAAC,EAClFE,EAAoB,SAASF,CAAE,CACxC,EAAG,cAAc,EACbI,GAAgCN,EAAO,CAACL,EAAMY,IAAc,CAC9D,MAAMC,EAAqBX,EAAY,IAAIU,CAAS,GAAK,CAAE,EAG3D,OAFAF,EAAI,KAAK,kBAAmBE,EAAW,OAAQC,CAAkB,EACjEH,EAAI,KAAK,WAAYV,CAAI,EACrBA,EAAK,IAAMY,GAAaZ,EAAK,IAAMY,EAC9B,GAEJC,EAIEA,EAAmB,SAASb,EAAK,CAAC,GAAKM,EAAaN,EAAK,EAAGY,CAAS,GAAKN,EAAaN,EAAK,EAAGY,CAAS,GAAKC,EAAmB,SAASb,EAAK,CAAC,GAHpJU,EAAI,MAAM,SAAUE,EAAW,qBAAqB,EAC7C,GAGX,EAAG,eAAe,EACdE,EAAuBT,EAAO,CAACO,EAAWG,EAAOC,EAAUC,IAAW,CACxEP,EAAI,KACF,uBACAE,EACA,OACAK,EACA,OACAF,EAAM,KAAKH,CAAS,EACpBK,CACD,EACD,MAAMC,EAAQH,EAAM,SAASH,CAAS,GAAK,CAAE,EACzCA,IAAcK,GAChBC,EAAM,KAAKN,CAAS,EAEtBF,EAAI,KAAK,4BAA6BE,EAAW,QAASM,CAAK,EAC/DA,EAAM,QAASrB,GAAS,CACtB,GAAIkB,EAAM,SAASlB,CAAI,EAAE,OAAS,EAChCiB,EAAKjB,EAAMkB,EAAOC,EAAUC,CAAM,MAC7B,CACL,MAAME,EAAOJ,EAAM,KAAKlB,CAAI,EAC5Ba,EAAI,KAAK,MAAOb,EAAM,OAAQoB,EAAQ,gBAAiBL,CAAS,EAChEI,EAAS,QAAQnB,EAAMsB,CAAI,EACvBF,IAAWF,EAAM,OAAOlB,CAAI,IAC9Ba,EAAI,KAAK,iBAAkBb,EAAMkB,EAAM,OAAOlB,CAAI,CAAC,EACnDmB,EAAS,UAAUnB,EAAMkB,EAAM,OAAOlB,CAAI,CAAC,GAEzCe,IAAcK,GAAUpB,IAASe,GACnCF,EAAI,MAAM,iBAAkBb,EAAMe,CAAS,EAC3CI,EAAS,UAAUnB,EAAMe,CAAS,IAElCF,EAAI,KAAK,WAAYE,EAAW,OAAQK,EAAQ,OAAQF,EAAM,KAAKH,CAAS,EAAGK,CAAM,EACrFP,EAAI,MACF,+BACAb,EACA,mBACAe,IAAcK,EACd,mBACApB,IAASe,CACV,GAEH,MAAMQ,EAAQL,EAAM,MAAMlB,CAAI,EAC9Ba,EAAI,MAAM,gBAAiBU,CAAK,EAChCA,EAAM,QAASpB,GAAS,CACtBU,EAAI,KAAK,OAAQV,CAAI,EACrB,MAAMqB,EAAQN,EAAM,KAAKf,EAAK,EAAGA,EAAK,EAAGA,EAAK,IAAI,EAClDU,EAAI,KAAK,YAAaW,EAAOJ,CAAM,EACnC,GAAI,CACEN,GAAcX,EAAMiB,CAAM,GAC5BP,EAAI,KAAK,cAAeV,EAAK,EAAGA,EAAK,EAAGqB,EAAOrB,EAAK,IAAI,EACxDgB,EAAS,QAAQhB,EAAK,EAAGA,EAAK,EAAGqB,EAAOrB,EAAK,IAAI,EACjDU,EAAI,KAAK,kBAAmBM,EAAS,MAAO,EAAEA,EAAS,KAAKA,EAAS,QAAQ,CAAC,CAAC,CAAC,GAEhFN,EAAI,KACF,yBACAV,EAAK,EACL,MACAA,EAAK,EACL,YACAiB,EACA,cACAL,CACD,CAEJ,OAAQd,EAAG,CACVY,EAAI,MAAMZ,CAAC,CACrB,CACA,CAAO,CACP,CACIY,EAAI,MAAM,gBAAiBb,CAAI,EAC/BkB,EAAM,WAAWlB,CAAI,CACzB,CAAG,CACH,EAAG,MAAM,EACLyB,EAAqCjB,EAAO,CAACE,EAAIQ,IAAU,CAC7D,MAAMQ,EAAWR,EAAM,SAASR,CAAE,EAClC,IAAIiB,EAAM,CAAC,GAAGD,CAAQ,EACtB,UAAWE,KAASF,EAClBpB,EAAQ,IAAIsB,EAAOlB,CAAE,EACrBiB,EAAM,CAAC,GAAGA,EAAK,GAAGF,EAAmBG,EAAOV,CAAK,CAAC,EAEpD,OAAOS,CACT,EAAG,oBAAoB,EACnBE,GAAkCrB,EAAO,CAACU,EAAOY,EAAKC,IAAQ,CAChE,MAAMC,EAASd,EAAM,MAAO,EAAC,OAAQf,GAASA,EAAK,IAAM2B,GAAO3B,EAAK,IAAM2B,CAAG,EACxEG,EAASf,EAAM,MAAO,EAAC,OAAQf,GAASA,EAAK,IAAM4B,GAAO5B,EAAK,IAAM4B,CAAG,EACxEG,EAAaF,EAAO,IAAK7B,IACtB,CAAE,EAAGA,EAAK,IAAM2B,EAAMC,EAAM5B,EAAK,EAAG,EAAGA,EAAK,IAAM2B,EAAMA,EAAM3B,EAAK,CAAG,EAC9E,EACKgC,EAAaF,EAAO,IAAK9B,IACtB,CAAE,EAAGA,EAAK,EAAG,EAAGA,EAAK,CAAG,EAChC,EAID,OAHe+B,EAAW,OAAQE,GACzBD,EAAW,KAAMhC,GAASiC,EAAQ,IAAMjC,EAAK,GAAKiC,EAAQ,IAAMjC,EAAK,CAAC,CAC9E,CAEH,EAAG,iBAAiB,EAChBkC,EAAsC7B,EAAO,CAACE,EAAIQ,EAAOH,IAAc,CACzE,MAAMW,EAAWR,EAAM,SAASR,CAAE,EAElC,GADAG,EAAI,MAAM,4BAA6BH,EAAIgB,CAAQ,EAC/CA,EAAS,OAAS,EACpB,OAAOhB,EAET,IAAI4B,EACJ,UAAWV,KAASF,EAAU,CAC5B,MAAMa,EAAMF,EAAoBT,EAAOV,EAAOH,CAAS,EACjDyB,EAAcX,GAAgBX,EAAOH,EAAWwB,CAAG,EACzD,GAAIA,EACF,GAAIC,EAAY,OAAS,EACvBF,EAAUC,MAEV,QAAOA,CAGf,CACE,OAAOD,CACT,EAAG,qBAAqB,EACpBG,EAA8BjC,EAAQE,GACpC,CAACN,EAAU,IAAIM,CAAE,GAGjB,CAACN,EAAU,IAAIM,CAAE,EAAE,oBACdA,EAELN,EAAU,IAAIM,CAAE,EACXN,EAAU,IAAIM,CAAE,EAAE,GAEpBA,EACN,aAAa,EACZgC,GAAyClC,EAAO,CAACU,EAAOyB,IAAU,CACpE,GAAI,CAACzB,GAASyB,EAAQ,GAAI,CACxB9B,EAAI,MAAM,uBAAuB,EACjC,MACJ,MACIA,EAAI,MAAM,mBAAmB,EAE/BK,EAAM,MAAK,EAAG,QAAQ,SAASR,EAAI,CAChBQ,EAAM,SAASR,CAAE,EACrB,OAAS,IACpBG,EAAI,KACF,qBACAH,EACA,6BACA2B,EAAoB3B,EAAIQ,EAAOR,CAAE,CAClC,EACDL,EAAY,IAAIK,EAAIe,EAAmBf,EAAIQ,CAAK,CAAC,EACjDd,EAAU,IAAIM,EAAI,CAAE,GAAI2B,EAAoB3B,EAAIQ,EAAOR,CAAE,EAAG,YAAaQ,EAAM,KAAKR,CAAE,CAAC,CAAE,EAE/F,CAAG,EACDQ,EAAM,MAAK,EAAG,QAAQ,SAASR,EAAI,CACjC,MAAMgB,EAAWR,EAAM,SAASR,CAAE,EAC5Ba,EAAQL,EAAM,MAAO,EACvBQ,EAAS,OAAS,GACpBb,EAAI,MAAM,qBAAsBH,EAAIL,CAAW,EAC/CkB,EAAM,QAASpB,GAAS,CACtB,MAAMyC,EAAKnC,EAAaN,EAAK,EAAGO,CAAE,EAC5BmC,EAAKpC,EAAaN,EAAK,EAAGO,CAAE,EAC9BkC,EAAKC,IACPhC,EAAI,KAAK,SAAUV,EAAM,mBAAoBO,CAAE,EAC/CG,EAAI,KAAK,sBAAuBH,EAAI,KAAML,EAAY,IAAIK,CAAE,CAAC,EAC7DN,EAAU,IAAIM,CAAE,EAAE,oBAAsB,GAElD,CAAO,GAEDG,EAAI,MAAM,iBAAkBH,EAAIL,CAAW,CAEjD,CAAG,EACD,QAASK,KAAMN,EAAU,OAAQ,CAC/B,MAAM0C,EAAkB1C,EAAU,IAAIM,CAAE,EAAE,GACpCX,EAASmB,EAAM,OAAO4B,CAAe,EACvC/C,IAAWW,GAAMN,EAAU,IAAIL,CAAM,GAAK,CAACK,EAAU,IAAIL,CAAM,EAAE,sBACnEK,EAAU,IAAIM,CAAE,EAAE,GAAKX,EAE7B,CACEmB,EAAM,MAAK,EAAG,QAAQ,SAASjB,EAAG,CAChC,MAAME,EAAOe,EAAM,KAAKjB,CAAC,EACzBY,EAAI,KAAK,QAAUZ,EAAE,EAAI,OAASA,EAAE,EAAI,KAAO,KAAK,UAAUA,CAAC,CAAC,EAChEY,EAAI,KAAK,QAAUZ,EAAE,EAAI,OAASA,EAAE,EAAI,KAAO,KAAK,UAAUiB,EAAM,KAAKjB,CAAC,CAAC,CAAC,EAC5E,IAAIJ,EAAII,EAAE,EACN8C,EAAI9C,EAAE,EAYV,GAXAY,EAAI,KACF,UACAT,EACA,OACAH,EAAE,EACFA,EAAE,EACF,gBACAG,EAAU,IAAIH,EAAE,CAAC,EACjB,QACAG,EAAU,IAAIH,EAAE,CAAC,CAClB,EACGG,EAAU,IAAIH,EAAE,CAAC,GAAKG,EAAU,IAAIH,EAAE,CAAC,EAAG,CAK5C,GAJAY,EAAI,KAAK,mCAAoCZ,EAAE,EAAGA,EAAE,EAAGA,EAAE,IAAI,EAC7DJ,EAAI4C,EAAYxC,EAAE,CAAC,EACnB8C,EAAIN,EAAYxC,EAAE,CAAC,EACnBiB,EAAM,WAAWjB,EAAE,EAAGA,EAAE,EAAGA,EAAE,IAAI,EAC7BJ,IAAMI,EAAE,EAAG,CACb,MAAMF,EAASmB,EAAM,OAAOrB,CAAC,EAC7BO,EAAU,IAAIL,CAAM,EAAE,oBAAsB,GAC5CI,EAAK,YAAcF,EAAE,CAC7B,CACM,GAAI8C,IAAM9C,EAAE,EAAG,CACb,MAAMF,EAASmB,EAAM,OAAO6B,CAAC,EAC7B3C,EAAU,IAAIL,CAAM,EAAE,oBAAsB,GAC5CI,EAAK,UAAYF,EAAE,CAC3B,CACMY,EAAI,KAAK,yBAA0BhB,EAAGkD,EAAG9C,EAAE,IAAI,EAC/CiB,EAAM,QAAQrB,EAAGkD,EAAG5C,EAAMF,EAAE,IAAI,CACtC,CACA,CAAG,EACDY,EAAI,KAAK,iBAAkBmC,EAAmB9B,CAAK,CAAC,EACpD+B,EAAU/B,EAAO,CAAC,EAClBL,EAAI,MAAMT,CAAS,CACrB,EAAG,wBAAwB,EACvB6C,EAA4BzC,EAAO,CAACU,EAAOyB,IAAU,SAEvD,GADA9B,EAAI,KAAK,eAAgB8B,EAAOK,EAAmB9B,CAAK,EAAGA,EAAM,SAAS,GAAG,CAAC,EAC1EyB,EAAQ,GAAI,CACd9B,EAAI,MAAM,aAAa,EACvB,MACJ,CACE,IAAIQ,EAAQH,EAAM,MAAO,EACrBgC,EAAc,GAClB,UAAWlD,KAAQqB,EAAO,CACxB,MAAMK,EAAWR,EAAM,SAASlB,CAAI,EACpCkD,EAAcA,GAAexB,EAAS,OAAS,CACnD,CACE,GAAI,CAACwB,EAAa,CAChBrC,EAAI,MAAM,6BAA8BK,EAAM,MAAK,CAAE,EACrD,MACJ,CACEL,EAAI,MAAM,WAAYQ,EAAOsB,CAAK,EAClC,UAAW3C,KAAQqB,EAYjB,GAXAR,EAAI,MACF,kBACAb,EACAI,EACAA,EAAU,IAAIJ,CAAI,GAAK,CAACI,EAAU,IAAIJ,CAAI,EAAE,oBAC5C,CAACkB,EAAM,OAAOlB,CAAI,EAClBkB,EAAM,KAAKlB,CAAI,EACfkB,EAAM,SAAS,GAAG,EAClB,UACAyB,CACD,EACG,CAACvC,EAAU,IAAIJ,CAAI,EACrBa,EAAI,MAAM,gBAAiBb,EAAM2C,CAAK,UAC7B,CAACvC,EAAU,IAAIJ,CAAI,EAAE,qBAAuBkB,EAAM,SAASlB,CAAI,GAAKkB,EAAM,SAASlB,CAAI,EAAE,OAAS,EAAG,CAC9Ga,EAAI,KACF,2EACAb,EACA2C,CACD,EAED,IAAIQ,EADkBjC,EAAM,MAAO,EACX,UAAY,KAAO,KAAO,MAC9CkC,GAAAC,EAAAjD,EAAU,IAAIJ,CAAI,IAAlB,YAAAqD,EAAqB,cAArB,MAAAD,EAAkC,MACpCD,EAAM/C,EAAU,IAAIJ,CAAI,EAAE,YAAY,IACtCa,EAAI,KAAK,aAAcT,EAAU,IAAIJ,CAAI,EAAE,YAAY,IAAKmD,CAAG,GAEjE,MAAMG,EAAe,IAAIC,EAAe,CACtC,WAAY,GACZ,SAAU,EACX,CAAA,EAAE,SAAS,CACV,QAASJ,EACT,QAAS,GACT,QAAS,GACT,QAAS,EACT,QAAS,CACjB,CAAO,EAAE,oBAAoB,UAAW,CAChC,MAAO,CAAE,CACjB,CAAO,EACDtC,EAAI,KAAK,wBAAyBmC,EAAmB9B,CAAK,CAAC,EAC3DD,EAAKjB,EAAMkB,EAAOoC,EAActD,CAAI,EACpCkB,EAAM,QAAQlB,EAAM,CAClB,YAAa,GACb,GAAIA,EACJ,YAAaI,EAAU,IAAIJ,CAAI,EAAE,YACjC,MAAOI,EAAU,IAAIJ,CAAI,EAAE,MAC3B,MAAOsD,CACf,CAAO,EACDzC,EAAI,KAAK,+BAAgCb,EAAM,IAAKgD,EAAmBM,CAAY,CAAC,EACpFzC,EAAI,MAAM,uBAAwBmC,EAAmB9B,CAAK,CAAC,CACjE,MACML,EAAI,KACF,cACAb,EACA,oDACA,CAACI,EAAU,IAAIJ,CAAI,EAAE,oBACrB,eACA,CAACkB,EAAM,OAAOlB,CAAI,EAClB,aACAkB,EAAM,SAASlB,CAAI,GAAKkB,EAAM,SAASlB,CAAI,EAAE,OAAS,EACtDkB,EAAM,SAAS,GAAG,EAClByB,CACD,EACD9B,EAAI,MAAMT,CAAS,EAGvBiB,EAAQH,EAAM,MAAO,EACrBL,EAAI,KAAK,oBAAqBQ,CAAK,EACnC,UAAWrB,KAAQqB,EAAO,CACxB,MAAMC,EAAOJ,EAAM,KAAKlB,CAAI,EAC5Ba,EAAI,KAAK,kBAAmBb,EAAMsB,CAAI,EAClCA,GAAA,MAAAA,EAAM,aACR2B,EAAU3B,EAAK,MAAOqB,EAAQ,CAAC,CAErC,CACA,EAAG,WAAW,EACVa,EAAyBhD,EAAO,CAACU,EAAOG,IAAU,CACpD,GAAIA,EAAM,SAAW,EACnB,MAAO,CAAE,EAEX,IAAIoC,EAAS,OAAO,OAAO,CAAA,EAAIpC,CAAK,EACpC,OAAAA,EAAM,QAASrB,GAAS,CACtB,MAAM0B,EAAWR,EAAM,SAASlB,CAAI,EAC9B0D,EAASF,EAAOtC,EAAOQ,CAAQ,EACrC+B,EAAS,CAAC,GAAGA,EAAQ,GAAGC,CAAM,CAClC,CAAG,EACMD,CACT,EAAG,QAAQ,EACPE,GAAuCnD,EAAQU,GAAUsC,EAAOtC,EAAOA,EAAM,UAAU,EAAG,sBAAsB,EAGhH0C,EAAkCpD,EAAO,MAAOqD,EAAO3C,EAAO4C,EAAapD,EAAIqD,EAAeC,IAAe,CAC/GnD,EAAI,KAAK,gCAAiCoD,EAAoB/C,CAAK,EAAG6C,CAAa,EACnF,MAAMZ,EAAMjC,EAAM,MAAK,EAAG,QAC1BL,EAAI,MAAM,iCAAkCsC,CAAG,EAC/C,MAAMe,EAAOL,EAAM,OAAO,GAAG,EAAE,KAAK,QAAS,MAAM,EAC9C3C,EAAM,QAGTL,EAAI,KAAK,uBAAwBK,EAAM,MAAK,CAAE,EAF9CL,EAAI,KAAK,qBAAsBK,CAAK,EAIlCA,EAAM,QAAQ,OAAS,GACzBL,EAAI,KAAK,kBAAmBK,EAAM,KAAKA,EAAM,MAAK,EAAG,CAAC,CAAC,CAAC,EAE1D,MAAMiD,EAAWD,EAAK,OAAO,GAAG,EAAE,KAAK,QAAS,UAAU,EACpDE,EAAYF,EAAK,OAAO,GAAG,EAAE,KAAK,QAAS,WAAW,EACtDG,EAAaH,EAAK,OAAO,GAAG,EAAE,KAAK,QAAS,YAAY,EACxD7C,EAAQ6C,EAAK,OAAO,GAAG,EAAE,KAAK,QAAS,OAAO,EACpD,MAAM,QAAQ,IACZhD,EAAM,MAAK,EAAG,IAAI,eAAerB,EAAG,CAClC,MAAMG,EAAOkB,EAAM,KAAKrB,CAAC,EACzB,GAAIkE,IAAkB,OAAQ,CAC5B,MAAMzC,EAAO,KAAK,MAAM,KAAK,UAAUyC,EAAc,WAAW,CAAC,EACjElD,EAAI,MACF;AAAA,aACAhB,EACA;AAAA,QACAyB,EAAK,OACL;AAAA,gBACAyC,EAAc,MACf,EACD7C,EAAM,QAAQ6C,EAAc,GAAIzC,CAAI,EAC/BJ,EAAM,OAAOrB,CAAC,IACjBgB,EAAI,MAAM,iBAAkBhB,EAAGkE,EAAc,EAAE,EAC/C7C,EAAM,UAAUrB,EAAGkE,EAAc,GAAIzC,CAAI,EAEnD,CAEM,GADAT,EAAI,KAAK,oBAAsBhB,EAAI,KAAO,KAAK,UAAUqB,EAAM,KAAKrB,CAAC,CAAC,CAAC,EACnEG,GAAA,MAAAA,EAAM,YAAa,CACrBa,EAAI,KAAK,yBAA0BhB,EAAGG,EAAK,MAAOkB,EAAM,KAAKrB,CAAC,CAAC,EAC/D,KAAM,CAAE,QAAAyE,EAAS,QAAAC,GAAYrD,EAAM,MAAO,EAC1ClB,EAAK,MAAM,SAAS,CAClB,GAAGA,EAAK,MAAM,MAAO,EACrB,QAASsE,EAAU,GACnB,QAAAC,CACV,CAAS,EACD,MAAMC,EAAI,MAAMZ,EACdvC,EACArB,EAAK,MACL8D,EACApD,EACAQ,EAAM,KAAKrB,CAAC,EACZmE,CACD,EACKS,EAAQD,EAAE,KAChBE,EAAiB1E,EAAMyE,CAAK,EAC5BzE,EAAK,KAAOwE,EAAE,MAAQ,EACtB3D,EAAI,KACF,+CACAhB,EACA,QAEAG,EAAK,MACL,SACAA,EAAK,MAGN,EACD2E,EAAYF,EAAOzE,CAAI,CAC/B,MACYkB,EAAM,SAASrB,CAAC,EAAE,OAAS,GAC7BgB,EAAI,MACF,uCACAhB,EACAG,EAAK,GACLA,EACAA,EAAK,MACL,SACAkB,CACD,EACDL,EAAI,MAAMwB,EAAoBrC,EAAK,GAAIkB,CAAK,CAAC,EAC7Cd,EAAU,IAAIJ,EAAK,GAAI,CAAE,GAAIqC,EAAoBrC,EAAK,GAAIkB,CAAK,EAAG,KAAAlB,CAAI,CAAE,IAExEa,EAAI,MAAM,oCAAqChB,EAAGwB,EAAOH,EAAM,KAAKrB,CAAC,EAAGsD,CAAG,EAC3E,MAAMyB,EAAWvD,EAAOH,EAAM,KAAKrB,CAAC,EAAG,CAAE,OAAQmE,EAAY,IAAAb,EAAK,EAGvE,CAAA,CACF,EAoBD,MAnBqC3C,EAAO,SAAY,CACtD,MAAMqE,EAAe3D,EAAM,MAAO,EAAC,IAAI,eAAejB,EAAG,CACvD,MAAME,EAAOe,EAAM,KAAKjB,EAAE,EAAGA,EAAE,EAAGA,EAAE,IAAI,EACxCY,EAAI,KAAK,QAAUZ,EAAE,EAAI,OAASA,EAAE,EAAI,KAAO,KAAK,UAAUA,CAAC,CAAC,EAChEY,EAAI,KAAK,QAAUZ,EAAE,EAAI,OAASA,EAAE,EAAI,KAAMA,EAAG,IAAK,KAAK,UAAUiB,EAAM,KAAKjB,CAAC,CAAC,CAAC,EACnFY,EAAI,KACF,MACAT,EACA,OACAH,EAAE,EACFA,EAAE,EACF,gBACAG,EAAU,IAAIH,EAAE,CAAC,EACjBG,EAAU,IAAIH,EAAE,CAAC,CAClB,EACD,MAAM6E,EAAgBT,EAAYlE,CAAI,CAC5C,CAAK,EACD,MAAM,QAAQ,IAAI0E,CAAY,CAC/B,EAAE,cAAc,EACG,EACpBhE,EAAI,KAAK,uBAAwB,KAAK,UAAUoD,EAAoB/C,CAAK,CAAC,CAAC,EAC3EL,EAAI,KAAK,mDAAmD,EAC5DA,EAAI,KAAK,mDAAmD,EAC5DA,EAAI,KAAK,mDAAmD,EAC5DkE,EAAY7D,CAAK,EACjBL,EAAI,KAAK,sBAAuB,KAAK,UAAUoD,EAAoB/C,CAAK,CAAC,CAAC,EAC1E,IAAI8D,EAAO,EACP,CAAE,yBAAAC,CAAwB,EAAKC,EAAwBlB,CAAU,EACrE,aAAM,QAAQ,IACZL,GAAqBzC,CAAK,EAAE,IAAI,eAAerB,EAAG,OAChD,MAAMG,EAAOkB,EAAM,KAAKrB,CAAC,EASzB,GARAgB,EAAI,KACF,mBAAqBhB,EAAI,MAAQG,EAAK,EACtC,IAAMA,EAAK,EACX,YACAA,EAAK,MACL,YACAA,EAAK,MACN,EACGA,GAAA,MAAAA,EAAM,YACRA,EAAK,GAAKiF,EACVpE,EAAI,KACF,8BACAhB,EACAG,EAAK,GACLA,EAAK,MACLA,EAAK,OACLA,EAAK,EACLA,EAAK,EACLkB,EAAM,OAAOrB,CAAC,CACf,EACDO,EAAU,IAAIJ,EAAK,EAAE,EAAE,KAAOA,EAC9BmF,EAAanF,CAAI,UAEbkB,EAAM,SAASrB,CAAC,EAAE,OAAS,EAAG,CAChCgB,EAAI,KACF,2BACAhB,EACAG,EAAK,GACLA,EAAK,EACLA,EAAK,EACLA,EAAK,MACLA,EAAK,OACLkB,EAAM,OAAOrB,CAAC,CACf,EACDG,EAAK,QAAUiF,EACf/D,EAAM,KAAKlB,EAAK,QAAQ,EACxB,MAAMoF,GAAcpF,GAAA,YAAAA,EAAM,SAAU,GAAK,EACnCqF,IAAchC,EAAArD,GAAA,YAAAA,EAAM,YAAN,YAAAqD,EAAiB,SAAU,EACzCiC,EAAUD,EAAcD,GAAe,EAC7CvE,EAAI,MAAM,UAAWyE,EAAS,cAAeD,EAAa,cAAeD,CAAW,EACpF,MAAMG,EAAcpB,EAAUnE,CAAI,EAClCI,EAAU,IAAIJ,EAAK,EAAE,EAAE,KAAOA,CACxC,KAAe,CACL,MAAMD,EAASmB,EAAM,KAAKlB,EAAK,QAAQ,EACvCA,EAAK,GAAKiF,EAA2B,EACrCpE,EAAI,KACF,0CACAb,EAAK,GACL,SACAA,EAAK,SACLA,EAAK,MACLA,EAAK,OACLA,EAAK,EACLA,EAAK,EACL,UACAA,EAAK,QACL,SACAD,EACAA,GAAA,YAAAA,EAAQ,QACRC,CACD,EACDmF,EAAanF,CAAI,CAC3B,CAEK,CAAA,CACF,EACDkB,EAAM,MAAK,EAAG,QAAQ,SAASjB,EAAG,CAChC,MAAME,EAAOe,EAAM,KAAKjB,CAAC,EACzBY,EAAI,KAAK,QAAUZ,EAAE,EAAI,OAASA,EAAE,EAAI,KAAO,KAAK,UAAUE,CAAI,EAAGA,CAAI,EACzEA,EAAK,OAAO,QAASqF,GAAUA,EAAM,GAAKP,EAA2B,CAAC,EACtE,MAAMQ,EAAYvE,EAAM,KAAKjB,EAAE,CAAC,EAChC,IAAIyF,EAAUxE,EAAM,KAAKjB,EAAE,CAAC,EAC5B,MAAM0F,EAAQC,EAAWxB,EAAWjE,EAAMC,EAAW0D,EAAa2B,EAAWC,EAAShF,CAAE,EACxFmF,EAAkB1F,EAAMwF,CAAK,CACjC,CAAG,EACDzE,EAAM,MAAK,EAAG,QAAQ,SAASrB,EAAG,CAChC,MAAMiG,EAAI5E,EAAM,KAAKrB,CAAC,EACtBgB,EAAI,KAAKhB,EAAGiG,EAAE,KAAMA,EAAE,IAAI,EACtBA,EAAE,UACJd,EAAOc,EAAE,KAEf,CAAG,EACDjF,EAAI,KAAK,sCAAuCqD,EAAMc,CAAI,EACnD,CAAE,KAAAd,EAAM,KAAAc,CAAM,CACvB,EAAG,iBAAiB,EAChBe,GAAyBvF,EAAO,MAAOwF,EAAaC,IAAQ,iBAC9D,MAAM/E,EAAQ,IAAIgF,EAAgB,CAChC,WAAY,GACZ,SAAU,EACX,CAAA,EAAE,SAAS,CACV,QAASF,EAAY,UACrB,UAAS3C,EAAA2C,EAAY,SAAZ,YAAA3C,EAAoB,gBAAe8C,GAAA/C,EAAA4C,EAAY,SAAZ,YAAA5C,EAAoB,YAApB,YAAA+C,EAA+B,cAAeH,EAAY,YACtG,UAASI,EAAAJ,EAAY,SAAZ,YAAAI,EAAoB,gBAAeC,GAAAC,EAAAN,EAAY,SAAZ,YAAAM,EAAoB,YAApB,YAAAD,EAA+B,cAAeL,EAAY,YACtG,QAAS,EACT,QAAS,CACb,CAAG,EAAE,oBAAoB,UAAW,CAChC,MAAO,CAAE,CACb,CAAG,EACKO,EAAUN,EAAI,OAAO,GAAG,EAC9BO,EAAgBD,EAASP,EAAY,QAASA,EAAY,KAAMA,EAAY,SAAS,EACrFS,EAAQ,EACRC,EAAQ,EACRC,EAAO,EACPpG,GAAQ,EACRyF,EAAY,MAAM,QAAShG,GAAS,CAClCkB,EAAM,QAAQlB,EAAK,GAAI,CAAE,GAAGA,CAAI,CAAE,EAC9BA,EAAK,UACPkB,EAAM,UAAUlB,EAAK,GAAIA,EAAK,QAAQ,CAE5C,CAAG,EACDa,EAAI,MAAM,SAAUmF,EAAY,KAAK,EACrCA,EAAY,MAAM,QAAS7F,GAAS,CAClC,GAAIA,EAAK,QAAUA,EAAK,IAAK,CAC3B,MAAMyG,EAASzG,EAAK,MACd0G,EAAaD,EAAS,MAAQA,EAAS,OACvCE,EAAaF,EAAS,MAAQA,EAAS,OACvC5G,EAAOkB,EAAM,KAAK0F,CAAM,EAC9B1F,EAAM,QAAQ2F,EAAY,CACxB,MAAOA,EACP,GAAIA,EACJ,SAAU7G,EAAK,SACf,WAAY,GACZ,MAAO,GACP,QAAS,EACT,MAAO,YAEP,MAAO,GACP,MAAO,GACP,OAAQ,EAChB,CAAO,EACDkB,EAAM,UAAU2F,EAAY7G,EAAK,QAAQ,EACzCkB,EAAM,QAAQ4F,EAAY,CACxB,MAAOA,EACP,GAAIA,EACJ,SAAU9G,EAAK,SACf,WAAY,GACZ,QAAS,EAET,MAAO,YACP,MAAO,GACP,MAAO,GACP,MAAO,GACP,OAAQ,EAChB,CAAO,EACDkB,EAAM,UAAU4F,EAAY9G,EAAK,QAAQ,EACzC,MAAM+G,EAAQ,gBAAgB5G,CAAI,EAC5B6G,EAAU,gBAAgB7G,CAAI,EAC9B8G,EAAQ,gBAAgB9G,CAAI,EAClC4G,EAAM,MAAQ,GACdA,EAAM,aAAe,OACrBA,EAAM,GAAKH,EAAS,oBACpBI,EAAQ,eAAiB,OACzBA,EAAQ,aAAe,OACvBA,EAAQ,GAAKJ,EAAS,sBACtBK,EAAM,MAAQ,GACVjH,EAAK,UACP+G,EAAM,YAAcH,EACpBK,EAAM,UAAYL,GAEpBK,EAAM,GAAKL,EAAS,oBACpBK,EAAM,eAAiB,OACvB/F,EAAM,QAAQ0F,EAAQC,EAAYE,EAAOH,EAAS,mBAAmB,EACrE1F,EAAM,QAAQ2F,EAAYC,EAAYE,EAASJ,EAAS,mBAAmB,EAC3E1F,EAAM,QAAQ4F,EAAYF,EAAQK,EAAOL,EAAS,oBAAoB,CAC5E,MACM1F,EAAM,QAAQf,EAAK,MAAOA,EAAK,IAAK,CAAE,GAAGA,CAAI,EAAIA,EAAK,EAAE,CAE9D,CAAG,EACDU,EAAI,KAAK,kBAAmB,KAAK,UAAUoD,EAAoB/C,CAAK,CAAC,CAAC,EACtEwB,GAAuBxB,CAAK,EAC5BL,EAAI,KAAK,mBAAoB,KAAK,UAAUoD,EAAoB/C,CAAK,CAAC,CAAC,EACvE,MAAM8C,EAAakD,EAAW,EAC9B,MAAMtD,EACJ2C,EACArF,EACA8E,EAAY,KACZA,EAAY,UACZ,OACAhC,CACD,CACH,EAAG,QAAQ", "x_google_ignoreList": [0, 1]}