{"version": 3, "file": "nextflow.js", "sources": ["../../../../node_modules/.pnpm/@shikijs+langs@3.4.1/node_modules/@shikijs/langs/dist/nextflow.mjs"], "sourcesContent": ["const lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Nextflow\\\",\\\"name\\\":\\\"nextflow\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#nextflow\\\"}],\\\"repository\\\":{\\\"enum-def\\\":{\\\"begin\\\":\\\"^\\\\\\\\s*(enum)\\\\\\\\s+(\\\\\\\\w+)\\\\\\\\s*\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.nextflow\\\"},\\\"2\\\":{\\\"name\\\":\\\"storage.type.groovy\\\"}},\\\"end\\\":\\\"}\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.nextflow-groovy#comments\\\"},{\\\"include\\\":\\\"#enum-values\\\"}]},\\\"enum-values\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<=;|^)\\\\\\\\s*\\\\\\\\b([0-9A-Z_]+)(?=\\\\\\\\s*(?:[(,}]|$))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.enum.name.groovy\\\"}},\\\"end\\\":\\\",|(?=})|^(?!\\\\\\\\s*\\\\\\\\w+\\\\\\\\s*(?:,|$))\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"name\\\":\\\"meta.enum.value.groovy\\\",\\\"patterns\\\":[{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.definition.seperator.parameter.groovy\\\"},{\\\"include\\\":\\\"#groovy-code\\\"}]}]}]},\\\"function-body\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\s\\\"},{\\\"begin\\\":\\\"(?=[<\\\\\\\\w][^(]*\\\\\\\\s+[$<\\\\\\\\w]+\\\\\\\\s*\\\\\\\\()\\\",\\\"end\\\":\\\"(?=[$\\\\\\\\w]+\\\\\\\\s*\\\\\\\\()\\\",\\\"name\\\":\\\"meta.method.return-type.java\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.nextflow-groovy#types\\\"}]},{\\\"begin\\\":\\\"([$\\\\\\\\w]+)\\\\\\\\s*\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.nextflow\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"name\\\":\\\"meta.definition.method.signature.java\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?=[^)])\\\",\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"name\\\":\\\"meta.method.parameters.groovy\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?=[^),])\\\",\\\"end\\\":\\\"(?=[),])\\\",\\\"name\\\":\\\"meta.method.parameter.groovy\\\",\\\"patterns\\\":[{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.definition.separator.groovy\\\"},{\\\"begin\\\":\\\"=\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.assignment.groovy\\\"}},\\\"end\\\":\\\"(?=[),])\\\",\\\"name\\\":\\\"meta.parameter.default.groovy\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.nextflow-groovy#groovy-code\\\"}]},{\\\"include\\\":\\\"source.nextflow-groovy#parameters\\\"}]}]}]},{\\\"begin\\\":\\\"(?=<)\\\",\\\"end\\\":\\\"(?=\\\\\\\\s)\\\",\\\"name\\\":\\\"meta.method.paramerised-type.groovy\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"<\\\",\\\"end\\\":\\\">\\\",\\\"name\\\":\\\"storage.type.parameters.groovy\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.nextflow-groovy#types\\\"},{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.definition.seperator.groovy\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\{\\\",\\\"end\\\":\\\"(?=})\\\",\\\"name\\\":\\\"meta.method.body.java\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.nextflow-groovy#groovy-code\\\"}]}]},\\\"function-def\\\":{\\\"applyEndPatternLast\\\":1,\\\"begin\\\":\\\"(?<=;|^|\\\\\\\\{)(?=\\\\\\\\s*(?:def|(?:(?:boolean|byte|char|short|int|float|long|double)|@?(?:[A-Za-z]\\\\\\\\w*\\\\\\\\.)*[A-Z]+\\\\\\\\w*)[]\\\\\\\\[]*(?:<.*>)?n)\\\\\\\\s+([^=]+\\\\\\\\s+)?\\\\\\\\w+\\\\\\\\s*\\\\\\\\()\\\",\\\"end\\\":\\\"}|(?=[^{])\\\",\\\"name\\\":\\\"meta.definition.method.groovy\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#function-body\\\"}]},\\\"include-decl\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"^\\\\\\\\b(include)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.nextflow\\\"},{\\\"match\\\":\\\"\\\\\\\\b(from)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.nextflow\\\"}]},\\\"nextflow\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#enum-def\\\"},{\\\"include\\\":\\\"#function-def\\\"},{\\\"include\\\":\\\"#process-def\\\"},{\\\"include\\\":\\\"#workflow-def\\\"},{\\\"include\\\":\\\"#output-def\\\"},{\\\"include\\\":\\\"#include-decl\\\"},{\\\"include\\\":\\\"source.nextflow-groovy\\\"}]},\\\"output-def\\\":{\\\"begin\\\":\\\"^\\\\\\\\s*(output)\\\\\\\\s*\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.nextflow\\\"}},\\\"end\\\":\\\"}\\\",\\\"name\\\":\\\"output.nextflow\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.nextflow-groovy#groovy\\\"}]},\\\"process-body\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?:input|output|when|script|shell|exec):\\\",\\\"name\\\":\\\"constant.block.nextflow\\\"},{\\\"match\\\":\\\"\\\\\\\\b(val|env|file|path|stdin|stdout|tuple)([(\\\\\\\\s])\\\",\\\"name\\\":\\\"entity.name.function.nextflow\\\"},{\\\"include\\\":\\\"source.nextflow-groovy#groovy\\\"}]},\\\"process-def\\\":{\\\"begin\\\":\\\"^\\\\\\\\s*(process)\\\\\\\\s+(\\\\\\\\w+)\\\\\\\\s*\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.nextflow\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.function.nextflow\\\"}},\\\"end\\\":\\\"}\\\",\\\"name\\\":\\\"process.nextflow\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#process-body\\\"}]},\\\"workflow-body\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?:take|main|emit|publish):\\\",\\\"name\\\":\\\"constant.block.nextflow\\\"},{\\\"include\\\":\\\"source.nextflow-groovy#groovy\\\"}]},\\\"workflow-def\\\":{\\\"begin\\\":\\\"^\\\\\\\\s*(workflow)(?:\\\\\\\\s+(\\\\\\\\w+))?\\\\\\\\s*\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.nextflow\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.function.nextflow\\\"}},\\\"end\\\":\\\"}\\\",\\\"name\\\":\\\"workflow.nextflow\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#workflow-body\\\"}]}},\\\"scopeName\\\":\\\"source.nextflow\\\",\\\"aliases\\\":[\\\"nf\\\"]}\"))\n\nexport default [\nlang\n]\n"], "names": ["lang", "nextflow"], "mappings": "AAAA,MAAMA,EAAO,OAAO,OAAO,KAAK,MAAM,iyHAAywI,CAAC,EAEjyIC,EAAA,CACfD,CACA", "x_google_ignoreList": [0]}