{"version": 3, "file": "howler.js", "sources": ["../../../../node_modules/.pnpm/howler@2.2.4/node_modules/howler/dist/howler.js"], "sourcesContent": ["/*!\n *  howler.js v2.2.4\n *  howlerjs.com\n *\n *  (c) 2013-2020, <PERSON> of GoldFire Studios\n *  goldfirestudios.com\n *\n *  MIT License\n */\n\n(function() {\n\n  'use strict';\n\n  /** Global Methods **/\n  /***************************************************************************/\n\n  /**\n   * Create the global controller. All contained methods and properties apply\n   * to all sounds that are currently playing or will be in the future.\n   */\n  var HowlerGlobal = function() {\n    this.init();\n  };\n  HowlerGlobal.prototype = {\n    /**\n     * Initialize the global Howler object.\n     * @return {Howler}\n     */\n    init: function() {\n      var self = this || Howler;\n\n      // Create a global ID counter.\n      self._counter = 1000;\n\n      // Pool of unlocked HTML5 Audio objects.\n      self._html5AudioPool = [];\n      self.html5PoolSize = 10;\n\n      // Internal properties.\n      self._codecs = {};\n      self._howls = [];\n      self._muted = false;\n      self._volume = 1;\n      self._canPlayEvent = 'canplaythrough';\n      self._navigator = (typeof window !== 'undefined' && window.navigator) ? window.navigator : null;\n\n      // Public properties.\n      self.masterGain = null;\n      self.noAudio = false;\n      self.usingWebAudio = true;\n      self.autoSuspend = true;\n      self.ctx = null;\n\n      // Set to false to disable the auto audio unlocker.\n      self.autoUnlock = true;\n\n      // Setup the various state values for global tracking.\n      self._setup();\n\n      return self;\n    },\n\n    /**\n     * Get/set the global volume for all sounds.\n     * @param  {Float} vol Volume from 0.0 to 1.0.\n     * @return {Howler/Float}     Returns self or current volume.\n     */\n    volume: function(vol) {\n      var self = this || Howler;\n      vol = parseFloat(vol);\n\n      // If we don't have an AudioContext created yet, run the setup.\n      if (!self.ctx) {\n        setupAudioContext();\n      }\n\n      if (typeof vol !== 'undefined' && vol >= 0 && vol <= 1) {\n        self._volume = vol;\n\n        // Don't update any of the nodes if we are muted.\n        if (self._muted) {\n          return self;\n        }\n\n        // When using Web Audio, we just need to adjust the master gain.\n        if (self.usingWebAudio) {\n          self.masterGain.gain.setValueAtTime(vol, Howler.ctx.currentTime);\n        }\n\n        // Loop through and change volume for all HTML5 audio nodes.\n        for (var i=0; i<self._howls.length; i++) {\n          if (!self._howls[i]._webAudio) {\n            // Get all of the sounds in this Howl group.\n            var ids = self._howls[i]._getSoundIds();\n\n            // Loop through all sounds and change the volumes.\n            for (var j=0; j<ids.length; j++) {\n              var sound = self._howls[i]._soundById(ids[j]);\n\n              if (sound && sound._node) {\n                sound._node.volume = sound._volume * vol;\n              }\n            }\n          }\n        }\n\n        return self;\n      }\n\n      return self._volume;\n    },\n\n    /**\n     * Handle muting and unmuting globally.\n     * @param  {Boolean} muted Is muted or not.\n     */\n    mute: function(muted) {\n      var self = this || Howler;\n\n      // If we don't have an AudioContext created yet, run the setup.\n      if (!self.ctx) {\n        setupAudioContext();\n      }\n\n      self._muted = muted;\n\n      // With Web Audio, we just need to mute the master gain.\n      if (self.usingWebAudio) {\n        self.masterGain.gain.setValueAtTime(muted ? 0 : self._volume, Howler.ctx.currentTime);\n      }\n\n      // Loop through and mute all HTML5 Audio nodes.\n      for (var i=0; i<self._howls.length; i++) {\n        if (!self._howls[i]._webAudio) {\n          // Get all of the sounds in this Howl group.\n          var ids = self._howls[i]._getSoundIds();\n\n          // Loop through all sounds and mark the audio node as muted.\n          for (var j=0; j<ids.length; j++) {\n            var sound = self._howls[i]._soundById(ids[j]);\n\n            if (sound && sound._node) {\n              sound._node.muted = (muted) ? true : sound._muted;\n            }\n          }\n        }\n      }\n\n      return self;\n    },\n\n    /**\n     * Handle stopping all sounds globally.\n     */\n    stop: function() {\n      var self = this || Howler;\n\n      // Loop through all Howls and stop them.\n      for (var i=0; i<self._howls.length; i++) {\n        self._howls[i].stop();\n      }\n\n      return self;\n    },\n\n    /**\n     * Unload and destroy all currently loaded Howl objects.\n     * @return {Howler}\n     */\n    unload: function() {\n      var self = this || Howler;\n\n      for (var i=self._howls.length-1; i>=0; i--) {\n        self._howls[i].unload();\n      }\n\n      // Create a new AudioContext to make sure it is fully reset.\n      if (self.usingWebAudio && self.ctx && typeof self.ctx.close !== 'undefined') {\n        self.ctx.close();\n        self.ctx = null;\n        setupAudioContext();\n      }\n\n      return self;\n    },\n\n    /**\n     * Check for codec support of specific extension.\n     * @param  {String} ext Audio file extention.\n     * @return {Boolean}\n     */\n    codecs: function(ext) {\n      return (this || Howler)._codecs[ext.replace(/^x-/, '')];\n    },\n\n    /**\n     * Setup various state values for global tracking.\n     * @return {Howler}\n     */\n    _setup: function() {\n      var self = this || Howler;\n\n      // Keeps track of the suspend/resume state of the AudioContext.\n      self.state = self.ctx ? self.ctx.state || 'suspended' : 'suspended';\n\n      // Automatically begin the 30-second suspend process\n      self._autoSuspend();\n\n      // Check if audio is available.\n      if (!self.usingWebAudio) {\n        // No audio is available on this system if noAudio is set to true.\n        if (typeof Audio !== 'undefined') {\n          try {\n            var test = new Audio();\n\n            // Check if the canplaythrough event is available.\n            if (typeof test.oncanplaythrough === 'undefined') {\n              self._canPlayEvent = 'canplay';\n            }\n          } catch(e) {\n            self.noAudio = true;\n          }\n        } else {\n          self.noAudio = true;\n        }\n      }\n\n      // Test to make sure audio isn't disabled in Internet Explorer.\n      try {\n        var test = new Audio();\n        if (test.muted) {\n          self.noAudio = true;\n        }\n      } catch (e) {}\n\n      // Check for supported codecs.\n      if (!self.noAudio) {\n        self._setupCodecs();\n      }\n\n      return self;\n    },\n\n    /**\n     * Check for browser support for various codecs and cache the results.\n     * @return {Howler}\n     */\n    _setupCodecs: function() {\n      var self = this || Howler;\n      var audioTest = null;\n\n      // Must wrap in a try/catch because IE11 in server mode throws an error.\n      try {\n        audioTest = (typeof Audio !== 'undefined') ? new Audio() : null;\n      } catch (err) {\n        return self;\n      }\n\n      if (!audioTest || typeof audioTest.canPlayType !== 'function') {\n        return self;\n      }\n\n      var mpegTest = audioTest.canPlayType('audio/mpeg;').replace(/^no$/, '');\n\n      // Opera version <33 has mixed MP3 support, so we need to check for and block it.\n      var ua = self._navigator ? self._navigator.userAgent : '';\n      var checkOpera = ua.match(/OPR\\/(\\d+)/g);\n      var isOldOpera = (checkOpera && parseInt(checkOpera[0].split('/')[1], 10) < 33);\n      var checkSafari = ua.indexOf('Safari') !== -1 && ua.indexOf('Chrome') === -1;\n      var safariVersion = ua.match(/Version\\/(.*?) /);\n      var isOldSafari = (checkSafari && safariVersion && parseInt(safariVersion[1], 10) < 15);\n\n      self._codecs = {\n        mp3: !!(!isOldOpera && (mpegTest || audioTest.canPlayType('audio/mp3;').replace(/^no$/, ''))),\n        mpeg: !!mpegTest,\n        opus: !!audioTest.canPlayType('audio/ogg; codecs=\"opus\"').replace(/^no$/, ''),\n        ogg: !!audioTest.canPlayType('audio/ogg; codecs=\"vorbis\"').replace(/^no$/, ''),\n        oga: !!audioTest.canPlayType('audio/ogg; codecs=\"vorbis\"').replace(/^no$/, ''),\n        wav: !!(audioTest.canPlayType('audio/wav; codecs=\"1\"') || audioTest.canPlayType('audio/wav')).replace(/^no$/, ''),\n        aac: !!audioTest.canPlayType('audio/aac;').replace(/^no$/, ''),\n        caf: !!audioTest.canPlayType('audio/x-caf;').replace(/^no$/, ''),\n        m4a: !!(audioTest.canPlayType('audio/x-m4a;') || audioTest.canPlayType('audio/m4a;') || audioTest.canPlayType('audio/aac;')).replace(/^no$/, ''),\n        m4b: !!(audioTest.canPlayType('audio/x-m4b;') || audioTest.canPlayType('audio/m4b;') || audioTest.canPlayType('audio/aac;')).replace(/^no$/, ''),\n        mp4: !!(audioTest.canPlayType('audio/x-mp4;') || audioTest.canPlayType('audio/mp4;') || audioTest.canPlayType('audio/aac;')).replace(/^no$/, ''),\n        weba: !!(!isOldSafari && audioTest.canPlayType('audio/webm; codecs=\"vorbis\"').replace(/^no$/, '')),\n        webm: !!(!isOldSafari && audioTest.canPlayType('audio/webm; codecs=\"vorbis\"').replace(/^no$/, '')),\n        dolby: !!audioTest.canPlayType('audio/mp4; codecs=\"ec-3\"').replace(/^no$/, ''),\n        flac: !!(audioTest.canPlayType('audio/x-flac;') || audioTest.canPlayType('audio/flac;')).replace(/^no$/, '')\n      };\n\n      return self;\n    },\n\n    /**\n     * Some browsers/devices will only allow audio to be played after a user interaction.\n     * Attempt to automatically unlock audio on the first user interaction.\n     * Concept from: http://paulbakaus.com/tutorials/html5/web-audio-on-ios/\n     * @return {Howler}\n     */\n    _unlockAudio: function() {\n      var self = this || Howler;\n\n      // Only run this if Web Audio is supported and it hasn't already been unlocked.\n      if (self._audioUnlocked || !self.ctx) {\n        return;\n      }\n\n      self._audioUnlocked = false;\n      self.autoUnlock = false;\n\n      // Some mobile devices/platforms have distortion issues when opening/closing tabs and/or web views.\n      // Bugs in the browser (especially Mobile Safari) can cause the sampleRate to change from 44100 to 48000.\n      // By calling Howler.unload(), we create a new AudioContext with the correct sampleRate.\n      if (!self._mobileUnloaded && self.ctx.sampleRate !== 44100) {\n        self._mobileUnloaded = true;\n        self.unload();\n      }\n\n      // Scratch buffer for enabling iOS to dispose of web audio buffers correctly, as per:\n      // http://stackoverflow.com/questions/24119684\n      self._scratchBuffer = self.ctx.createBuffer(1, 1, 22050);\n\n      // Call this method on touch start to create and play a buffer,\n      // then check if the audio actually played to determine if\n      // audio has now been unlocked on iOS, Android, etc.\n      var unlock = function(e) {\n        // Create a pool of unlocked HTML5 Audio objects that can\n        // be used for playing sounds without user interaction. HTML5\n        // Audio objects must be individually unlocked, as opposed\n        // to the WebAudio API which only needs a single activation.\n        // This must occur before WebAudio setup or the source.onended\n        // event will not fire.\n        while (self._html5AudioPool.length < self.html5PoolSize) {\n          try {\n            var audioNode = new Audio();\n\n            // Mark this Audio object as unlocked to ensure it can get returned\n            // to the unlocked pool when released.\n            audioNode._unlocked = true;\n\n            // Add the audio node to the pool.\n            self._releaseHtml5Audio(audioNode);\n          } catch (e) {\n            self.noAudio = true;\n            break;\n          }\n        }\n\n        // Loop through any assigned audio nodes and unlock them.\n        for (var i=0; i<self._howls.length; i++) {\n          if (!self._howls[i]._webAudio) {\n            // Get all of the sounds in this Howl group.\n            var ids = self._howls[i]._getSoundIds();\n\n            // Loop through all sounds and unlock the audio nodes.\n            for (var j=0; j<ids.length; j++) {\n              var sound = self._howls[i]._soundById(ids[j]);\n\n              if (sound && sound._node && !sound._node._unlocked) {\n                sound._node._unlocked = true;\n                sound._node.load();\n              }\n            }\n          }\n        }\n\n        // Fix Android can not play in suspend state.\n        self._autoResume();\n\n        // Create an empty buffer.\n        var source = self.ctx.createBufferSource();\n        source.buffer = self._scratchBuffer;\n        source.connect(self.ctx.destination);\n\n        // Play the empty buffer.\n        if (typeof source.start === 'undefined') {\n          source.noteOn(0);\n        } else {\n          source.start(0);\n        }\n\n        // Calling resume() on a stack initiated by user gesture is what actually unlocks the audio on Android Chrome >= 55.\n        if (typeof self.ctx.resume === 'function') {\n          self.ctx.resume();\n        }\n\n        // Setup a timeout to check that we are unlocked on the next event loop.\n        source.onended = function() {\n          source.disconnect(0);\n\n          // Update the unlocked state and prevent this check from happening again.\n          self._audioUnlocked = true;\n\n          // Remove the touch start listener.\n          document.removeEventListener('touchstart', unlock, true);\n          document.removeEventListener('touchend', unlock, true);\n          document.removeEventListener('click', unlock, true);\n          document.removeEventListener('keydown', unlock, true);\n\n          // Let all sounds know that audio has been unlocked.\n          for (var i=0; i<self._howls.length; i++) {\n            self._howls[i]._emit('unlock');\n          }\n        };\n      };\n\n      // Setup a touch start listener to attempt an unlock in.\n      document.addEventListener('touchstart', unlock, true);\n      document.addEventListener('touchend', unlock, true);\n      document.addEventListener('click', unlock, true);\n      document.addEventListener('keydown', unlock, true);\n\n      return self;\n    },\n\n    /**\n     * Get an unlocked HTML5 Audio object from the pool. If none are left,\n     * return a new Audio object and throw a warning.\n     * @return {Audio} HTML5 Audio object.\n     */\n    _obtainHtml5Audio: function() {\n      var self = this || Howler;\n\n      // Return the next object from the pool if one exists.\n      if (self._html5AudioPool.length) {\n        return self._html5AudioPool.pop();\n      }\n\n      //.Check if the audio is locked and throw a warning.\n      var testPlay = new Audio().play();\n      if (testPlay && typeof Promise !== 'undefined' && (testPlay instanceof Promise || typeof testPlay.then === 'function')) {\n        testPlay.catch(function() {\n          console.warn('HTML5 Audio pool exhausted, returning potentially locked audio object.');\n        });\n      }\n\n      return new Audio();\n    },\n\n    /**\n     * Return an activated HTML5 Audio object to the pool.\n     * @return {Howler}\n     */\n    _releaseHtml5Audio: function(audio) {\n      var self = this || Howler;\n\n      // Don't add audio to the pool if we don't know if it has been unlocked.\n      if (audio._unlocked) {\n        self._html5AudioPool.push(audio);\n      }\n\n      return self;\n    },\n\n    /**\n     * Automatically suspend the Web Audio AudioContext after no sound has played for 30 seconds.\n     * This saves processing/energy and fixes various browser-specific bugs with audio getting stuck.\n     * @return {Howler}\n     */\n    _autoSuspend: function() {\n      var self = this;\n\n      if (!self.autoSuspend || !self.ctx || typeof self.ctx.suspend === 'undefined' || !Howler.usingWebAudio) {\n        return;\n      }\n\n      // Check if any sounds are playing.\n      for (var i=0; i<self._howls.length; i++) {\n        if (self._howls[i]._webAudio) {\n          for (var j=0; j<self._howls[i]._sounds.length; j++) {\n            if (!self._howls[i]._sounds[j]._paused) {\n              return self;\n            }\n          }\n        }\n      }\n\n      if (self._suspendTimer) {\n        clearTimeout(self._suspendTimer);\n      }\n\n      // If no sound has played after 30 seconds, suspend the context.\n      self._suspendTimer = setTimeout(function() {\n        if (!self.autoSuspend) {\n          return;\n        }\n\n        self._suspendTimer = null;\n        self.state = 'suspending';\n\n        // Handle updating the state of the audio context after suspending.\n        var handleSuspension = function() {\n          self.state = 'suspended';\n\n          if (self._resumeAfterSuspend) {\n            delete self._resumeAfterSuspend;\n            self._autoResume();\n          }\n        };\n\n        // Either the state gets suspended or it is interrupted.\n        // Either way, we need to update the state to suspended.\n        self.ctx.suspend().then(handleSuspension, handleSuspension);\n      }, 30000);\n\n      return self;\n    },\n\n    /**\n     * Automatically resume the Web Audio AudioContext when a new sound is played.\n     * @return {Howler}\n     */\n    _autoResume: function() {\n      var self = this;\n\n      if (!self.ctx || typeof self.ctx.resume === 'undefined' || !Howler.usingWebAudio) {\n        return;\n      }\n\n      if (self.state === 'running' && self.ctx.state !== 'interrupted' && self._suspendTimer) {\n        clearTimeout(self._suspendTimer);\n        self._suspendTimer = null;\n      } else if (self.state === 'suspended' || self.state === 'running' && self.ctx.state === 'interrupted') {\n        self.ctx.resume().then(function() {\n          self.state = 'running';\n\n          // Emit to all Howls that the audio has resumed.\n          for (var i=0; i<self._howls.length; i++) {\n            self._howls[i]._emit('resume');\n          }\n        });\n\n        if (self._suspendTimer) {\n          clearTimeout(self._suspendTimer);\n          self._suspendTimer = null;\n        }\n      } else if (self.state === 'suspending') {\n        self._resumeAfterSuspend = true;\n      }\n\n      return self;\n    }\n  };\n\n  // Setup the global audio controller.\n  var Howler = new HowlerGlobal();\n\n  /** Group Methods **/\n  /***************************************************************************/\n\n  /**\n   * Create an audio group controller.\n   * @param {Object} o Passed in properties for this group.\n   */\n  var Howl = function(o) {\n    var self = this;\n\n    // Throw an error if no source is provided.\n    if (!o.src || o.src.length === 0) {\n      console.error('An array of source files must be passed with any new Howl.');\n      return;\n    }\n\n    self.init(o);\n  };\n  Howl.prototype = {\n    /**\n     * Initialize a new Howl group object.\n     * @param  {Object} o Passed in properties for this group.\n     * @return {Howl}\n     */\n    init: function(o) {\n      var self = this;\n\n      // If we don't have an AudioContext created yet, run the setup.\n      if (!Howler.ctx) {\n        setupAudioContext();\n      }\n\n      // Setup user-defined default properties.\n      self._autoplay = o.autoplay || false;\n      self._format = (typeof o.format !== 'string') ? o.format : [o.format];\n      self._html5 = o.html5 || false;\n      self._muted = o.mute || false;\n      self._loop = o.loop || false;\n      self._pool = o.pool || 5;\n      self._preload = (typeof o.preload === 'boolean' || o.preload === 'metadata') ? o.preload : true;\n      self._rate = o.rate || 1;\n      self._sprite = o.sprite || {};\n      self._src = (typeof o.src !== 'string') ? o.src : [o.src];\n      self._volume = o.volume !== undefined ? o.volume : 1;\n      self._xhr = {\n        method: o.xhr && o.xhr.method ? o.xhr.method : 'GET',\n        headers: o.xhr && o.xhr.headers ? o.xhr.headers : null,\n        withCredentials: o.xhr && o.xhr.withCredentials ? o.xhr.withCredentials : false,\n      };\n\n      // Setup all other default properties.\n      self._duration = 0;\n      self._state = 'unloaded';\n      self._sounds = [];\n      self._endTimers = {};\n      self._queue = [];\n      self._playLock = false;\n\n      // Setup event listeners.\n      self._onend = o.onend ? [{fn: o.onend}] : [];\n      self._onfade = o.onfade ? [{fn: o.onfade}] : [];\n      self._onload = o.onload ? [{fn: o.onload}] : [];\n      self._onloaderror = o.onloaderror ? [{fn: o.onloaderror}] : [];\n      self._onplayerror = o.onplayerror ? [{fn: o.onplayerror}] : [];\n      self._onpause = o.onpause ? [{fn: o.onpause}] : [];\n      self._onplay = o.onplay ? [{fn: o.onplay}] : [];\n      self._onstop = o.onstop ? [{fn: o.onstop}] : [];\n      self._onmute = o.onmute ? [{fn: o.onmute}] : [];\n      self._onvolume = o.onvolume ? [{fn: o.onvolume}] : [];\n      self._onrate = o.onrate ? [{fn: o.onrate}] : [];\n      self._onseek = o.onseek ? [{fn: o.onseek}] : [];\n      self._onunlock = o.onunlock ? [{fn: o.onunlock}] : [];\n      self._onresume = [];\n\n      // Web Audio or HTML5 Audio?\n      self._webAudio = Howler.usingWebAudio && !self._html5;\n\n      // Automatically try to enable audio.\n      if (typeof Howler.ctx !== 'undefined' && Howler.ctx && Howler.autoUnlock) {\n        Howler._unlockAudio();\n      }\n\n      // Keep track of this Howl group in the global controller.\n      Howler._howls.push(self);\n\n      // If they selected autoplay, add a play event to the load queue.\n      if (self._autoplay) {\n        self._queue.push({\n          event: 'play',\n          action: function() {\n            self.play();\n          }\n        });\n      }\n\n      // Load the source file unless otherwise specified.\n      if (self._preload && self._preload !== 'none') {\n        self.load();\n      }\n\n      return self;\n    },\n\n    /**\n     * Load the audio file.\n     * @return {Howler}\n     */\n    load: function() {\n      var self = this;\n      var url = null;\n\n      // If no audio is available, quit immediately.\n      if (Howler.noAudio) {\n        self._emit('loaderror', null, 'No audio support.');\n        return;\n      }\n\n      // Make sure our source is in an array.\n      if (typeof self._src === 'string') {\n        self._src = [self._src];\n      }\n\n      // Loop through the sources and pick the first one that is compatible.\n      for (var i=0; i<self._src.length; i++) {\n        var ext, str;\n\n        if (self._format && self._format[i]) {\n          // If an extension was specified, use that instead.\n          ext = self._format[i];\n        } else {\n          // Make sure the source is a string.\n          str = self._src[i];\n          if (typeof str !== 'string') {\n            self._emit('loaderror', null, 'Non-string found in selected audio sources - ignoring.');\n            continue;\n          }\n\n          // Extract the file extension from the URL or base64 data URI.\n          ext = /^data:audio\\/([^;,]+);/i.exec(str);\n          if (!ext) {\n            ext = /\\.([^.]+)$/.exec(str.split('?', 1)[0]);\n          }\n\n          if (ext) {\n            ext = ext[1].toLowerCase();\n          }\n        }\n\n        // Log a warning if no extension was found.\n        if (!ext) {\n          console.warn('No file extension was found. Consider using the \"format\" property or specify an extension.');\n        }\n\n        // Check if this extension is available.\n        if (ext && Howler.codecs(ext)) {\n          url = self._src[i];\n          break;\n        }\n      }\n\n      if (!url) {\n        self._emit('loaderror', null, 'No codec support for selected audio sources.');\n        return;\n      }\n\n      self._src = url;\n      self._state = 'loading';\n\n      // If the hosting page is HTTPS and the source isn't,\n      // drop down to HTML5 Audio to avoid Mixed Content errors.\n      if (window.location.protocol === 'https:' && url.slice(0, 5) === 'http:') {\n        self._html5 = true;\n        self._webAudio = false;\n      }\n\n      // Create a new sound object and add it to the pool.\n      new Sound(self);\n\n      // Load and decode the audio data for playback.\n      if (self._webAudio) {\n        loadBuffer(self);\n      }\n\n      return self;\n    },\n\n    /**\n     * Play a sound or resume previous playback.\n     * @param  {String/Number} sprite   Sprite name for sprite playback or sound id to continue previous.\n     * @param  {Boolean} internal Internal Use: true prevents event firing.\n     * @return {Number}          Sound ID.\n     */\n    play: function(sprite, internal) {\n      var self = this;\n      var id = null;\n\n      // Determine if a sprite, sound id or nothing was passed\n      if (typeof sprite === 'number') {\n        id = sprite;\n        sprite = null;\n      } else if (typeof sprite === 'string' && self._state === 'loaded' && !self._sprite[sprite]) {\n        // If the passed sprite doesn't exist, do nothing.\n        return null;\n      } else if (typeof sprite === 'undefined') {\n        // Use the default sound sprite (plays the full audio length).\n        sprite = '__default';\n\n        // Check if there is a single paused sound that isn't ended.\n        // If there is, play that sound. If not, continue as usual.\n        if (!self._playLock) {\n          var num = 0;\n          for (var i=0; i<self._sounds.length; i++) {\n            if (self._sounds[i]._paused && !self._sounds[i]._ended) {\n              num++;\n              id = self._sounds[i]._id;\n            }\n          }\n\n          if (num === 1) {\n            sprite = null;\n          } else {\n            id = null;\n          }\n        }\n      }\n\n      // Get the selected node, or get one from the pool.\n      var sound = id ? self._soundById(id) : self._inactiveSound();\n\n      // If the sound doesn't exist, do nothing.\n      if (!sound) {\n        return null;\n      }\n\n      // Select the sprite definition.\n      if (id && !sprite) {\n        sprite = sound._sprite || '__default';\n      }\n\n      // If the sound hasn't loaded, we must wait to get the audio's duration.\n      // We also need to wait to make sure we don't run into race conditions with\n      // the order of function calls.\n      if (self._state !== 'loaded') {\n        // Set the sprite value on this sound.\n        sound._sprite = sprite;\n\n        // Mark this sound as not ended in case another sound is played before this one loads.\n        sound._ended = false;\n\n        // Add the sound to the queue to be played on load.\n        var soundId = sound._id;\n        self._queue.push({\n          event: 'play',\n          action: function() {\n            self.play(soundId);\n          }\n        });\n\n        return soundId;\n      }\n\n      // Don't play the sound if an id was passed and it is already playing.\n      if (id && !sound._paused) {\n        // Trigger the play event, in order to keep iterating through queue.\n        if (!internal) {\n          self._loadQueue('play');\n        }\n\n        return sound._id;\n      }\n\n      // Make sure the AudioContext isn't suspended, and resume it if it is.\n      if (self._webAudio) {\n        Howler._autoResume();\n      }\n\n      // Determine how long to play for and where to start playing.\n      var seek = Math.max(0, sound._seek > 0 ? sound._seek : self._sprite[sprite][0] / 1000);\n      var duration = Math.max(0, ((self._sprite[sprite][0] + self._sprite[sprite][1]) / 1000) - seek);\n      var timeout = (duration * 1000) / Math.abs(sound._rate);\n      var start = self._sprite[sprite][0] / 1000;\n      var stop = (self._sprite[sprite][0] + self._sprite[sprite][1]) / 1000;\n      sound._sprite = sprite;\n\n      // Mark the sound as ended instantly so that this async playback\n      // doesn't get grabbed by another call to play while this one waits to start.\n      sound._ended = false;\n\n      // Update the parameters of the sound.\n      var setParams = function() {\n        sound._paused = false;\n        sound._seek = seek;\n        sound._start = start;\n        sound._stop = stop;\n        sound._loop = !!(sound._loop || self._sprite[sprite][2]);\n      };\n\n      // End the sound instantly if seek is at the end.\n      if (seek >= stop) {\n        self._ended(sound);\n        return;\n      }\n\n      // Begin the actual playback.\n      var node = sound._node;\n      if (self._webAudio) {\n        // Fire this when the sound is ready to play to begin Web Audio playback.\n        var playWebAudio = function() {\n          self._playLock = false;\n          setParams();\n          self._refreshBuffer(sound);\n\n          // Setup the playback params.\n          var vol = (sound._muted || self._muted) ? 0 : sound._volume;\n          node.gain.setValueAtTime(vol, Howler.ctx.currentTime);\n          sound._playStart = Howler.ctx.currentTime;\n\n          // Play the sound using the supported method.\n          if (typeof node.bufferSource.start === 'undefined') {\n            sound._loop ? node.bufferSource.noteGrainOn(0, seek, 86400) : node.bufferSource.noteGrainOn(0, seek, duration);\n          } else {\n            sound._loop ? node.bufferSource.start(0, seek, 86400) : node.bufferSource.start(0, seek, duration);\n          }\n\n          // Start a new timer if none is present.\n          if (timeout !== Infinity) {\n            self._endTimers[sound._id] = setTimeout(self._ended.bind(self, sound), timeout);\n          }\n\n          if (!internal) {\n            setTimeout(function() {\n              self._emit('play', sound._id);\n              self._loadQueue();\n            }, 0);\n          }\n        };\n\n        if (Howler.state === 'running' && Howler.ctx.state !== 'interrupted') {\n          playWebAudio();\n        } else {\n          self._playLock = true;\n\n          // Wait for the audio context to resume before playing.\n          self.once('resume', playWebAudio);\n\n          // Cancel the end timer.\n          self._clearTimer(sound._id);\n        }\n      } else {\n        // Fire this when the sound is ready to play to begin HTML5 Audio playback.\n        var playHtml5 = function() {\n          node.currentTime = seek;\n          node.muted = sound._muted || self._muted || Howler._muted || node.muted;\n          node.volume = sound._volume * Howler.volume();\n          node.playbackRate = sound._rate;\n\n          // Some browsers will throw an error if this is called without user interaction.\n          try {\n            var play = node.play();\n\n            // Support older browsers that don't support promises, and thus don't have this issue.\n            if (play && typeof Promise !== 'undefined' && (play instanceof Promise || typeof play.then === 'function')) {\n              // Implements a lock to prevent DOMException: The play() request was interrupted by a call to pause().\n              self._playLock = true;\n\n              // Set param values immediately.\n              setParams();\n\n              // Releases the lock and executes queued actions.\n              play\n                .then(function() {\n                  self._playLock = false;\n                  node._unlocked = true;\n                  if (!internal) {\n                    self._emit('play', sound._id);\n                  } else {\n                    self._loadQueue();\n                  }\n                })\n                .catch(function() {\n                  self._playLock = false;\n                  self._emit('playerror', sound._id, 'Playback was unable to start. This is most commonly an issue ' +\n                    'on mobile devices and Chrome where playback was not within a user interaction.');\n\n                  // Reset the ended and paused values.\n                  sound._ended = true;\n                  sound._paused = true;\n                });\n            } else if (!internal) {\n              self._playLock = false;\n              setParams();\n              self._emit('play', sound._id);\n            }\n\n            // Setting rate before playing won't work in IE, so we set it again here.\n            node.playbackRate = sound._rate;\n\n            // If the node is still paused, then we can assume there was a playback issue.\n            if (node.paused) {\n              self._emit('playerror', sound._id, 'Playback was unable to start. This is most commonly an issue ' +\n                'on mobile devices and Chrome where playback was not within a user interaction.');\n              return;\n            }\n\n            // Setup the end timer on sprites or listen for the ended event.\n            if (sprite !== '__default' || sound._loop) {\n              self._endTimers[sound._id] = setTimeout(self._ended.bind(self, sound), timeout);\n            } else {\n              self._endTimers[sound._id] = function() {\n                // Fire ended on this audio node.\n                self._ended(sound);\n\n                // Clear this listener.\n                node.removeEventListener('ended', self._endTimers[sound._id], false);\n              };\n              node.addEventListener('ended', self._endTimers[sound._id], false);\n            }\n          } catch (err) {\n            self._emit('playerror', sound._id, err);\n          }\n        };\n\n        // If this is streaming audio, make sure the src is set and load again.\n        if (node.src === 'data:audio/wav;base64,UklGRigAAABXQVZFZm10IBIAAAABAAEARKwAAIhYAQACABAAAABkYXRhAgAAAAEA') {\n          node.src = self._src;\n          node.load();\n        }\n\n        // Play immediately if ready, or wait for the 'canplaythrough'e vent.\n        var loadedNoReadyState = (window && window.ejecta) || (!node.readyState && Howler._navigator.isCocoonJS);\n        if (node.readyState >= 3 || loadedNoReadyState) {\n          playHtml5();\n        } else {\n          self._playLock = true;\n          self._state = 'loading';\n\n          var listener = function() {\n            self._state = 'loaded';\n            \n            // Begin playback.\n            playHtml5();\n\n            // Clear this listener.\n            node.removeEventListener(Howler._canPlayEvent, listener, false);\n          };\n          node.addEventListener(Howler._canPlayEvent, listener, false);\n\n          // Cancel the end timer.\n          self._clearTimer(sound._id);\n        }\n      }\n\n      return sound._id;\n    },\n\n    /**\n     * Pause playback and save current position.\n     * @param  {Number} id The sound ID (empty to pause all in group).\n     * @return {Howl}\n     */\n    pause: function(id) {\n      var self = this;\n\n      // If the sound hasn't loaded or a play() promise is pending, add it to the load queue to pause when capable.\n      if (self._state !== 'loaded' || self._playLock) {\n        self._queue.push({\n          event: 'pause',\n          action: function() {\n            self.pause(id);\n          }\n        });\n\n        return self;\n      }\n\n      // If no id is passed, get all ID's to be paused.\n      var ids = self._getSoundIds(id);\n\n      for (var i=0; i<ids.length; i++) {\n        // Clear the end timer.\n        self._clearTimer(ids[i]);\n\n        // Get the sound.\n        var sound = self._soundById(ids[i]);\n\n        if (sound && !sound._paused) {\n          // Reset the seek position.\n          sound._seek = self.seek(ids[i]);\n          sound._rateSeek = 0;\n          sound._paused = true;\n\n          // Stop currently running fades.\n          self._stopFade(ids[i]);\n\n          if (sound._node) {\n            if (self._webAudio) {\n              // Make sure the sound has been created.\n              if (!sound._node.bufferSource) {\n                continue;\n              }\n\n              if (typeof sound._node.bufferSource.stop === 'undefined') {\n                sound._node.bufferSource.noteOff(0);\n              } else {\n                sound._node.bufferSource.stop(0);\n              }\n\n              // Clean up the buffer source.\n              self._cleanBuffer(sound._node);\n            } else if (!isNaN(sound._node.duration) || sound._node.duration === Infinity) {\n              sound._node.pause();\n            }\n          }\n        }\n\n        // Fire the pause event, unless `true` is passed as the 2nd argument.\n        if (!arguments[1]) {\n          self._emit('pause', sound ? sound._id : null);\n        }\n      }\n\n      return self;\n    },\n\n    /**\n     * Stop playback and reset to start.\n     * @param  {Number} id The sound ID (empty to stop all in group).\n     * @param  {Boolean} internal Internal Use: true prevents event firing.\n     * @return {Howl}\n     */\n    stop: function(id, internal) {\n      var self = this;\n\n      // If the sound hasn't loaded, add it to the load queue to stop when capable.\n      if (self._state !== 'loaded' || self._playLock) {\n        self._queue.push({\n          event: 'stop',\n          action: function() {\n            self.stop(id);\n          }\n        });\n\n        return self;\n      }\n\n      // If no id is passed, get all ID's to be stopped.\n      var ids = self._getSoundIds(id);\n\n      for (var i=0; i<ids.length; i++) {\n        // Clear the end timer.\n        self._clearTimer(ids[i]);\n\n        // Get the sound.\n        var sound = self._soundById(ids[i]);\n\n        if (sound) {\n          // Reset the seek position.\n          sound._seek = sound._start || 0;\n          sound._rateSeek = 0;\n          sound._paused = true;\n          sound._ended = true;\n\n          // Stop currently running fades.\n          self._stopFade(ids[i]);\n\n          if (sound._node) {\n            if (self._webAudio) {\n              // Make sure the sound's AudioBufferSourceNode has been created.\n              if (sound._node.bufferSource) {\n                if (typeof sound._node.bufferSource.stop === 'undefined') {\n                  sound._node.bufferSource.noteOff(0);\n                } else {\n                  sound._node.bufferSource.stop(0);\n                }\n\n                // Clean up the buffer source.\n                self._cleanBuffer(sound._node);\n              }\n            } else if (!isNaN(sound._node.duration) || sound._node.duration === Infinity) {\n              sound._node.currentTime = sound._start || 0;\n              sound._node.pause();\n\n              // If this is a live stream, stop download once the audio is stopped.\n              if (sound._node.duration === Infinity) {\n                self._clearSound(sound._node);\n              }\n            }\n          }\n\n          if (!internal) {\n            self._emit('stop', sound._id);\n          }\n        }\n      }\n\n      return self;\n    },\n\n    /**\n     * Mute/unmute a single sound or all sounds in this Howl group.\n     * @param  {Boolean} muted Set to true to mute and false to unmute.\n     * @param  {Number} id    The sound ID to update (omit to mute/unmute all).\n     * @return {Howl}\n     */\n    mute: function(muted, id) {\n      var self = this;\n\n      // If the sound hasn't loaded, add it to the load queue to mute when capable.\n      if (self._state !== 'loaded'|| self._playLock) {\n        self._queue.push({\n          event: 'mute',\n          action: function() {\n            self.mute(muted, id);\n          }\n        });\n\n        return self;\n      }\n\n      // If applying mute/unmute to all sounds, update the group's value.\n      if (typeof id === 'undefined') {\n        if (typeof muted === 'boolean') {\n          self._muted = muted;\n        } else {\n          return self._muted;\n        }\n      }\n\n      // If no id is passed, get all ID's to be muted.\n      var ids = self._getSoundIds(id);\n\n      for (var i=0; i<ids.length; i++) {\n        // Get the sound.\n        var sound = self._soundById(ids[i]);\n\n        if (sound) {\n          sound._muted = muted;\n\n          // Cancel active fade and set the volume to the end value.\n          if (sound._interval) {\n            self._stopFade(sound._id);\n          }\n\n          if (self._webAudio && sound._node) {\n            sound._node.gain.setValueAtTime(muted ? 0 : sound._volume, Howler.ctx.currentTime);\n          } else if (sound._node) {\n            sound._node.muted = Howler._muted ? true : muted;\n          }\n\n          self._emit('mute', sound._id);\n        }\n      }\n\n      return self;\n    },\n\n    /**\n     * Get/set the volume of this sound or of the Howl group. This method can optionally take 0, 1 or 2 arguments.\n     *   volume() -> Returns the group's volume value.\n     *   volume(id) -> Returns the sound id's current volume.\n     *   volume(vol) -> Sets the volume of all sounds in this Howl group.\n     *   volume(vol, id) -> Sets the volume of passed sound id.\n     * @return {Howl/Number} Returns self or current volume.\n     */\n    volume: function() {\n      var self = this;\n      var args = arguments;\n      var vol, id;\n\n      // Determine the values based on arguments.\n      if (args.length === 0) {\n        // Return the value of the groups' volume.\n        return self._volume;\n      } else if (args.length === 1 || args.length === 2 && typeof args[1] === 'undefined') {\n        // First check if this is an ID, and if not, assume it is a new volume.\n        var ids = self._getSoundIds();\n        var index = ids.indexOf(args[0]);\n        if (index >= 0) {\n          id = parseInt(args[0], 10);\n        } else {\n          vol = parseFloat(args[0]);\n        }\n      } else if (args.length >= 2) {\n        vol = parseFloat(args[0]);\n        id = parseInt(args[1], 10);\n      }\n\n      // Update the volume or return the current volume.\n      var sound;\n      if (typeof vol !== 'undefined' && vol >= 0 && vol <= 1) {\n        // If the sound hasn't loaded, add it to the load queue to change volume when capable.\n        if (self._state !== 'loaded'|| self._playLock) {\n          self._queue.push({\n            event: 'volume',\n            action: function() {\n              self.volume.apply(self, args);\n            }\n          });\n\n          return self;\n        }\n\n        // Set the group volume.\n        if (typeof id === 'undefined') {\n          self._volume = vol;\n        }\n\n        // Update one or all volumes.\n        id = self._getSoundIds(id);\n        for (var i=0; i<id.length; i++) {\n          // Get the sound.\n          sound = self._soundById(id[i]);\n\n          if (sound) {\n            sound._volume = vol;\n\n            // Stop currently running fades.\n            if (!args[2]) {\n              self._stopFade(id[i]);\n            }\n\n            if (self._webAudio && sound._node && !sound._muted) {\n              sound._node.gain.setValueAtTime(vol, Howler.ctx.currentTime);\n            } else if (sound._node && !sound._muted) {\n              sound._node.volume = vol * Howler.volume();\n            }\n\n            self._emit('volume', sound._id);\n          }\n        }\n      } else {\n        sound = id ? self._soundById(id) : self._sounds[0];\n        return sound ? sound._volume : 0;\n      }\n\n      return self;\n    },\n\n    /**\n     * Fade a currently playing sound between two volumes (if no id is passed, all sounds will fade).\n     * @param  {Number} from The value to fade from (0.0 to 1.0).\n     * @param  {Number} to   The volume to fade to (0.0 to 1.0).\n     * @param  {Number} len  Time in milliseconds to fade.\n     * @param  {Number} id   The sound id (omit to fade all sounds).\n     * @return {Howl}\n     */\n    fade: function(from, to, len, id) {\n      var self = this;\n\n      // If the sound hasn't loaded, add it to the load queue to fade when capable.\n      if (self._state !== 'loaded' || self._playLock) {\n        self._queue.push({\n          event: 'fade',\n          action: function() {\n            self.fade(from, to, len, id);\n          }\n        });\n\n        return self;\n      }\n\n      // Make sure the to/from/len values are numbers.\n      from = Math.min(Math.max(0, parseFloat(from)), 1);\n      to = Math.min(Math.max(0, parseFloat(to)), 1);\n      len = parseFloat(len);\n\n      // Set the volume to the start position.\n      self.volume(from, id);\n\n      // Fade the volume of one or all sounds.\n      var ids = self._getSoundIds(id);\n      for (var i=0; i<ids.length; i++) {\n        // Get the sound.\n        var sound = self._soundById(ids[i]);\n\n        // Create a linear fade or fall back to timeouts with HTML5 Audio.\n        if (sound) {\n          // Stop the previous fade if no sprite is being used (otherwise, volume handles this).\n          if (!id) {\n            self._stopFade(ids[i]);\n          }\n\n          // If we are using Web Audio, let the native methods do the actual fade.\n          if (self._webAudio && !sound._muted) {\n            var currentTime = Howler.ctx.currentTime;\n            var end = currentTime + (len / 1000);\n            sound._volume = from;\n            sound._node.gain.setValueAtTime(from, currentTime);\n            sound._node.gain.linearRampToValueAtTime(to, end);\n          }\n\n          self._startFadeInterval(sound, from, to, len, ids[i], typeof id === 'undefined');\n        }\n      }\n\n      return self;\n    },\n\n    /**\n     * Starts the internal interval to fade a sound.\n     * @param  {Object} sound Reference to sound to fade.\n     * @param  {Number} from The value to fade from (0.0 to 1.0).\n     * @param  {Number} to   The volume to fade to (0.0 to 1.0).\n     * @param  {Number} len  Time in milliseconds to fade.\n     * @param  {Number} id   The sound id to fade.\n     * @param  {Boolean} isGroup   If true, set the volume on the group.\n     */\n    _startFadeInterval: function(sound, from, to, len, id, isGroup) {\n      var self = this;\n      var vol = from;\n      var diff = to - from;\n      var steps = Math.abs(diff / 0.01);\n      var stepLen = Math.max(4, (steps > 0) ? len / steps : len);\n      var lastTick = Date.now();\n\n      // Store the value being faded to.\n      sound._fadeTo = to;\n\n      // Update the volume value on each interval tick.\n      sound._interval = setInterval(function() {\n        // Update the volume based on the time since the last tick.\n        var tick = (Date.now() - lastTick) / len;\n        lastTick = Date.now();\n        vol += diff * tick;\n\n        // Round to within 2 decimal points.\n        vol = Math.round(vol * 100) / 100;\n\n        // Make sure the volume is in the right bounds.\n        if (diff < 0) {\n          vol = Math.max(to, vol);\n        } else {\n          vol = Math.min(to, vol);\n        }\n\n        // Change the volume.\n        if (self._webAudio) {\n          sound._volume = vol;\n        } else {\n          self.volume(vol, sound._id, true);\n        }\n\n        // Set the group's volume.\n        if (isGroup) {\n          self._volume = vol;\n        }\n\n        // When the fade is complete, stop it and fire event.\n        if ((to < from && vol <= to) || (to > from && vol >= to)) {\n          clearInterval(sound._interval);\n          sound._interval = null;\n          sound._fadeTo = null;\n          self.volume(to, sound._id);\n          self._emit('fade', sound._id);\n        }\n      }, stepLen);\n    },\n\n    /**\n     * Internal method that stops the currently playing fade when\n     * a new fade starts, volume is changed or the sound is stopped.\n     * @param  {Number} id The sound id.\n     * @return {Howl}\n     */\n    _stopFade: function(id) {\n      var self = this;\n      var sound = self._soundById(id);\n\n      if (sound && sound._interval) {\n        if (self._webAudio) {\n          sound._node.gain.cancelScheduledValues(Howler.ctx.currentTime);\n        }\n\n        clearInterval(sound._interval);\n        sound._interval = null;\n        self.volume(sound._fadeTo, id);\n        sound._fadeTo = null;\n        self._emit('fade', id);\n      }\n\n      return self;\n    },\n\n    /**\n     * Get/set the loop parameter on a sound. This method can optionally take 0, 1 or 2 arguments.\n     *   loop() -> Returns the group's loop value.\n     *   loop(id) -> Returns the sound id's loop value.\n     *   loop(loop) -> Sets the loop value for all sounds in this Howl group.\n     *   loop(loop, id) -> Sets the loop value of passed sound id.\n     * @return {Howl/Boolean} Returns self or current loop value.\n     */\n    loop: function() {\n      var self = this;\n      var args = arguments;\n      var loop, id, sound;\n\n      // Determine the values for loop and id.\n      if (args.length === 0) {\n        // Return the grou's loop value.\n        return self._loop;\n      } else if (args.length === 1) {\n        if (typeof args[0] === 'boolean') {\n          loop = args[0];\n          self._loop = loop;\n        } else {\n          // Return this sound's loop value.\n          sound = self._soundById(parseInt(args[0], 10));\n          return sound ? sound._loop : false;\n        }\n      } else if (args.length === 2) {\n        loop = args[0];\n        id = parseInt(args[1], 10);\n      }\n\n      // If no id is passed, get all ID's to be looped.\n      var ids = self._getSoundIds(id);\n      for (var i=0; i<ids.length; i++) {\n        sound = self._soundById(ids[i]);\n\n        if (sound) {\n          sound._loop = loop;\n          if (self._webAudio && sound._node && sound._node.bufferSource) {\n            sound._node.bufferSource.loop = loop;\n            if (loop) {\n              sound._node.bufferSource.loopStart = sound._start || 0;\n              sound._node.bufferSource.loopEnd = sound._stop;\n\n              // If playing, restart playback to ensure looping updates.\n              if (self.playing(ids[i])) {\n                self.pause(ids[i], true);\n                self.play(ids[i], true);\n              }\n            }\n          }\n        }\n      }\n\n      return self;\n    },\n\n    /**\n     * Get/set the playback rate of a sound. This method can optionally take 0, 1 or 2 arguments.\n     *   rate() -> Returns the first sound node's current playback rate.\n     *   rate(id) -> Returns the sound id's current playback rate.\n     *   rate(rate) -> Sets the playback rate of all sounds in this Howl group.\n     *   rate(rate, id) -> Sets the playback rate of passed sound id.\n     * @return {Howl/Number} Returns self or the current playback rate.\n     */\n    rate: function() {\n      var self = this;\n      var args = arguments;\n      var rate, id;\n\n      // Determine the values based on arguments.\n      if (args.length === 0) {\n        // We will simply return the current rate of the first node.\n        id = self._sounds[0]._id;\n      } else if (args.length === 1) {\n        // First check if this is an ID, and if not, assume it is a new rate value.\n        var ids = self._getSoundIds();\n        var index = ids.indexOf(args[0]);\n        if (index >= 0) {\n          id = parseInt(args[0], 10);\n        } else {\n          rate = parseFloat(args[0]);\n        }\n      } else if (args.length === 2) {\n        rate = parseFloat(args[0]);\n        id = parseInt(args[1], 10);\n      }\n\n      // Update the playback rate or return the current value.\n      var sound;\n      if (typeof rate === 'number') {\n        // If the sound hasn't loaded, add it to the load queue to change playback rate when capable.\n        if (self._state !== 'loaded' || self._playLock) {\n          self._queue.push({\n            event: 'rate',\n            action: function() {\n              self.rate.apply(self, args);\n            }\n          });\n\n          return self;\n        }\n\n        // Set the group rate.\n        if (typeof id === 'undefined') {\n          self._rate = rate;\n        }\n\n        // Update one or all volumes.\n        id = self._getSoundIds(id);\n        for (var i=0; i<id.length; i++) {\n          // Get the sound.\n          sound = self._soundById(id[i]);\n\n          if (sound) {\n            // Keep track of our position when the rate changed and update the playback\n            // start position so we can properly adjust the seek position for time elapsed.\n            if (self.playing(id[i])) {\n              sound._rateSeek = self.seek(id[i]);\n              sound._playStart = self._webAudio ? Howler.ctx.currentTime : sound._playStart;\n            }\n            sound._rate = rate;\n\n            // Change the playback rate.\n            if (self._webAudio && sound._node && sound._node.bufferSource) {\n              sound._node.bufferSource.playbackRate.setValueAtTime(rate, Howler.ctx.currentTime);\n            } else if (sound._node) {\n              sound._node.playbackRate = rate;\n            }\n\n            // Reset the timers.\n            var seek = self.seek(id[i]);\n            var duration = ((self._sprite[sound._sprite][0] + self._sprite[sound._sprite][1]) / 1000) - seek;\n            var timeout = (duration * 1000) / Math.abs(sound._rate);\n\n            // Start a new end timer if sound is already playing.\n            if (self._endTimers[id[i]] || !sound._paused) {\n              self._clearTimer(id[i]);\n              self._endTimers[id[i]] = setTimeout(self._ended.bind(self, sound), timeout);\n            }\n\n            self._emit('rate', sound._id);\n          }\n        }\n      } else {\n        sound = self._soundById(id);\n        return sound ? sound._rate : self._rate;\n      }\n\n      return self;\n    },\n\n    /**\n     * Get/set the seek position of a sound. This method can optionally take 0, 1 or 2 arguments.\n     *   seek() -> Returns the first sound node's current seek position.\n     *   seek(id) -> Returns the sound id's current seek position.\n     *   seek(seek) -> Sets the seek position of the first sound node.\n     *   seek(seek, id) -> Sets the seek position of passed sound id.\n     * @return {Howl/Number} Returns self or the current seek position.\n     */\n    seek: function() {\n      var self = this;\n      var args = arguments;\n      var seek, id;\n\n      // Determine the values based on arguments.\n      if (args.length === 0) {\n        // We will simply return the current position of the first node.\n        if (self._sounds.length) {\n          id = self._sounds[0]._id;\n        }\n      } else if (args.length === 1) {\n        // First check if this is an ID, and if not, assume it is a new seek position.\n        var ids = self._getSoundIds();\n        var index = ids.indexOf(args[0]);\n        if (index >= 0) {\n          id = parseInt(args[0], 10);\n        } else if (self._sounds.length) {\n          id = self._sounds[0]._id;\n          seek = parseFloat(args[0]);\n        }\n      } else if (args.length === 2) {\n        seek = parseFloat(args[0]);\n        id = parseInt(args[1], 10);\n      }\n\n      // If there is no ID, bail out.\n      if (typeof id === 'undefined') {\n        return 0;\n      }\n\n      // If the sound hasn't loaded, add it to the load queue to seek when capable.\n      if (typeof seek === 'number' && (self._state !== 'loaded' || self._playLock)) {\n        self._queue.push({\n          event: 'seek',\n          action: function() {\n            self.seek.apply(self, args);\n          }\n        });\n\n        return self;\n      }\n\n      // Get the sound.\n      var sound = self._soundById(id);\n\n      if (sound) {\n        if (typeof seek === 'number' && seek >= 0) {\n          // Pause the sound and update position for restarting playback.\n          var playing = self.playing(id);\n          if (playing) {\n            self.pause(id, true);\n          }\n\n          // Move the position of the track and cancel timer.\n          sound._seek = seek;\n          sound._ended = false;\n          self._clearTimer(id);\n\n          // Update the seek position for HTML5 Audio.\n          if (!self._webAudio && sound._node && !isNaN(sound._node.duration)) {\n            sound._node.currentTime = seek;\n          }\n\n          // Seek and emit when ready.\n          var seekAndEmit = function() {\n            // Restart the playback if the sound was playing.\n            if (playing) {\n              self.play(id, true);\n            }\n\n            self._emit('seek', id);\n          };\n\n          // Wait for the play lock to be unset before emitting (HTML5 Audio).\n          if (playing && !self._webAudio) {\n            var emitSeek = function() {\n              if (!self._playLock) {\n                seekAndEmit();\n              } else {\n                setTimeout(emitSeek, 0);\n              }\n            };\n            setTimeout(emitSeek, 0);\n          } else {\n            seekAndEmit();\n          }\n        } else {\n          if (self._webAudio) {\n            var realTime = self.playing(id) ? Howler.ctx.currentTime - sound._playStart : 0;\n            var rateSeek = sound._rateSeek ? sound._rateSeek - sound._seek : 0;\n            return sound._seek + (rateSeek + realTime * Math.abs(sound._rate));\n          } else {\n            return sound._node.currentTime;\n          }\n        }\n      }\n\n      return self;\n    },\n\n    /**\n     * Check if a specific sound is currently playing or not (if id is provided), or check if at least one of the sounds in the group is playing or not.\n     * @param  {Number}  id The sound id to check. If none is passed, the whole sound group is checked.\n     * @return {Boolean} True if playing and false if not.\n     */\n    playing: function(id) {\n      var self = this;\n\n      // Check the passed sound ID (if any).\n      if (typeof id === 'number') {\n        var sound = self._soundById(id);\n        return sound ? !sound._paused : false;\n      }\n\n      // Otherwise, loop through all sounds and check if any are playing.\n      for (var i=0; i<self._sounds.length; i++) {\n        if (!self._sounds[i]._paused) {\n          return true;\n        }\n      }\n\n      return false;\n    },\n\n    /**\n     * Get the duration of this sound. Passing a sound id will return the sprite duration.\n     * @param  {Number} id The sound id to check. If none is passed, return full source duration.\n     * @return {Number} Audio duration in seconds.\n     */\n    duration: function(id) {\n      var self = this;\n      var duration = self._duration;\n\n      // If we pass an ID, get the sound and return the sprite length.\n      var sound = self._soundById(id);\n      if (sound) {\n        duration = self._sprite[sound._sprite][1] / 1000;\n      }\n\n      return duration;\n    },\n\n    /**\n     * Returns the current loaded state of this Howl.\n     * @return {String} 'unloaded', 'loading', 'loaded'\n     */\n    state: function() {\n      return this._state;\n    },\n\n    /**\n     * Unload and destroy the current Howl object.\n     * This will immediately stop all sound instances attached to this group.\n     */\n    unload: function() {\n      var self = this;\n\n      // Stop playing any active sounds.\n      var sounds = self._sounds;\n      for (var i=0; i<sounds.length; i++) {\n        // Stop the sound if it is currently playing.\n        if (!sounds[i]._paused) {\n          self.stop(sounds[i]._id);\n        }\n\n        // Remove the source or disconnect.\n        if (!self._webAudio) {\n          // Set the source to 0-second silence to stop any downloading (except in IE).\n          self._clearSound(sounds[i]._node);\n\n          // Remove any event listeners.\n          sounds[i]._node.removeEventListener('error', sounds[i]._errorFn, false);\n          sounds[i]._node.removeEventListener(Howler._canPlayEvent, sounds[i]._loadFn, false);\n          sounds[i]._node.removeEventListener('ended', sounds[i]._endFn, false);\n\n          // Release the Audio object back to the pool.\n          Howler._releaseHtml5Audio(sounds[i]._node);\n        }\n\n        // Empty out all of the nodes.\n        delete sounds[i]._node;\n\n        // Make sure all timers are cleared out.\n        self._clearTimer(sounds[i]._id);\n      }\n\n      // Remove the references in the global Howler object.\n      var index = Howler._howls.indexOf(self);\n      if (index >= 0) {\n        Howler._howls.splice(index, 1);\n      }\n\n      // Delete this sound from the cache (if no other Howl is using it).\n      var remCache = true;\n      for (i=0; i<Howler._howls.length; i++) {\n        if (Howler._howls[i]._src === self._src || self._src.indexOf(Howler._howls[i]._src) >= 0) {\n          remCache = false;\n          break;\n        }\n      }\n\n      if (cache && remCache) {\n        delete cache[self._src];\n      }\n\n      // Clear global errors.\n      Howler.noAudio = false;\n\n      // Clear out `self`.\n      self._state = 'unloaded';\n      self._sounds = [];\n      self = null;\n\n      return null;\n    },\n\n    /**\n     * Listen to a custom event.\n     * @param  {String}   event Event name.\n     * @param  {Function} fn    Listener to call.\n     * @param  {Number}   id    (optional) Only listen to events for this sound.\n     * @param  {Number}   once  (INTERNAL) Marks event to fire only once.\n     * @return {Howl}\n     */\n    on: function(event, fn, id, once) {\n      var self = this;\n      var events = self['_on' + event];\n\n      if (typeof fn === 'function') {\n        events.push(once ? {id: id, fn: fn, once: once} : {id: id, fn: fn});\n      }\n\n      return self;\n    },\n\n    /**\n     * Remove a custom event. Call without parameters to remove all events.\n     * @param  {String}   event Event name.\n     * @param  {Function} fn    Listener to remove. Leave empty to remove all.\n     * @param  {Number}   id    (optional) Only remove events for this sound.\n     * @return {Howl}\n     */\n    off: function(event, fn, id) {\n      var self = this;\n      var events = self['_on' + event];\n      var i = 0;\n\n      // Allow passing just an event and ID.\n      if (typeof fn === 'number') {\n        id = fn;\n        fn = null;\n      }\n\n      if (fn || id) {\n        // Loop through event store and remove the passed function.\n        for (i=0; i<events.length; i++) {\n          var isId = (id === events[i].id);\n          if (fn === events[i].fn && isId || !fn && isId) {\n            events.splice(i, 1);\n            break;\n          }\n        }\n      } else if (event) {\n        // Clear out all events of this type.\n        self['_on' + event] = [];\n      } else {\n        // Clear out all events of every type.\n        var keys = Object.keys(self);\n        for (i=0; i<keys.length; i++) {\n          if ((keys[i].indexOf('_on') === 0) && Array.isArray(self[keys[i]])) {\n            self[keys[i]] = [];\n          }\n        }\n      }\n\n      return self;\n    },\n\n    /**\n     * Listen to a custom event and remove it once fired.\n     * @param  {String}   event Event name.\n     * @param  {Function} fn    Listener to call.\n     * @param  {Number}   id    (optional) Only listen to events for this sound.\n     * @return {Howl}\n     */\n    once: function(event, fn, id) {\n      var self = this;\n\n      // Setup the event listener.\n      self.on(event, fn, id, 1);\n\n      return self;\n    },\n\n    /**\n     * Emit all events of a specific type and pass the sound id.\n     * @param  {String} event Event name.\n     * @param  {Number} id    Sound ID.\n     * @param  {Number} msg   Message to go with event.\n     * @return {Howl}\n     */\n    _emit: function(event, id, msg) {\n      var self = this;\n      var events = self['_on' + event];\n\n      // Loop through event store and fire all functions.\n      for (var i=events.length-1; i>=0; i--) {\n        // Only fire the listener if the correct ID is used.\n        if (!events[i].id || events[i].id === id || event === 'load') {\n          setTimeout(function(fn) {\n            fn.call(this, id, msg);\n          }.bind(self, events[i].fn), 0);\n\n          // If this event was setup with `once`, remove it.\n          if (events[i].once) {\n            self.off(event, events[i].fn, events[i].id);\n          }\n        }\n      }\n\n      // Pass the event type into load queue so that it can continue stepping.\n      self._loadQueue(event);\n\n      return self;\n    },\n\n    /**\n     * Queue of actions initiated before the sound has loaded.\n     * These will be called in sequence, with the next only firing\n     * after the previous has finished executing (even if async like play).\n     * @return {Howl}\n     */\n    _loadQueue: function(event) {\n      var self = this;\n\n      if (self._queue.length > 0) {\n        var task = self._queue[0];\n\n        // Remove this task if a matching event was passed.\n        if (task.event === event) {\n          self._queue.shift();\n          self._loadQueue();\n        }\n\n        // Run the task if no event type is passed.\n        if (!event) {\n          task.action();\n        }\n      }\n\n      return self;\n    },\n\n    /**\n     * Fired when playback ends at the end of the duration.\n     * @param  {Sound} sound The sound object to work with.\n     * @return {Howl}\n     */\n    _ended: function(sound) {\n      var self = this;\n      var sprite = sound._sprite;\n\n      // If we are using IE and there was network latency we may be clipping\n      // audio before it completes playing. Lets check the node to make sure it\n      // believes it has completed, before ending the playback.\n      if (!self._webAudio && sound._node && !sound._node.paused && !sound._node.ended && sound._node.currentTime < sound._stop) {\n        setTimeout(self._ended.bind(self, sound), 100);\n        return self;\n      }\n\n      // Should this sound loop?\n      var loop = !!(sound._loop || self._sprite[sprite][2]);\n\n      // Fire the ended event.\n      self._emit('end', sound._id);\n\n      // Restart the playback for HTML5 Audio loop.\n      if (!self._webAudio && loop) {\n        self.stop(sound._id, true).play(sound._id);\n      }\n\n      // Restart this timer if on a Web Audio loop.\n      if (self._webAudio && loop) {\n        self._emit('play', sound._id);\n        sound._seek = sound._start || 0;\n        sound._rateSeek = 0;\n        sound._playStart = Howler.ctx.currentTime;\n\n        var timeout = ((sound._stop - sound._start) * 1000) / Math.abs(sound._rate);\n        self._endTimers[sound._id] = setTimeout(self._ended.bind(self, sound), timeout);\n      }\n\n      // Mark the node as paused.\n      if (self._webAudio && !loop) {\n        sound._paused = true;\n        sound._ended = true;\n        sound._seek = sound._start || 0;\n        sound._rateSeek = 0;\n        self._clearTimer(sound._id);\n\n        // Clean up the buffer source.\n        self._cleanBuffer(sound._node);\n\n        // Attempt to auto-suspend AudioContext if no sounds are still playing.\n        Howler._autoSuspend();\n      }\n\n      // When using a sprite, end the track.\n      if (!self._webAudio && !loop) {\n        self.stop(sound._id, true);\n      }\n\n      return self;\n    },\n\n    /**\n     * Clear the end timer for a sound playback.\n     * @param  {Number} id The sound ID.\n     * @return {Howl}\n     */\n    _clearTimer: function(id) {\n      var self = this;\n\n      if (self._endTimers[id]) {\n        // Clear the timeout or remove the ended listener.\n        if (typeof self._endTimers[id] !== 'function') {\n          clearTimeout(self._endTimers[id]);\n        } else {\n          var sound = self._soundById(id);\n          if (sound && sound._node) {\n            sound._node.removeEventListener('ended', self._endTimers[id], false);\n          }\n        }\n\n        delete self._endTimers[id];\n      }\n\n      return self;\n    },\n\n    /**\n     * Return the sound identified by this ID, or return null.\n     * @param  {Number} id Sound ID\n     * @return {Object}    Sound object or null.\n     */\n    _soundById: function(id) {\n      var self = this;\n\n      // Loop through all sounds and find the one with this ID.\n      for (var i=0; i<self._sounds.length; i++) {\n        if (id === self._sounds[i]._id) {\n          return self._sounds[i];\n        }\n      }\n\n      return null;\n    },\n\n    /**\n     * Return an inactive sound from the pool or create a new one.\n     * @return {Sound} Sound playback object.\n     */\n    _inactiveSound: function() {\n      var self = this;\n\n      self._drain();\n\n      // Find the first inactive node to recycle.\n      for (var i=0; i<self._sounds.length; i++) {\n        if (self._sounds[i]._ended) {\n          return self._sounds[i].reset();\n        }\n      }\n\n      // If no inactive node was found, create a new one.\n      return new Sound(self);\n    },\n\n    /**\n     * Drain excess inactive sounds from the pool.\n     */\n    _drain: function() {\n      var self = this;\n      var limit = self._pool;\n      var cnt = 0;\n      var i = 0;\n\n      // If there are less sounds than the max pool size, we are done.\n      if (self._sounds.length < limit) {\n        return;\n      }\n\n      // Count the number of inactive sounds.\n      for (i=0; i<self._sounds.length; i++) {\n        if (self._sounds[i]._ended) {\n          cnt++;\n        }\n      }\n\n      // Remove excess inactive sounds, going in reverse order.\n      for (i=self._sounds.length - 1; i>=0; i--) {\n        if (cnt <= limit) {\n          return;\n        }\n\n        if (self._sounds[i]._ended) {\n          // Disconnect the audio source when using Web Audio.\n          if (self._webAudio && self._sounds[i]._node) {\n            self._sounds[i]._node.disconnect(0);\n          }\n\n          // Remove sounds until we have the pool size.\n          self._sounds.splice(i, 1);\n          cnt--;\n        }\n      }\n    },\n\n    /**\n     * Get all ID's from the sounds pool.\n     * @param  {Number} id Only return one ID if one is passed.\n     * @return {Array}    Array of IDs.\n     */\n    _getSoundIds: function(id) {\n      var self = this;\n\n      if (typeof id === 'undefined') {\n        var ids = [];\n        for (var i=0; i<self._sounds.length; i++) {\n          ids.push(self._sounds[i]._id);\n        }\n\n        return ids;\n      } else {\n        return [id];\n      }\n    },\n\n    /**\n     * Load the sound back into the buffer source.\n     * @param  {Sound} sound The sound object to work with.\n     * @return {Howl}\n     */\n    _refreshBuffer: function(sound) {\n      var self = this;\n\n      // Setup the buffer source for playback.\n      sound._node.bufferSource = Howler.ctx.createBufferSource();\n      sound._node.bufferSource.buffer = cache[self._src];\n\n      // Connect to the correct node.\n      if (sound._panner) {\n        sound._node.bufferSource.connect(sound._panner);\n      } else {\n        sound._node.bufferSource.connect(sound._node);\n      }\n\n      // Setup looping and playback rate.\n      sound._node.bufferSource.loop = sound._loop;\n      if (sound._loop) {\n        sound._node.bufferSource.loopStart = sound._start || 0;\n        sound._node.bufferSource.loopEnd = sound._stop || 0;\n      }\n      sound._node.bufferSource.playbackRate.setValueAtTime(sound._rate, Howler.ctx.currentTime);\n\n      return self;\n    },\n\n    /**\n     * Prevent memory leaks by cleaning up the buffer source after playback.\n     * @param  {Object} node Sound's audio node containing the buffer source.\n     * @return {Howl}\n     */\n    _cleanBuffer: function(node) {\n      var self = this;\n      var isIOS = Howler._navigator && Howler._navigator.vendor.indexOf('Apple') >= 0;\n\n      if (!node.bufferSource) {\n        return self;\n      }\n\n      if (Howler._scratchBuffer && node.bufferSource) {\n        node.bufferSource.onended = null;\n        node.bufferSource.disconnect(0);\n        if (isIOS) {\n          try { node.bufferSource.buffer = Howler._scratchBuffer; } catch(e) {}\n        }\n      }\n      node.bufferSource = null;\n\n      return self;\n    },\n\n    /**\n     * Set the source to a 0-second silence to stop any downloading (except in IE).\n     * @param  {Object} node Audio node to clear.\n     */\n    _clearSound: function(node) {\n      var checkIE = /MSIE |Trident\\//.test(Howler._navigator && Howler._navigator.userAgent);\n      if (!checkIE) {\n        node.src = 'data:audio/wav;base64,UklGRigAAABXQVZFZm10IBIAAAABAAEARKwAAIhYAQACABAAAABkYXRhAgAAAAEA';\n      }\n    }\n  };\n\n  /** Single Sound Methods **/\n  /***************************************************************************/\n\n  /**\n   * Setup the sound object, which each node attached to a Howl group is contained in.\n   * @param {Object} howl The Howl parent group.\n   */\n  var Sound = function(howl) {\n    this._parent = howl;\n    this.init();\n  };\n  Sound.prototype = {\n    /**\n     * Initialize a new Sound object.\n     * @return {Sound}\n     */\n    init: function() {\n      var self = this;\n      var parent = self._parent;\n\n      // Setup the default parameters.\n      self._muted = parent._muted;\n      self._loop = parent._loop;\n      self._volume = parent._volume;\n      self._rate = parent._rate;\n      self._seek = 0;\n      self._paused = true;\n      self._ended = true;\n      self._sprite = '__default';\n\n      // Generate a unique ID for this sound.\n      self._id = ++Howler._counter;\n\n      // Add itself to the parent's pool.\n      parent._sounds.push(self);\n\n      // Create the new node.\n      self.create();\n\n      return self;\n    },\n\n    /**\n     * Create and setup a new sound object, whether HTML5 Audio or Web Audio.\n     * @return {Sound}\n     */\n    create: function() {\n      var self = this;\n      var parent = self._parent;\n      var volume = (Howler._muted || self._muted || self._parent._muted) ? 0 : self._volume;\n\n      if (parent._webAudio) {\n        // Create the gain node for controlling volume (the source will connect to this).\n        self._node = (typeof Howler.ctx.createGain === 'undefined') ? Howler.ctx.createGainNode() : Howler.ctx.createGain();\n        self._node.gain.setValueAtTime(volume, Howler.ctx.currentTime);\n        self._node.paused = true;\n        self._node.connect(Howler.masterGain);\n      } else if (!Howler.noAudio) {\n        // Get an unlocked Audio object from the pool.\n        self._node = Howler._obtainHtml5Audio();\n\n        // Listen for errors (http://dev.w3.org/html5/spec-author-view/spec.html#mediaerror).\n        self._errorFn = self._errorListener.bind(self);\n        self._node.addEventListener('error', self._errorFn, false);\n\n        // Listen for 'canplaythrough' event to let us know the sound is ready.\n        self._loadFn = self._loadListener.bind(self);\n        self._node.addEventListener(Howler._canPlayEvent, self._loadFn, false);\n\n        // Listen for the 'ended' event on the sound to account for edge-case where\n        // a finite sound has a duration of Infinity.\n        self._endFn = self._endListener.bind(self);\n        self._node.addEventListener('ended', self._endFn, false);\n\n        // Setup the new audio node.\n        self._node.src = parent._src;\n        self._node.preload = parent._preload === true ? 'auto' : parent._preload;\n        self._node.volume = volume * Howler.volume();\n\n        // Begin loading the source.\n        self._node.load();\n      }\n\n      return self;\n    },\n\n    /**\n     * Reset the parameters of this sound to the original state (for recycle).\n     * @return {Sound}\n     */\n    reset: function() {\n      var self = this;\n      var parent = self._parent;\n\n      // Reset all of the parameters of this sound.\n      self._muted = parent._muted;\n      self._loop = parent._loop;\n      self._volume = parent._volume;\n      self._rate = parent._rate;\n      self._seek = 0;\n      self._rateSeek = 0;\n      self._paused = true;\n      self._ended = true;\n      self._sprite = '__default';\n\n      // Generate a new ID so that it isn't confused with the previous sound.\n      self._id = ++Howler._counter;\n\n      return self;\n    },\n\n    /**\n     * HTML5 Audio error listener callback.\n     */\n    _errorListener: function() {\n      var self = this;\n\n      // Fire an error event and pass back the code.\n      self._parent._emit('loaderror', self._id, self._node.error ? self._node.error.code : 0);\n\n      // Clear the event listener.\n      self._node.removeEventListener('error', self._errorFn, false);\n    },\n\n    /**\n     * HTML5 Audio canplaythrough listener callback.\n     */\n    _loadListener: function() {\n      var self = this;\n      var parent = self._parent;\n\n      // Round up the duration to account for the lower precision in HTML5 Audio.\n      parent._duration = Math.ceil(self._node.duration * 10) / 10;\n\n      // Setup a sprite if none is defined.\n      if (Object.keys(parent._sprite).length === 0) {\n        parent._sprite = {__default: [0, parent._duration * 1000]};\n      }\n\n      if (parent._state !== 'loaded') {\n        parent._state = 'loaded';\n        parent._emit('load');\n        parent._loadQueue();\n      }\n\n      // Clear the event listener.\n      self._node.removeEventListener(Howler._canPlayEvent, self._loadFn, false);\n    },\n\n    /**\n     * HTML5 Audio ended listener callback.\n     */\n    _endListener: function() {\n      var self = this;\n      var parent = self._parent;\n\n      // Only handle the `ended`` event if the duration is Infinity.\n      if (parent._duration === Infinity) {\n        // Update the parent duration to match the real audio duration.\n        // Round up the duration to account for the lower precision in HTML5 Audio.\n        parent._duration = Math.ceil(self._node.duration * 10) / 10;\n\n        // Update the sprite that corresponds to the real duration.\n        if (parent._sprite.__default[1] === Infinity) {\n          parent._sprite.__default[1] = parent._duration * 1000;\n        }\n\n        // Run the regular ended method.\n        parent._ended(self);\n      }\n\n      // Clear the event listener since the duration is now correct.\n      self._node.removeEventListener('ended', self._endFn, false);\n    }\n  };\n\n  /** Helper Methods **/\n  /***************************************************************************/\n\n  var cache = {};\n\n  /**\n   * Buffer a sound from URL, Data URI or cache and decode to audio source (Web Audio API).\n   * @param  {Howl} self\n   */\n  var loadBuffer = function(self) {\n    var url = self._src;\n\n    // Check if the buffer has already been cached and use it instead.\n    if (cache[url]) {\n      // Set the duration from the cache.\n      self._duration = cache[url].duration;\n\n      // Load the sound into this Howl.\n      loadSound(self);\n\n      return;\n    }\n\n    if (/^data:[^;]+;base64,/.test(url)) {\n      // Decode the base64 data URI without XHR, since some browsers don't support it.\n      var data = atob(url.split(',')[1]);\n      var dataView = new Uint8Array(data.length);\n      for (var i=0; i<data.length; ++i) {\n        dataView[i] = data.charCodeAt(i);\n      }\n\n      decodeAudioData(dataView.buffer, self);\n    } else {\n      // Load the buffer from the URL.\n      var xhr = new XMLHttpRequest();\n      xhr.open(self._xhr.method, url, true);\n      xhr.withCredentials = self._xhr.withCredentials;\n      xhr.responseType = 'arraybuffer';\n\n      // Apply any custom headers to the request.\n      if (self._xhr.headers) {\n        Object.keys(self._xhr.headers).forEach(function(key) {\n          xhr.setRequestHeader(key, self._xhr.headers[key]);\n        });\n      }\n\n      xhr.onload = function() {\n        // Make sure we get a successful response back.\n        var code = (xhr.status + '')[0];\n        if (code !== '0' && code !== '2' && code !== '3') {\n          self._emit('loaderror', null, 'Failed loading audio file with status: ' + xhr.status + '.');\n          return;\n        }\n\n        decodeAudioData(xhr.response, self);\n      };\n      xhr.onerror = function() {\n        // If there is an error, switch to HTML5 Audio.\n        if (self._webAudio) {\n          self._html5 = true;\n          self._webAudio = false;\n          self._sounds = [];\n          delete cache[url];\n          self.load();\n        }\n      };\n      safeXhrSend(xhr);\n    }\n  };\n\n  /**\n   * Send the XHR request wrapped in a try/catch.\n   * @param  {Object} xhr XHR to send.\n   */\n  var safeXhrSend = function(xhr) {\n    try {\n      xhr.send();\n    } catch (e) {\n      xhr.onerror();\n    }\n  };\n\n  /**\n   * Decode audio data from an array buffer.\n   * @param  {ArrayBuffer} arraybuffer The audio data.\n   * @param  {Howl}        self\n   */\n  var decodeAudioData = function(arraybuffer, self) {\n    // Fire a load error if something broke.\n    var error = function() {\n      self._emit('loaderror', null, 'Decoding audio data failed.');\n    };\n\n    // Load the sound on success.\n    var success = function(buffer) {\n      if (buffer && self._sounds.length > 0) {\n        cache[self._src] = buffer;\n        loadSound(self, buffer);\n      } else {\n        error();\n      }\n    };\n\n    // Decode the buffer into an audio source.\n    if (typeof Promise !== 'undefined' && Howler.ctx.decodeAudioData.length === 1) {\n      Howler.ctx.decodeAudioData(arraybuffer).then(success).catch(error);\n    } else {\n      Howler.ctx.decodeAudioData(arraybuffer, success, error);\n    }\n  }\n\n  /**\n   * Sound is now loaded, so finish setting everything up and fire the loaded event.\n   * @param  {Howl} self\n   * @param  {Object} buffer The decoded buffer sound source.\n   */\n  var loadSound = function(self, buffer) {\n    // Set the duration.\n    if (buffer && !self._duration) {\n      self._duration = buffer.duration;\n    }\n\n    // Setup a sprite if none is defined.\n    if (Object.keys(self._sprite).length === 0) {\n      self._sprite = {__default: [0, self._duration * 1000]};\n    }\n\n    // Fire the loaded event.\n    if (self._state !== 'loaded') {\n      self._state = 'loaded';\n      self._emit('load');\n      self._loadQueue();\n    }\n  };\n\n  /**\n   * Setup the audio context when available, or switch to HTML5 Audio mode.\n   */\n  var setupAudioContext = function() {\n    // If we have already detected that Web Audio isn't supported, don't run this step again.\n    if (!Howler.usingWebAudio) {\n      return;\n    }\n\n    // Check if we are using Web Audio and setup the AudioContext if we are.\n    try {\n      if (typeof AudioContext !== 'undefined') {\n        Howler.ctx = new AudioContext();\n      } else if (typeof webkitAudioContext !== 'undefined') {\n        Howler.ctx = new webkitAudioContext();\n      } else {\n        Howler.usingWebAudio = false;\n      }\n    } catch(e) {\n      Howler.usingWebAudio = false;\n    }\n\n    // If the audio context creation still failed, set using web audio to false.\n    if (!Howler.ctx) {\n      Howler.usingWebAudio = false;\n    }\n\n    // Check if a webview is being used on iOS8 or earlier (rather than the browser).\n    // If it is, disable Web Audio as it causes crashing.\n    var iOS = (/iP(hone|od|ad)/.test(Howler._navigator && Howler._navigator.platform));\n    var appVersion = Howler._navigator && Howler._navigator.appVersion.match(/OS (\\d+)_(\\d+)_?(\\d+)?/);\n    var version = appVersion ? parseInt(appVersion[1], 10) : null;\n    if (iOS && version && version < 9) {\n      var safari = /safari/.test(Howler._navigator && Howler._navigator.userAgent.toLowerCase());\n      if (Howler._navigator && !safari) {\n        Howler.usingWebAudio = false;\n      }\n    }\n\n    // Create and expose the master GainNode when using Web Audio (useful for plugins or advanced usage).\n    if (Howler.usingWebAudio) {\n      Howler.masterGain = (typeof Howler.ctx.createGain === 'undefined') ? Howler.ctx.createGainNode() : Howler.ctx.createGain();\n      Howler.masterGain.gain.setValueAtTime(Howler._muted ? 0 : Howler._volume, Howler.ctx.currentTime);\n      Howler.masterGain.connect(Howler.ctx.destination);\n    }\n\n    // Re-run the setup on Howler.\n    Howler._setup();\n  };\n\n  // Add support for AMD (Asynchronous Module Definition) libraries such as require.js.\n  if (typeof define === 'function' && define.amd) {\n    define([], function() {\n      return {\n        Howler: Howler,\n        Howl: Howl\n      };\n    });\n  }\n\n  // Add support for CommonJS libraries such as browserify.\n  if (typeof exports !== 'undefined') {\n    exports.Howler = Howler;\n    exports.Howl = Howl;\n  }\n\n  // Add to global in Node.js (for testing, etc).\n  if (typeof global !== 'undefined') {\n    global.HowlerGlobal = HowlerGlobal;\n    global.Howler = Howler;\n    global.Howl = Howl;\n    global.Sound = Sound;\n  } else if (typeof window !== 'undefined') {  // Define globally in case AMD is not available or unused.\n    window.HowlerGlobal = HowlerGlobal;\n    window.Howler = Howler;\n    window.Howl = Howl;\n    window.Sound = Sound;\n  }\n})();\n\n\n/*!\n *  Spatial Plugin - Adds support for stereo and 3D audio where Web Audio is supported.\n *  \n *  howler.js v2.2.4\n *  howlerjs.com\n *\n *  (c) 2013-2020, James Simpson of GoldFire Studios\n *  goldfirestudios.com\n *\n *  MIT License\n */\n\n(function() {\n\n  'use strict';\n\n  // Setup default properties.\n  HowlerGlobal.prototype._pos = [0, 0, 0];\n  HowlerGlobal.prototype._orientation = [0, 0, -1, 0, 1, 0];\n\n  /** Global Methods **/\n  /***************************************************************************/\n\n  /**\n   * Helper method to update the stereo panning position of all current Howls.\n   * Future Howls will not use this value unless explicitly set.\n   * @param  {Number} pan A value of -1.0 is all the way left and 1.0 is all the way right.\n   * @return {Howler/Number}     Self or current stereo panning value.\n   */\n  HowlerGlobal.prototype.stereo = function(pan) {\n    var self = this;\n\n    // Stop right here if not using Web Audio.\n    if (!self.ctx || !self.ctx.listener) {\n      return self;\n    }\n\n    // Loop through all Howls and update their stereo panning.\n    for (var i=self._howls.length-1; i>=0; i--) {\n      self._howls[i].stereo(pan);\n    }\n\n    return self;\n  };\n\n  /**\n   * Get/set the position of the listener in 3D cartesian space. Sounds using\n   * 3D position will be relative to the listener's position.\n   * @param  {Number} x The x-position of the listener.\n   * @param  {Number} y The y-position of the listener.\n   * @param  {Number} z The z-position of the listener.\n   * @return {Howler/Array}   Self or current listener position.\n   */\n  HowlerGlobal.prototype.pos = function(x, y, z) {\n    var self = this;\n\n    // Stop right here if not using Web Audio.\n    if (!self.ctx || !self.ctx.listener) {\n      return self;\n    }\n\n    // Set the defaults for optional 'y' & 'z'.\n    y = (typeof y !== 'number') ? self._pos[1] : y;\n    z = (typeof z !== 'number') ? self._pos[2] : z;\n\n    if (typeof x === 'number') {\n      self._pos = [x, y, z];\n\n      if (typeof self.ctx.listener.positionX !== 'undefined') {\n        self.ctx.listener.positionX.setTargetAtTime(self._pos[0], Howler.ctx.currentTime, 0.1);\n        self.ctx.listener.positionY.setTargetAtTime(self._pos[1], Howler.ctx.currentTime, 0.1);\n        self.ctx.listener.positionZ.setTargetAtTime(self._pos[2], Howler.ctx.currentTime, 0.1);\n      } else {\n        self.ctx.listener.setPosition(self._pos[0], self._pos[1], self._pos[2]);\n      }\n    } else {\n      return self._pos;\n    }\n\n    return self;\n  };\n\n  /**\n   * Get/set the direction the listener is pointing in the 3D cartesian space.\n   * A front and up vector must be provided. The front is the direction the\n   * face of the listener is pointing, and up is the direction the top of the\n   * listener is pointing. Thus, these values are expected to be at right angles\n   * from each other.\n   * @param  {Number} x   The x-orientation of the listener.\n   * @param  {Number} y   The y-orientation of the listener.\n   * @param  {Number} z   The z-orientation of the listener.\n   * @param  {Number} xUp The x-orientation of the top of the listener.\n   * @param  {Number} yUp The y-orientation of the top of the listener.\n   * @param  {Number} zUp The z-orientation of the top of the listener.\n   * @return {Howler/Array}     Returns self or the current orientation vectors.\n   */\n  HowlerGlobal.prototype.orientation = function(x, y, z, xUp, yUp, zUp) {\n    var self = this;\n\n    // Stop right here if not using Web Audio.\n    if (!self.ctx || !self.ctx.listener) {\n      return self;\n    }\n\n    // Set the defaults for optional 'y' & 'z'.\n    var or = self._orientation;\n    y = (typeof y !== 'number') ? or[1] : y;\n    z = (typeof z !== 'number') ? or[2] : z;\n    xUp = (typeof xUp !== 'number') ? or[3] : xUp;\n    yUp = (typeof yUp !== 'number') ? or[4] : yUp;\n    zUp = (typeof zUp !== 'number') ? or[5] : zUp;\n\n    if (typeof x === 'number') {\n      self._orientation = [x, y, z, xUp, yUp, zUp];\n\n      if (typeof self.ctx.listener.forwardX !== 'undefined') {\n        self.ctx.listener.forwardX.setTargetAtTime(x, Howler.ctx.currentTime, 0.1);\n        self.ctx.listener.forwardY.setTargetAtTime(y, Howler.ctx.currentTime, 0.1);\n        self.ctx.listener.forwardZ.setTargetAtTime(z, Howler.ctx.currentTime, 0.1);\n        self.ctx.listener.upX.setTargetAtTime(xUp, Howler.ctx.currentTime, 0.1);\n        self.ctx.listener.upY.setTargetAtTime(yUp, Howler.ctx.currentTime, 0.1);\n        self.ctx.listener.upZ.setTargetAtTime(zUp, Howler.ctx.currentTime, 0.1);\n      } else {\n        self.ctx.listener.setOrientation(x, y, z, xUp, yUp, zUp);\n      }\n    } else {\n      return or;\n    }\n\n    return self;\n  };\n\n  /** Group Methods **/\n  /***************************************************************************/\n\n  /**\n   * Add new properties to the core init.\n   * @param  {Function} _super Core init method.\n   * @return {Howl}\n   */\n  Howl.prototype.init = (function(_super) {\n    return function(o) {\n      var self = this;\n\n      // Setup user-defined default properties.\n      self._orientation = o.orientation || [1, 0, 0];\n      self._stereo = o.stereo || null;\n      self._pos = o.pos || null;\n      self._pannerAttr = {\n        coneInnerAngle: typeof o.coneInnerAngle !== 'undefined' ? o.coneInnerAngle : 360,\n        coneOuterAngle: typeof o.coneOuterAngle !== 'undefined' ? o.coneOuterAngle : 360,\n        coneOuterGain: typeof o.coneOuterGain !== 'undefined' ? o.coneOuterGain : 0,\n        distanceModel: typeof o.distanceModel !== 'undefined' ? o.distanceModel : 'inverse',\n        maxDistance: typeof o.maxDistance !== 'undefined' ? o.maxDistance : 10000,\n        panningModel: typeof o.panningModel !== 'undefined' ? o.panningModel : 'HRTF',\n        refDistance: typeof o.refDistance !== 'undefined' ? o.refDistance : 1,\n        rolloffFactor: typeof o.rolloffFactor !== 'undefined' ? o.rolloffFactor : 1\n      };\n\n      // Setup event listeners.\n      self._onstereo = o.onstereo ? [{fn: o.onstereo}] : [];\n      self._onpos = o.onpos ? [{fn: o.onpos}] : [];\n      self._onorientation = o.onorientation ? [{fn: o.onorientation}] : [];\n\n      // Complete initilization with howler.js core's init function.\n      return _super.call(this, o);\n    };\n  })(Howl.prototype.init);\n\n  /**\n   * Get/set the stereo panning of the audio source for this sound or all in the group.\n   * @param  {Number} pan  A value of -1.0 is all the way left and 1.0 is all the way right.\n   * @param  {Number} id (optional) The sound ID. If none is passed, all in group will be updated.\n   * @return {Howl/Number}    Returns self or the current stereo panning value.\n   */\n  Howl.prototype.stereo = function(pan, id) {\n    var self = this;\n\n    // Stop right here if not using Web Audio.\n    if (!self._webAudio) {\n      return self;\n    }\n\n    // If the sound hasn't loaded, add it to the load queue to change stereo pan when capable.\n    if (self._state !== 'loaded') {\n      self._queue.push({\n        event: 'stereo',\n        action: function() {\n          self.stereo(pan, id);\n        }\n      });\n\n      return self;\n    }\n\n    // Check for PannerStereoNode support and fallback to PannerNode if it doesn't exist.\n    var pannerType = (typeof Howler.ctx.createStereoPanner === 'undefined') ? 'spatial' : 'stereo';\n\n    // Setup the group's stereo panning if no ID is passed.\n    if (typeof id === 'undefined') {\n      // Return the group's stereo panning if no parameters are passed.\n      if (typeof pan === 'number') {\n        self._stereo = pan;\n        self._pos = [pan, 0, 0];\n      } else {\n        return self._stereo;\n      }\n    }\n\n    // Change the streo panning of one or all sounds in group.\n    var ids = self._getSoundIds(id);\n    for (var i=0; i<ids.length; i++) {\n      // Get the sound.\n      var sound = self._soundById(ids[i]);\n\n      if (sound) {\n        if (typeof pan === 'number') {\n          sound._stereo = pan;\n          sound._pos = [pan, 0, 0];\n\n          if (sound._node) {\n            // If we are falling back, make sure the panningModel is equalpower.\n            sound._pannerAttr.panningModel = 'equalpower';\n\n            // Check if there is a panner setup and create a new one if not.\n            if (!sound._panner || !sound._panner.pan) {\n              setupPanner(sound, pannerType);\n            }\n\n            if (pannerType === 'spatial') {\n              if (typeof sound._panner.positionX !== 'undefined') {\n                sound._panner.positionX.setValueAtTime(pan, Howler.ctx.currentTime);\n                sound._panner.positionY.setValueAtTime(0, Howler.ctx.currentTime);\n                sound._panner.positionZ.setValueAtTime(0, Howler.ctx.currentTime);\n              } else {\n                sound._panner.setPosition(pan, 0, 0);\n              }\n            } else {\n              sound._panner.pan.setValueAtTime(pan, Howler.ctx.currentTime);\n            }\n          }\n\n          self._emit('stereo', sound._id);\n        } else {\n          return sound._stereo;\n        }\n      }\n    }\n\n    return self;\n  };\n\n  /**\n   * Get/set the 3D spatial position of the audio source for this sound or group relative to the global listener.\n   * @param  {Number} x  The x-position of the audio source.\n   * @param  {Number} y  The y-position of the audio source.\n   * @param  {Number} z  The z-position of the audio source.\n   * @param  {Number} id (optional) The sound ID. If none is passed, all in group will be updated.\n   * @return {Howl/Array}    Returns self or the current 3D spatial position: [x, y, z].\n   */\n  Howl.prototype.pos = function(x, y, z, id) {\n    var self = this;\n\n    // Stop right here if not using Web Audio.\n    if (!self._webAudio) {\n      return self;\n    }\n\n    // If the sound hasn't loaded, add it to the load queue to change position when capable.\n    if (self._state !== 'loaded') {\n      self._queue.push({\n        event: 'pos',\n        action: function() {\n          self.pos(x, y, z, id);\n        }\n      });\n\n      return self;\n    }\n\n    // Set the defaults for optional 'y' & 'z'.\n    y = (typeof y !== 'number') ? 0 : y;\n    z = (typeof z !== 'number') ? -0.5 : z;\n\n    // Setup the group's spatial position if no ID is passed.\n    if (typeof id === 'undefined') {\n      // Return the group's spatial position if no parameters are passed.\n      if (typeof x === 'number') {\n        self._pos = [x, y, z];\n      } else {\n        return self._pos;\n      }\n    }\n\n    // Change the spatial position of one or all sounds in group.\n    var ids = self._getSoundIds(id);\n    for (var i=0; i<ids.length; i++) {\n      // Get the sound.\n      var sound = self._soundById(ids[i]);\n\n      if (sound) {\n        if (typeof x === 'number') {\n          sound._pos = [x, y, z];\n\n          if (sound._node) {\n            // Check if there is a panner setup and create a new one if not.\n            if (!sound._panner || sound._panner.pan) {\n              setupPanner(sound, 'spatial');\n            }\n\n            if (typeof sound._panner.positionX !== 'undefined') {\n              sound._panner.positionX.setValueAtTime(x, Howler.ctx.currentTime);\n              sound._panner.positionY.setValueAtTime(y, Howler.ctx.currentTime);\n              sound._panner.positionZ.setValueAtTime(z, Howler.ctx.currentTime);\n            } else {\n              sound._panner.setPosition(x, y, z);\n            }\n          }\n\n          self._emit('pos', sound._id);\n        } else {\n          return sound._pos;\n        }\n      }\n    }\n\n    return self;\n  };\n\n  /**\n   * Get/set the direction the audio source is pointing in the 3D cartesian coordinate\n   * space. Depending on how direction the sound is, based on the `cone` attributes,\n   * a sound pointing away from the listener can be quiet or silent.\n   * @param  {Number} x  The x-orientation of the source.\n   * @param  {Number} y  The y-orientation of the source.\n   * @param  {Number} z  The z-orientation of the source.\n   * @param  {Number} id (optional) The sound ID. If none is passed, all in group will be updated.\n   * @return {Howl/Array}    Returns self or the current 3D spatial orientation: [x, y, z].\n   */\n  Howl.prototype.orientation = function(x, y, z, id) {\n    var self = this;\n\n    // Stop right here if not using Web Audio.\n    if (!self._webAudio) {\n      return self;\n    }\n\n    // If the sound hasn't loaded, add it to the load queue to change orientation when capable.\n    if (self._state !== 'loaded') {\n      self._queue.push({\n        event: 'orientation',\n        action: function() {\n          self.orientation(x, y, z, id);\n        }\n      });\n\n      return self;\n    }\n\n    // Set the defaults for optional 'y' & 'z'.\n    y = (typeof y !== 'number') ? self._orientation[1] : y;\n    z = (typeof z !== 'number') ? self._orientation[2] : z;\n\n    // Setup the group's spatial orientation if no ID is passed.\n    if (typeof id === 'undefined') {\n      // Return the group's spatial orientation if no parameters are passed.\n      if (typeof x === 'number') {\n        self._orientation = [x, y, z];\n      } else {\n        return self._orientation;\n      }\n    }\n\n    // Change the spatial orientation of one or all sounds in group.\n    var ids = self._getSoundIds(id);\n    for (var i=0; i<ids.length; i++) {\n      // Get the sound.\n      var sound = self._soundById(ids[i]);\n\n      if (sound) {\n        if (typeof x === 'number') {\n          sound._orientation = [x, y, z];\n\n          if (sound._node) {\n            // Check if there is a panner setup and create a new one if not.\n            if (!sound._panner) {\n              // Make sure we have a position to setup the node with.\n              if (!sound._pos) {\n                sound._pos = self._pos || [0, 0, -0.5];\n              }\n\n              setupPanner(sound, 'spatial');\n            }\n\n            if (typeof sound._panner.orientationX !== 'undefined') {\n              sound._panner.orientationX.setValueAtTime(x, Howler.ctx.currentTime);\n              sound._panner.orientationY.setValueAtTime(y, Howler.ctx.currentTime);\n              sound._panner.orientationZ.setValueAtTime(z, Howler.ctx.currentTime);\n            } else {\n              sound._panner.setOrientation(x, y, z);\n            }\n          }\n\n          self._emit('orientation', sound._id);\n        } else {\n          return sound._orientation;\n        }\n      }\n    }\n\n    return self;\n  };\n\n  /**\n   * Get/set the panner node's attributes for a sound or group of sounds.\n   * This method can optionall take 0, 1 or 2 arguments.\n   *   pannerAttr() -> Returns the group's values.\n   *   pannerAttr(id) -> Returns the sound id's values.\n   *   pannerAttr(o) -> Set's the values of all sounds in this Howl group.\n   *   pannerAttr(o, id) -> Set's the values of passed sound id.\n   *\n   *   Attributes:\n   *     coneInnerAngle - (360 by default) A parameter for directional audio sources, this is an angle, in degrees,\n   *                      inside of which there will be no volume reduction.\n   *     coneOuterAngle - (360 by default) A parameter for directional audio sources, this is an angle, in degrees,\n   *                      outside of which the volume will be reduced to a constant value of `coneOuterGain`.\n   *     coneOuterGain - (0 by default) A parameter for directional audio sources, this is the gain outside of the\n   *                     `coneOuterAngle`. It is a linear value in the range `[0, 1]`.\n   *     distanceModel - ('inverse' by default) Determines algorithm used to reduce volume as audio moves away from\n   *                     listener. Can be `linear`, `inverse` or `exponential.\n   *     maxDistance - (10000 by default) The maximum distance between source and listener, after which the volume\n   *                   will not be reduced any further.\n   *     refDistance - (1 by default) A reference distance for reducing volume as source moves further from the listener.\n   *                   This is simply a variable of the distance model and has a different effect depending on which model\n   *                   is used and the scale of your coordinates. Generally, volume will be equal to 1 at this distance.\n   *     rolloffFactor - (1 by default) How quickly the volume reduces as source moves from listener. This is simply a\n   *                     variable of the distance model and can be in the range of `[0, 1]` with `linear` and `[0, ∞]`\n   *                     with `inverse` and `exponential`.\n   *     panningModel - ('HRTF' by default) Determines which spatialization algorithm is used to position audio.\n   *                     Can be `HRTF` or `equalpower`.\n   *\n   * @return {Howl/Object} Returns self or current panner attributes.\n   */\n  Howl.prototype.pannerAttr = function() {\n    var self = this;\n    var args = arguments;\n    var o, id, sound;\n\n    // Stop right here if not using Web Audio.\n    if (!self._webAudio) {\n      return self;\n    }\n\n    // Determine the values based on arguments.\n    if (args.length === 0) {\n      // Return the group's panner attribute values.\n      return self._pannerAttr;\n    } else if (args.length === 1) {\n      if (typeof args[0] === 'object') {\n        o = args[0];\n\n        // Set the grou's panner attribute values.\n        if (typeof id === 'undefined') {\n          if (!o.pannerAttr) {\n            o.pannerAttr = {\n              coneInnerAngle: o.coneInnerAngle,\n              coneOuterAngle: o.coneOuterAngle,\n              coneOuterGain: o.coneOuterGain,\n              distanceModel: o.distanceModel,\n              maxDistance: o.maxDistance,\n              refDistance: o.refDistance,\n              rolloffFactor: o.rolloffFactor,\n              panningModel: o.panningModel\n            };\n          }\n\n          self._pannerAttr = {\n            coneInnerAngle: typeof o.pannerAttr.coneInnerAngle !== 'undefined' ? o.pannerAttr.coneInnerAngle : self._coneInnerAngle,\n            coneOuterAngle: typeof o.pannerAttr.coneOuterAngle !== 'undefined' ? o.pannerAttr.coneOuterAngle : self._coneOuterAngle,\n            coneOuterGain: typeof o.pannerAttr.coneOuterGain !== 'undefined' ? o.pannerAttr.coneOuterGain : self._coneOuterGain,\n            distanceModel: typeof o.pannerAttr.distanceModel !== 'undefined' ? o.pannerAttr.distanceModel : self._distanceModel,\n            maxDistance: typeof o.pannerAttr.maxDistance !== 'undefined' ? o.pannerAttr.maxDistance : self._maxDistance,\n            refDistance: typeof o.pannerAttr.refDistance !== 'undefined' ? o.pannerAttr.refDistance : self._refDistance,\n            rolloffFactor: typeof o.pannerAttr.rolloffFactor !== 'undefined' ? o.pannerAttr.rolloffFactor : self._rolloffFactor,\n            panningModel: typeof o.pannerAttr.panningModel !== 'undefined' ? o.pannerAttr.panningModel : self._panningModel\n          };\n        }\n      } else {\n        // Return this sound's panner attribute values.\n        sound = self._soundById(parseInt(args[0], 10));\n        return sound ? sound._pannerAttr : self._pannerAttr;\n      }\n    } else if (args.length === 2) {\n      o = args[0];\n      id = parseInt(args[1], 10);\n    }\n\n    // Update the values of the specified sounds.\n    var ids = self._getSoundIds(id);\n    for (var i=0; i<ids.length; i++) {\n      sound = self._soundById(ids[i]);\n\n      if (sound) {\n        // Merge the new values into the sound.\n        var pa = sound._pannerAttr;\n        pa = {\n          coneInnerAngle: typeof o.coneInnerAngle !== 'undefined' ? o.coneInnerAngle : pa.coneInnerAngle,\n          coneOuterAngle: typeof o.coneOuterAngle !== 'undefined' ? o.coneOuterAngle : pa.coneOuterAngle,\n          coneOuterGain: typeof o.coneOuterGain !== 'undefined' ? o.coneOuterGain : pa.coneOuterGain,\n          distanceModel: typeof o.distanceModel !== 'undefined' ? o.distanceModel : pa.distanceModel,\n          maxDistance: typeof o.maxDistance !== 'undefined' ? o.maxDistance : pa.maxDistance,\n          refDistance: typeof o.refDistance !== 'undefined' ? o.refDistance : pa.refDistance,\n          rolloffFactor: typeof o.rolloffFactor !== 'undefined' ? o.rolloffFactor : pa.rolloffFactor,\n          panningModel: typeof o.panningModel !== 'undefined' ? o.panningModel : pa.panningModel\n        };\n\n        // Create a new panner node if one doesn't already exist.\n        var panner = sound._panner;\n        if (!panner) {\n          // Make sure we have a position to setup the node with.\n          if (!sound._pos) {\n            sound._pos = self._pos || [0, 0, -0.5];\n          }\n\n          // Create a new panner node.\n          setupPanner(sound, 'spatial');\n          panner = sound._panner\n        }\n\n        // Update the panner values or create a new panner if none exists.\n        panner.coneInnerAngle = pa.coneInnerAngle;\n        panner.coneOuterAngle = pa.coneOuterAngle;\n        panner.coneOuterGain = pa.coneOuterGain;\n        panner.distanceModel = pa.distanceModel;\n        panner.maxDistance = pa.maxDistance;\n        panner.refDistance = pa.refDistance;\n        panner.rolloffFactor = pa.rolloffFactor;\n        panner.panningModel = pa.panningModel;\n      }\n    }\n\n    return self;\n  };\n\n  /** Single Sound Methods **/\n  /***************************************************************************/\n\n  /**\n   * Add new properties to the core Sound init.\n   * @param  {Function} _super Core Sound init method.\n   * @return {Sound}\n   */\n  Sound.prototype.init = (function(_super) {\n    return function() {\n      var self = this;\n      var parent = self._parent;\n\n      // Setup user-defined default properties.\n      self._orientation = parent._orientation;\n      self._stereo = parent._stereo;\n      self._pos = parent._pos;\n      self._pannerAttr = parent._pannerAttr;\n\n      // Complete initilization with howler.js core Sound's init function.\n      _super.call(this);\n\n      // If a stereo or position was specified, set it up.\n      if (self._stereo) {\n        parent.stereo(self._stereo);\n      } else if (self._pos) {\n        parent.pos(self._pos[0], self._pos[1], self._pos[2], self._id);\n      }\n    };\n  })(Sound.prototype.init);\n\n  /**\n   * Override the Sound.reset method to clean up properties from the spatial plugin.\n   * @param  {Function} _super Sound reset method.\n   * @return {Sound}\n   */\n  Sound.prototype.reset = (function(_super) {\n    return function() {\n      var self = this;\n      var parent = self._parent;\n\n      // Reset all spatial plugin properties on this sound.\n      self._orientation = parent._orientation;\n      self._stereo = parent._stereo;\n      self._pos = parent._pos;\n      self._pannerAttr = parent._pannerAttr;\n\n      // If a stereo or position was specified, set it up.\n      if (self._stereo) {\n        parent.stereo(self._stereo);\n      } else if (self._pos) {\n        parent.pos(self._pos[0], self._pos[1], self._pos[2], self._id);\n      } else if (self._panner) {\n        // Disconnect the panner.\n        self._panner.disconnect(0);\n        self._panner = undefined;\n        parent._refreshBuffer(self);\n      }\n\n      // Complete resetting of the sound.\n      return _super.call(this);\n    };\n  })(Sound.prototype.reset);\n\n  /** Helper Methods **/\n  /***************************************************************************/\n\n  /**\n   * Create a new panner node and save it on the sound.\n   * @param  {Sound} sound Specific sound to setup panning on.\n   * @param {String} type Type of panner to create: 'stereo' or 'spatial'.\n   */\n  var setupPanner = function(sound, type) {\n    type = type || 'spatial';\n\n    // Create the new panner node.\n    if (type === 'spatial') {\n      sound._panner = Howler.ctx.createPanner();\n      sound._panner.coneInnerAngle = sound._pannerAttr.coneInnerAngle;\n      sound._panner.coneOuterAngle = sound._pannerAttr.coneOuterAngle;\n      sound._panner.coneOuterGain = sound._pannerAttr.coneOuterGain;\n      sound._panner.distanceModel = sound._pannerAttr.distanceModel;\n      sound._panner.maxDistance = sound._pannerAttr.maxDistance;\n      sound._panner.refDistance = sound._pannerAttr.refDistance;\n      sound._panner.rolloffFactor = sound._pannerAttr.rolloffFactor;\n      sound._panner.panningModel = sound._pannerAttr.panningModel;\n\n      if (typeof sound._panner.positionX !== 'undefined') {\n        sound._panner.positionX.setValueAtTime(sound._pos[0], Howler.ctx.currentTime);\n        sound._panner.positionY.setValueAtTime(sound._pos[1], Howler.ctx.currentTime);\n        sound._panner.positionZ.setValueAtTime(sound._pos[2], Howler.ctx.currentTime);\n      } else {\n        sound._panner.setPosition(sound._pos[0], sound._pos[1], sound._pos[2]);\n      }\n\n      if (typeof sound._panner.orientationX !== 'undefined') {\n        sound._panner.orientationX.setValueAtTime(sound._orientation[0], Howler.ctx.currentTime);\n        sound._panner.orientationY.setValueAtTime(sound._orientation[1], Howler.ctx.currentTime);\n        sound._panner.orientationZ.setValueAtTime(sound._orientation[2], Howler.ctx.currentTime);\n      } else {\n        sound._panner.setOrientation(sound._orientation[0], sound._orientation[1], sound._orientation[2]);\n      }\n    } else {\n      sound._panner = Howler.ctx.createStereoPanner();\n      sound._panner.pan.setValueAtTime(sound._stereo, Howler.ctx.currentTime);\n    }\n\n    sound._panner.connect(sound._node);\n\n    // Update the connections.\n    if (!sound._paused) {\n      sound._parent.pause(sound._id, true).play(sound._id, true);\n    }\n  };\n})();\n"], "names": ["HowlerGlobal", "self", "Howler", "vol", "setupAudioContext", "i", "ids", "j", "sound", "muted", "ext", "test", "audioTest", "mpegTest", "ua", "checkOpera", "isOldOpera", "checkSafari", "safariVersion", "isOldSafari", "unlock", "e", "audioNode", "source", "testPlay", "audio", "handleSuspension", "Howl", "o", "url", "str", "Sound", "loadBuffer", "sprite", "internal", "id", "num", "soundId", "seek", "duration", "timeout", "start", "stop", "setParams", "node", "playWebAudio", "playHtml5", "play", "err", "loadedNoReadyState", "listener", "args", "index", "from", "to", "len", "currentTime", "end", "isGroup", "diff", "steps", "step<PERSON>en", "lastTick", "tick", "loop", "rate", "playing", "seekAndEmit", "emitSeek", "realTime", "rateSeek", "sounds", "rem<PERSON><PERSON>", "cache", "event", "fn", "once", "events", "isId", "keys", "msg", "task", "limit", "cnt", "isIOS", "checkIE", "howl", "parent", "volume", "loadSound", "data", "dataView", "decodeAudioData", "xhr", "key", "code", "safeXhrSend", "arraybuffer", "error", "success", "buffer", "iOS", "appVersion", "version", "safari", "exports", "global", "pan", "x", "y", "z", "xUp", "yUp", "zUp", "or", "_super", "pannerType", "setupPanner", "pa", "panner", "type"], "mappings": ";;;;;;;;kDAUC,UAAW,CAWV,IAAIA,EAAe,UAAW,CAC5B,KAAK,KAAM,CACZ,EACDA,EAAa,UAAY,CAKvB,KAAM,UAAW,CACf,IAAIC,EAAO,MAAQC,EAGnB,OAAAD,EAAK,SAAW,IAGhBA,EAAK,gBAAkB,CAAE,EACzBA,EAAK,cAAgB,GAGrBA,EAAK,QAAU,CAAE,EACjBA,EAAK,OAAS,CAAE,EAChBA,EAAK,OAAS,GACdA,EAAK,QAAU,EACfA,EAAK,cAAgB,iBACrBA,EAAK,WAAc,OAAO,OAAW,KAAe,OAAO,UAAa,OAAO,UAAY,KAG3FA,EAAK,WAAa,KAClBA,EAAK,QAAU,GACfA,EAAK,cAAgB,GACrBA,EAAK,YAAc,GACnBA,EAAK,IAAM,KAGXA,EAAK,WAAa,GAGlBA,EAAK,OAAQ,EAENA,CACR,EAOD,OAAQ,SAASE,EAAK,CACpB,IAAIF,EAAO,MAAQC,EAQnB,GAPAC,EAAM,WAAWA,CAAG,EAGfF,EAAK,KACRG,EAAmB,EAGjB,OAAOD,EAAQ,KAAeA,GAAO,GAAKA,GAAO,EAAG,CAItD,GAHAF,EAAK,QAAUE,EAGXF,EAAK,OACP,OAAOA,EAILA,EAAK,eACPA,EAAK,WAAW,KAAK,eAAeE,EAAKD,EAAO,IAAI,WAAW,EAIjE,QAASG,EAAE,EAAGA,EAAEJ,EAAK,OAAO,OAAQI,IAClC,GAAI,CAACJ,EAAK,OAAOI,CAAC,EAAE,UAKlB,QAHIC,EAAML,EAAK,OAAOI,CAAC,EAAE,aAAc,EAG9BE,EAAE,EAAGA,EAAED,EAAI,OAAQC,IAAK,CAC/B,IAAIC,EAAQP,EAAK,OAAOI,CAAC,EAAE,WAAWC,EAAIC,CAAC,CAAC,EAExCC,GAASA,EAAM,QACjBA,EAAM,MAAM,OAASA,EAAM,QAAUL,EAErD,CAIQ,OAAOF,CACf,CAEM,OAAOA,EAAK,OACb,EAMD,KAAM,SAASQ,EAAO,CACpB,IAAIR,EAAO,MAAQC,EAGdD,EAAK,KACRG,EAAmB,EAGrBH,EAAK,OAASQ,EAGVR,EAAK,eACPA,EAAK,WAAW,KAAK,eAAeQ,EAAQ,EAAIR,EAAK,QAASC,EAAO,IAAI,WAAW,EAItF,QAASG,EAAE,EAAGA,EAAEJ,EAAK,OAAO,OAAQI,IAClC,GAAI,CAACJ,EAAK,OAAOI,CAAC,EAAE,UAKlB,QAHIC,EAAML,EAAK,OAAOI,CAAC,EAAE,aAAc,EAG9BE,EAAE,EAAGA,EAAED,EAAI,OAAQC,IAAK,CAC/B,IAAIC,EAAQP,EAAK,OAAOI,CAAC,EAAE,WAAWC,EAAIC,CAAC,CAAC,EAExCC,GAASA,EAAM,QACjBA,EAAM,MAAM,MAASC,EAAS,GAAOD,EAAM,OAEzD,CAIM,OAAOP,CACR,EAKD,KAAM,UAAW,CAIf,QAHIA,EAAO,MAAQC,EAGVG,EAAE,EAAGA,EAAEJ,EAAK,OAAO,OAAQI,IAClCJ,EAAK,OAAOI,CAAC,EAAE,KAAM,EAGvB,OAAOJ,CACR,EAMD,OAAQ,UAAW,CAGjB,QAFIA,EAAO,MAAQC,EAEVG,EAAEJ,EAAK,OAAO,OAAO,EAAGI,GAAG,EAAGA,IACrCJ,EAAK,OAAOI,CAAC,EAAE,OAAQ,EAIzB,OAAIJ,EAAK,eAAiBA,EAAK,KAAO,OAAOA,EAAK,IAAI,MAAU,MAC9DA,EAAK,IAAI,MAAO,EAChBA,EAAK,IAAM,KACXG,EAAmB,GAGdH,CACR,EAOD,OAAQ,SAASS,EAAK,CACpB,OAAQ,MAAQR,GAAQ,QAAQQ,EAAI,QAAQ,MAAO,EAAE,CAAC,CACvD,EAMD,OAAQ,UAAW,CACjB,IAAIT,EAAO,MAAQC,EASnB,GANAD,EAAK,MAAQA,EAAK,KAAMA,EAAK,IAAI,OAAS,YAG1CA,EAAK,aAAc,EAGf,CAACA,EAAK,cAER,GAAI,OAAO,MAAU,IACnB,GAAI,CACF,IAAIU,EAAO,IAAI,MAGX,OAAOA,EAAK,iBAAqB,MACnCV,EAAK,cAAgB,UAExB,MAAU,CACTA,EAAK,QAAU,EAC3B,MAEUA,EAAK,QAAU,GAKnB,GAAI,CACF,IAAIU,EAAO,IAAI,MACXA,EAAK,QACPV,EAAK,QAAU,GAElB,MAAW,CAAA,CAGZ,OAAKA,EAAK,SACRA,EAAK,aAAc,EAGdA,CACR,EAMD,aAAc,UAAW,CACvB,IAAIA,EAAO,MAAQC,EACfU,EAAY,KAGhB,GAAI,CACFA,EAAa,OAAO,MAAU,IAAe,IAAI,MAAU,IAC5D,MAAa,CACZ,OAAOX,CACf,CAEM,GAAI,CAACW,GAAa,OAAOA,EAAU,aAAgB,WACjD,OAAOX,EAGT,IAAIY,EAAWD,EAAU,YAAY,aAAa,EAAE,QAAQ,OAAQ,EAAE,EAGlEE,EAAKb,EAAK,WAAaA,EAAK,WAAW,UAAY,GACnDc,EAAaD,EAAG,MAAM,aAAa,EACnCE,EAAcD,GAAc,SAASA,EAAW,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,EAAG,EAAE,EAAI,GACxEE,EAAcH,EAAG,QAAQ,QAAQ,IAAM,IAAMA,EAAG,QAAQ,QAAQ,IAAM,GACtEI,EAAgBJ,EAAG,MAAM,iBAAiB,EAC1CK,EAAeF,GAAeC,GAAiB,SAASA,EAAc,CAAC,EAAG,EAAE,EAAI,GAEpF,OAAAjB,EAAK,QAAU,CACb,IAAK,CAAC,EAAE,CAACe,IAAeH,GAAYD,EAAU,YAAY,YAAY,EAAE,QAAQ,OAAQ,EAAE,IAC1F,KAAM,CAAC,CAACC,EACR,KAAM,CAAC,CAACD,EAAU,YAAY,0BAA0B,EAAE,QAAQ,OAAQ,EAAE,EAC5E,IAAK,CAAC,CAACA,EAAU,YAAY,4BAA4B,EAAE,QAAQ,OAAQ,EAAE,EAC7E,IAAK,CAAC,CAACA,EAAU,YAAY,4BAA4B,EAAE,QAAQ,OAAQ,EAAE,EAC7E,IAAK,CAAC,EAAEA,EAAU,YAAY,uBAAuB,GAAKA,EAAU,YAAY,WAAW,GAAG,QAAQ,OAAQ,EAAE,EAChH,IAAK,CAAC,CAACA,EAAU,YAAY,YAAY,EAAE,QAAQ,OAAQ,EAAE,EAC7D,IAAK,CAAC,CAACA,EAAU,YAAY,cAAc,EAAE,QAAQ,OAAQ,EAAE,EAC/D,IAAK,CAAC,EAAEA,EAAU,YAAY,cAAc,GAAKA,EAAU,YAAY,YAAY,GAAKA,EAAU,YAAY,YAAY,GAAG,QAAQ,OAAQ,EAAE,EAC/I,IAAK,CAAC,EAAEA,EAAU,YAAY,cAAc,GAAKA,EAAU,YAAY,YAAY,GAAKA,EAAU,YAAY,YAAY,GAAG,QAAQ,OAAQ,EAAE,EAC/I,IAAK,CAAC,EAAEA,EAAU,YAAY,cAAc,GAAKA,EAAU,YAAY,YAAY,GAAKA,EAAU,YAAY,YAAY,GAAG,QAAQ,OAAQ,EAAE,EAC/I,KAAM,CAAC,EAAE,CAACO,GAAeP,EAAU,YAAY,6BAA6B,EAAE,QAAQ,OAAQ,EAAE,GAChG,KAAM,CAAC,EAAE,CAACO,GAAeP,EAAU,YAAY,6BAA6B,EAAE,QAAQ,OAAQ,EAAE,GAChG,MAAO,CAAC,CAACA,EAAU,YAAY,0BAA0B,EAAE,QAAQ,OAAQ,EAAE,EAC7E,KAAM,CAAC,EAAEA,EAAU,YAAY,eAAe,GAAKA,EAAU,YAAY,aAAa,GAAG,QAAQ,OAAQ,EAAE,CAC5G,EAEMX,CACR,EAQD,aAAc,UAAW,CACvB,IAAIA,EAAO,MAAQC,EAGnB,GAAI,EAAAD,EAAK,gBAAkB,CAACA,EAAK,KAIjC,CAAAA,EAAK,eAAiB,GACtBA,EAAK,WAAa,GAKd,CAACA,EAAK,iBAAmBA,EAAK,IAAI,aAAe,QACnDA,EAAK,gBAAkB,GACvBA,EAAK,OAAQ,GAKfA,EAAK,eAAiBA,EAAK,IAAI,aAAa,EAAG,EAAG,KAAK,EAKvD,IAAImB,EAAS,SAASC,EAAG,CAOvB,KAAOpB,EAAK,gBAAgB,OAASA,EAAK,eACxC,GAAI,CACF,IAAIqB,EAAY,IAAI,MAIpBA,EAAU,UAAY,GAGtBrB,EAAK,mBAAmBqB,CAAS,CAClC,MAAW,CACVrB,EAAK,QAAU,GACf,KACZ,CAIQ,QAASI,EAAE,EAAGA,EAAEJ,EAAK,OAAO,OAAQI,IAClC,GAAI,CAACJ,EAAK,OAAOI,CAAC,EAAE,UAKlB,QAHIC,EAAML,EAAK,OAAOI,CAAC,EAAE,aAAc,EAG9BE,EAAE,EAAGA,EAAED,EAAI,OAAQC,IAAK,CAC/B,IAAIC,EAAQP,EAAK,OAAOI,CAAC,EAAE,WAAWC,EAAIC,CAAC,CAAC,EAExCC,GAASA,EAAM,OAAS,CAACA,EAAM,MAAM,YACvCA,EAAM,MAAM,UAAY,GACxBA,EAAM,MAAM,KAAM,EAElC,CAKQP,EAAK,YAAa,EAGlB,IAAIsB,EAAStB,EAAK,IAAI,mBAAoB,EAC1CsB,EAAO,OAAStB,EAAK,eACrBsB,EAAO,QAAQtB,EAAK,IAAI,WAAW,EAG/B,OAAOsB,EAAO,MAAU,IAC1BA,EAAO,OAAO,CAAC,EAEfA,EAAO,MAAM,CAAC,EAIZ,OAAOtB,EAAK,IAAI,QAAW,YAC7BA,EAAK,IAAI,OAAQ,EAInBsB,EAAO,QAAU,UAAW,CAC1BA,EAAO,WAAW,CAAC,EAGnBtB,EAAK,eAAiB,GAGtB,SAAS,oBAAoB,aAAcmB,EAAQ,EAAI,EACvD,SAAS,oBAAoB,WAAYA,EAAQ,EAAI,EACrD,SAAS,oBAAoB,QAASA,EAAQ,EAAI,EAClD,SAAS,oBAAoB,UAAWA,EAAQ,EAAI,EAGpD,QAASf,EAAE,EAAGA,EAAEJ,EAAK,OAAO,OAAQI,IAClCJ,EAAK,OAAOI,CAAC,EAAE,MAAM,QAAQ,CAEhC,CACF,EAGD,gBAAS,iBAAiB,aAAce,EAAQ,EAAI,EACpD,SAAS,iBAAiB,WAAYA,EAAQ,EAAI,EAClD,SAAS,iBAAiB,QAASA,EAAQ,EAAI,EAC/C,SAAS,iBAAiB,UAAWA,EAAQ,EAAI,EAE1CnB,EACR,EAOD,kBAAmB,UAAW,CAC5B,IAAIA,EAAO,MAAQC,EAGnB,GAAID,EAAK,gBAAgB,OACvB,OAAOA,EAAK,gBAAgB,IAAK,EAInC,IAAIuB,EAAW,IAAI,MAAO,EAAC,KAAM,EACjC,OAAIA,GAAY,OAAO,QAAY,MAAgBA,aAAoB,SAAW,OAAOA,EAAS,MAAS,aACzGA,EAAS,MAAM,UAAW,CACxB,QAAQ,KAAK,wEAAwE,CAC/F,CAAS,EAGI,IAAI,KACZ,EAMD,mBAAoB,SAASC,EAAO,CAClC,IAAIxB,EAAO,MAAQC,EAGnB,OAAIuB,EAAM,WACRxB,EAAK,gBAAgB,KAAKwB,CAAK,EAG1BxB,CACR,EAOD,aAAc,UAAW,CACvB,IAAIA,EAAO,KAEX,GAAI,GAACA,EAAK,aAAe,CAACA,EAAK,KAAO,OAAOA,EAAK,IAAI,QAAY,KAAe,CAACC,EAAO,eAKzF,SAASG,EAAE,EAAGA,EAAEJ,EAAK,OAAO,OAAQI,IAClC,GAAIJ,EAAK,OAAOI,CAAC,EAAE,WACjB,QAASE,EAAE,EAAGA,EAAEN,EAAK,OAAOI,CAAC,EAAE,QAAQ,OAAQE,IAC7C,GAAI,CAACN,EAAK,OAAOI,CAAC,EAAE,QAAQE,CAAC,EAAE,QAC7B,OAAON,EAMf,OAAIA,EAAK,eACP,aAAaA,EAAK,aAAa,EAIjCA,EAAK,cAAgB,WAAW,UAAW,CACzC,GAAKA,EAAK,YAIV,CAAAA,EAAK,cAAgB,KACrBA,EAAK,MAAQ,aAGb,IAAIyB,EAAmB,UAAW,CAChCzB,EAAK,MAAQ,YAETA,EAAK,sBACP,OAAOA,EAAK,oBACZA,EAAK,YAAa,EAErB,EAIDA,EAAK,IAAI,QAAO,EAAG,KAAKyB,EAAkBA,CAAgB,EAC3D,EAAE,GAAK,EAEDzB,EACR,EAMD,YAAa,UAAW,CACtB,IAAIA,EAAO,KAEX,GAAI,GAACA,EAAK,KAAO,OAAOA,EAAK,IAAI,OAAW,KAAe,CAACC,EAAO,eAInE,OAAID,EAAK,QAAU,WAAaA,EAAK,IAAI,QAAU,eAAiBA,EAAK,eACvE,aAAaA,EAAK,aAAa,EAC/BA,EAAK,cAAgB,MACZA,EAAK,QAAU,aAAeA,EAAK,QAAU,WAAaA,EAAK,IAAI,QAAU,eACtFA,EAAK,IAAI,OAAQ,EAAC,KAAK,UAAW,CAChCA,EAAK,MAAQ,UAGb,QAASI,EAAE,EAAGA,EAAEJ,EAAK,OAAO,OAAQI,IAClCJ,EAAK,OAAOI,CAAC,EAAE,MAAM,QAAQ,CAEzC,CAAS,EAEGJ,EAAK,gBACP,aAAaA,EAAK,aAAa,EAC/BA,EAAK,cAAgB,OAEdA,EAAK,QAAU,eACxBA,EAAK,oBAAsB,IAGtBA,CACb,CACG,EAGD,IAAIC,EAAS,IAAIF,EASb2B,EAAO,SAASC,EAAG,CACrB,IAAI3B,EAAO,KAGX,GAAI,CAAC2B,EAAE,KAAOA,EAAE,IAAI,SAAW,EAAG,CAChC,QAAQ,MAAM,4DAA4D,EAC1E,MACN,CAEI3B,EAAK,KAAK2B,CAAC,CACZ,EACDD,EAAK,UAAY,CAMf,KAAM,SAASC,EAAG,CAChB,IAAI3B,EAAO,KAGX,OAAKC,EAAO,KACVE,EAAmB,EAIrBH,EAAK,UAAY2B,EAAE,UAAY,GAC/B3B,EAAK,QAAW,OAAO2B,EAAE,QAAW,SAAYA,EAAE,OAAS,CAACA,EAAE,MAAM,EACpE3B,EAAK,OAAS2B,EAAE,OAAS,GACzB3B,EAAK,OAAS2B,EAAE,MAAQ,GACxB3B,EAAK,MAAQ2B,EAAE,MAAQ,GACvB3B,EAAK,MAAQ2B,EAAE,MAAQ,EACvB3B,EAAK,SAAY,OAAO2B,EAAE,SAAY,WAAaA,EAAE,UAAY,WAAcA,EAAE,QAAU,GAC3F3B,EAAK,MAAQ2B,EAAE,MAAQ,EACvB3B,EAAK,QAAU2B,EAAE,QAAU,CAAE,EAC7B3B,EAAK,KAAQ,OAAO2B,EAAE,KAAQ,SAAYA,EAAE,IAAM,CAACA,EAAE,GAAG,EACxD3B,EAAK,QAAU2B,EAAE,SAAW,OAAYA,EAAE,OAAS,EACnD3B,EAAK,KAAO,CACV,OAAQ2B,EAAE,KAAOA,EAAE,IAAI,OAASA,EAAE,IAAI,OAAS,MAC/C,QAASA,EAAE,KAAOA,EAAE,IAAI,QAAUA,EAAE,IAAI,QAAU,KAClD,gBAAiBA,EAAE,KAAOA,EAAE,IAAI,gBAAkBA,EAAE,IAAI,gBAAkB,EAC3E,EAGD3B,EAAK,UAAY,EACjBA,EAAK,OAAS,WACdA,EAAK,QAAU,CAAE,EACjBA,EAAK,WAAa,CAAE,EACpBA,EAAK,OAAS,CAAE,EAChBA,EAAK,UAAY,GAGjBA,EAAK,OAAS2B,EAAE,MAAQ,CAAC,CAAC,GAAIA,EAAE,KAAK,CAAC,EAAI,CAAE,EAC5C3B,EAAK,QAAU2B,EAAE,OAAS,CAAC,CAAC,GAAIA,EAAE,MAAM,CAAC,EAAI,CAAE,EAC/C3B,EAAK,QAAU2B,EAAE,OAAS,CAAC,CAAC,GAAIA,EAAE,MAAM,CAAC,EAAI,CAAE,EAC/C3B,EAAK,aAAe2B,EAAE,YAAc,CAAC,CAAC,GAAIA,EAAE,WAAW,CAAC,EAAI,CAAE,EAC9D3B,EAAK,aAAe2B,EAAE,YAAc,CAAC,CAAC,GAAIA,EAAE,WAAW,CAAC,EAAI,CAAE,EAC9D3B,EAAK,SAAW2B,EAAE,QAAU,CAAC,CAAC,GAAIA,EAAE,OAAO,CAAC,EAAI,CAAE,EAClD3B,EAAK,QAAU2B,EAAE,OAAS,CAAC,CAAC,GAAIA,EAAE,MAAM,CAAC,EAAI,CAAE,EAC/C3B,EAAK,QAAU2B,EAAE,OAAS,CAAC,CAAC,GAAIA,EAAE,MAAM,CAAC,EAAI,CAAE,EAC/C3B,EAAK,QAAU2B,EAAE,OAAS,CAAC,CAAC,GAAIA,EAAE,MAAM,CAAC,EAAI,CAAE,EAC/C3B,EAAK,UAAY2B,EAAE,SAAW,CAAC,CAAC,GAAIA,EAAE,QAAQ,CAAC,EAAI,CAAE,EACrD3B,EAAK,QAAU2B,EAAE,OAAS,CAAC,CAAC,GAAIA,EAAE,MAAM,CAAC,EAAI,CAAE,EAC/C3B,EAAK,QAAU2B,EAAE,OAAS,CAAC,CAAC,GAAIA,EAAE,MAAM,CAAC,EAAI,CAAE,EAC/C3B,EAAK,UAAY2B,EAAE,SAAW,CAAC,CAAC,GAAIA,EAAE,QAAQ,CAAC,EAAI,CAAE,EACrD3B,EAAK,UAAY,CAAE,EAGnBA,EAAK,UAAYC,EAAO,eAAiB,CAACD,EAAK,OAG3C,OAAOC,EAAO,IAAQ,KAAeA,EAAO,KAAOA,EAAO,YAC5DA,EAAO,aAAc,EAIvBA,EAAO,OAAO,KAAKD,CAAI,EAGnBA,EAAK,WACPA,EAAK,OAAO,KAAK,CACf,MAAO,OACP,OAAQ,UAAW,CACjBA,EAAK,KAAM,CACvB,CACA,CAAS,EAICA,EAAK,UAAYA,EAAK,WAAa,QACrCA,EAAK,KAAM,EAGNA,CACR,EAMD,KAAM,UAAW,CACf,IAAIA,EAAO,KACP4B,EAAM,KAGV,GAAI3B,EAAO,QAAS,CAClBD,EAAK,MAAM,YAAa,KAAM,mBAAmB,EACjD,MACR,CAGU,OAAOA,EAAK,MAAS,WACvBA,EAAK,KAAO,CAACA,EAAK,IAAI,GAIxB,QAASI,EAAE,EAAGA,EAAEJ,EAAK,KAAK,OAAQI,IAAK,CACrC,IAAIK,EAAKoB,EAET,GAAI7B,EAAK,SAAWA,EAAK,QAAQI,CAAC,EAEhCK,EAAMT,EAAK,QAAQI,CAAC,MACf,CAGL,GADAyB,EAAM7B,EAAK,KAAKI,CAAC,EACb,OAAOyB,GAAQ,SAAU,CAC3B7B,EAAK,MAAM,YAAa,KAAM,wDAAwD,EACtF,QACZ,CAGUS,EAAM,0BAA0B,KAAKoB,CAAG,EACnCpB,IACHA,EAAM,aAAa,KAAKoB,EAAI,MAAM,IAAK,CAAC,EAAE,CAAC,CAAC,GAG1CpB,IACFA,EAAMA,EAAI,CAAC,EAAE,YAAa,EAEtC,CAQQ,GALKA,GACH,QAAQ,KAAK,4FAA4F,EAIvGA,GAAOR,EAAO,OAAOQ,CAAG,EAAG,CAC7BmB,EAAM5B,EAAK,KAAKI,CAAC,EACjB,KACV,CACA,CAEM,GAAI,CAACwB,EAAK,CACR5B,EAAK,MAAM,YAAa,KAAM,8CAA8C,EAC5E,MACR,CAEM,OAAAA,EAAK,KAAO4B,EACZ5B,EAAK,OAAS,UAIV,OAAO,SAAS,WAAa,UAAY4B,EAAI,MAAM,EAAG,CAAC,IAAM,UAC/D5B,EAAK,OAAS,GACdA,EAAK,UAAY,IAInB,IAAI8B,EAAM9B,CAAI,EAGVA,EAAK,WACP+B,EAAW/B,CAAI,EAGVA,CACR,EAQD,KAAM,SAASgC,EAAQC,EAAU,CAC/B,IAAIjC,EAAO,KACPkC,EAAK,KAGT,GAAI,OAAOF,GAAW,SACpBE,EAAKF,EACLA,EAAS,SACJ,IAAI,OAAOA,GAAW,UAAYhC,EAAK,SAAW,UAAY,CAACA,EAAK,QAAQgC,CAAM,EAEvF,OAAO,KACF,GAAI,OAAOA,EAAW,MAE3BA,EAAS,YAIL,CAAChC,EAAK,WAAW,CAEnB,QADImC,EAAM,EACD/B,EAAE,EAAGA,EAAEJ,EAAK,QAAQ,OAAQI,IAC/BJ,EAAK,QAAQI,CAAC,EAAE,SAAW,CAACJ,EAAK,QAAQI,CAAC,EAAE,SAC9C+B,IACAD,EAAKlC,EAAK,QAAQI,CAAC,EAAE,KAIrB+B,IAAQ,EACVH,EAAS,KAETE,EAAK,IAEjB,EAIM,IAAI3B,EAAQ2B,EAAKlC,EAAK,WAAWkC,CAAE,EAAIlC,EAAK,eAAgB,EAG5D,GAAI,CAACO,EACH,OAAO,KAWT,GAPI2B,GAAM,CAACF,IACTA,EAASzB,EAAM,SAAW,aAMxBP,EAAK,SAAW,SAAU,CAE5BO,EAAM,QAAUyB,EAGhBzB,EAAM,OAAS,GAGf,IAAI6B,EAAU7B,EAAM,IACpB,OAAAP,EAAK,OAAO,KAAK,CACf,MAAO,OACP,OAAQ,UAAW,CACjBA,EAAK,KAAKoC,CAAO,CAC7B,CACA,CAAS,EAEMA,CACf,CAGM,GAAIF,GAAM,CAAC3B,EAAM,QAEf,OAAK0B,GACHjC,EAAK,WAAW,MAAM,EAGjBO,EAAM,IAIXP,EAAK,WACPC,EAAO,YAAa,EAItB,IAAIoC,EAAO,KAAK,IAAI,EAAG9B,EAAM,MAAQ,EAAIA,EAAM,MAAQP,EAAK,QAAQgC,CAAM,EAAE,CAAC,EAAI,GAAI,EACjFM,EAAW,KAAK,IAAI,GAAKtC,EAAK,QAAQgC,CAAM,EAAE,CAAC,EAAIhC,EAAK,QAAQgC,CAAM,EAAE,CAAC,GAAK,IAAQK,CAAI,EAC1FE,EAAWD,EAAW,IAAQ,KAAK,IAAI/B,EAAM,KAAK,EAClDiC,EAAQxC,EAAK,QAAQgC,CAAM,EAAE,CAAC,EAAI,IAClCS,GAAQzC,EAAK,QAAQgC,CAAM,EAAE,CAAC,EAAIhC,EAAK,QAAQgC,CAAM,EAAE,CAAC,GAAK,IACjEzB,EAAM,QAAUyB,EAIhBzB,EAAM,OAAS,GAGf,IAAImC,EAAY,UAAW,CACzBnC,EAAM,QAAU,GAChBA,EAAM,MAAQ8B,EACd9B,EAAM,OAASiC,EACfjC,EAAM,MAAQkC,EACdlC,EAAM,MAAQ,CAAC,EAAEA,EAAM,OAASP,EAAK,QAAQgC,CAAM,EAAE,CAAC,EACvD,EAGD,GAAIK,GAAQI,EAAM,CAChBzC,EAAK,OAAOO,CAAK,EACjB,MACR,CAGM,IAAIoC,EAAOpC,EAAM,MACjB,GAAIP,EAAK,UAAW,CAElB,IAAI4C,EAAe,UAAW,CAC5B5C,EAAK,UAAY,GACjB0C,EAAW,EACX1C,EAAK,eAAeO,CAAK,EAGzB,IAAIL,EAAOK,EAAM,QAAUP,EAAK,OAAU,EAAIO,EAAM,QACpDoC,EAAK,KAAK,eAAezC,EAAKD,EAAO,IAAI,WAAW,EACpDM,EAAM,WAAaN,EAAO,IAAI,YAG1B,OAAO0C,EAAK,aAAa,MAAU,IACrCpC,EAAM,MAAQoC,EAAK,aAAa,YAAY,EAAGN,EAAM,KAAK,EAAIM,EAAK,aAAa,YAAY,EAAGN,EAAMC,CAAQ,EAE7G/B,EAAM,MAAQoC,EAAK,aAAa,MAAM,EAAGN,EAAM,KAAK,EAAIM,EAAK,aAAa,MAAM,EAAGN,EAAMC,CAAQ,EAI/FC,IAAY,MACdvC,EAAK,WAAWO,EAAM,GAAG,EAAI,WAAWP,EAAK,OAAO,KAAKA,EAAMO,CAAK,EAAGgC,CAAO,GAG3EN,GACH,WAAW,UAAW,CACpBjC,EAAK,MAAM,OAAQO,EAAM,GAAG,EAC5BP,EAAK,WAAY,CAClB,EAAE,CAAC,CAEP,EAEGC,EAAO,QAAU,WAAaA,EAAO,IAAI,QAAU,cACrD2C,EAAc,GAEd5C,EAAK,UAAY,GAGjBA,EAAK,KAAK,SAAU4C,CAAY,EAGhC5C,EAAK,YAAYO,EAAM,GAAG,EAEpC,KAAa,CAEL,IAAIsC,EAAY,UAAW,CACzBF,EAAK,YAAcN,EACnBM,EAAK,MAAQpC,EAAM,QAAUP,EAAK,QAAUC,EAAO,QAAU0C,EAAK,MAClEA,EAAK,OAASpC,EAAM,QAAUN,EAAO,OAAQ,EAC7C0C,EAAK,aAAepC,EAAM,MAG1B,GAAI,CACF,IAAIuC,EAAOH,EAAK,KAAM,EAwCtB,GArCIG,GAAQ,OAAO,QAAY,MAAgBA,aAAgB,SAAW,OAAOA,EAAK,MAAS,aAE7F9C,EAAK,UAAY,GAGjB0C,EAAW,EAGXI,EACG,KAAK,UAAW,CACf9C,EAAK,UAAY,GACjB2C,EAAK,UAAY,GACZV,EAGHjC,EAAK,WAAY,EAFjBA,EAAK,MAAM,OAAQO,EAAM,GAAG,CAI/B,CAAA,EACA,MAAM,UAAW,CAChBP,EAAK,UAAY,GACjBA,EAAK,MAAM,YAAaO,EAAM,IAAK,6IAC+C,EAGlFA,EAAM,OAAS,GACfA,EAAM,QAAU,EAClC,CAAiB,GACO0B,IACVjC,EAAK,UAAY,GACjB0C,EAAW,EACX1C,EAAK,MAAM,OAAQO,EAAM,GAAG,GAI9BoC,EAAK,aAAepC,EAAM,MAGtBoC,EAAK,OAAQ,CACf3C,EAAK,MAAM,YAAaO,EAAM,IAAK,6IAC+C,EAClF,MACd,CAGgByB,IAAW,aAAezB,EAAM,MAClCP,EAAK,WAAWO,EAAM,GAAG,EAAI,WAAWP,EAAK,OAAO,KAAKA,EAAMO,CAAK,EAAGgC,CAAO,GAE9EvC,EAAK,WAAWO,EAAM,GAAG,EAAI,UAAW,CAEtCP,EAAK,OAAOO,CAAK,EAGjBoC,EAAK,oBAAoB,QAAS3C,EAAK,WAAWO,EAAM,GAAG,EAAG,EAAK,CACpE,EACDoC,EAAK,iBAAiB,QAAS3C,EAAK,WAAWO,EAAM,GAAG,EAAG,EAAK,EAEnE,OAAQwC,EAAK,CACZ/C,EAAK,MAAM,YAAaO,EAAM,IAAKwC,CAAG,CAClD,CACS,EAGGJ,EAAK,MAAQ,2FACfA,EAAK,IAAM3C,EAAK,KAChB2C,EAAK,KAAM,GAIb,IAAIK,EAAsB,QAAU,OAAO,QAAY,CAACL,EAAK,YAAc1C,EAAO,WAAW,WAC7F,GAAI0C,EAAK,YAAc,GAAKK,EAC1BH,EAAW,MACN,CACL7C,EAAK,UAAY,GACjBA,EAAK,OAAS,UAEd,IAAIiD,EAAW,UAAW,CACxBjD,EAAK,OAAS,SAGd6C,EAAW,EAGXF,EAAK,oBAAoB1C,EAAO,cAAegD,EAAU,EAAK,CAC/D,EACDN,EAAK,iBAAiB1C,EAAO,cAAegD,EAAU,EAAK,EAG3DjD,EAAK,YAAYO,EAAM,GAAG,CACpC,CACA,CAEM,OAAOA,EAAM,GACd,EAOD,MAAO,SAAS2B,EAAI,CAClB,IAAIlC,EAAO,KAGX,GAAIA,EAAK,SAAW,UAAYA,EAAK,UACnC,OAAAA,EAAK,OAAO,KAAK,CACf,MAAO,QACP,OAAQ,UAAW,CACjBA,EAAK,MAAMkC,CAAE,CACzB,CACA,CAAS,EAEMlC,EAMT,QAFIK,EAAML,EAAK,aAAakC,CAAE,EAErB9B,EAAE,EAAGA,EAAEC,EAAI,OAAQD,IAAK,CAE/BJ,EAAK,YAAYK,EAAID,CAAC,CAAC,EAGvB,IAAIG,EAAQP,EAAK,WAAWK,EAAID,CAAC,CAAC,EAElC,GAAIG,GAAS,CAACA,EAAM,UAElBA,EAAM,MAAQP,EAAK,KAAKK,EAAID,CAAC,CAAC,EAC9BG,EAAM,UAAY,EAClBA,EAAM,QAAU,GAGhBP,EAAK,UAAUK,EAAID,CAAC,CAAC,EAEjBG,EAAM,OACR,GAAIP,EAAK,UAAW,CAElB,GAAI,CAACO,EAAM,MAAM,aACf,SAGE,OAAOA,EAAM,MAAM,aAAa,KAAS,IAC3CA,EAAM,MAAM,aAAa,QAAQ,CAAC,EAElCA,EAAM,MAAM,aAAa,KAAK,CAAC,EAIjCP,EAAK,aAAaO,EAAM,KAAK,CAC3C,MAAuB,CAAC,MAAMA,EAAM,MAAM,QAAQ,GAAKA,EAAM,MAAM,WAAa,MAClEA,EAAM,MAAM,MAAO,EAMpB,UAAU,CAAC,GACdP,EAAK,MAAM,QAASO,EAAQA,EAAM,IAAM,IAAI,CAEtD,CAEM,OAAOP,CACR,EAQD,KAAM,SAASkC,EAAID,EAAU,CAC3B,IAAIjC,EAAO,KAGX,GAAIA,EAAK,SAAW,UAAYA,EAAK,UACnC,OAAAA,EAAK,OAAO,KAAK,CACf,MAAO,OACP,OAAQ,UAAW,CACjBA,EAAK,KAAKkC,CAAE,CACxB,CACA,CAAS,EAEMlC,EAMT,QAFIK,EAAML,EAAK,aAAakC,CAAE,EAErB9B,EAAE,EAAGA,EAAEC,EAAI,OAAQD,IAAK,CAE/BJ,EAAK,YAAYK,EAAID,CAAC,CAAC,EAGvB,IAAIG,EAAQP,EAAK,WAAWK,EAAID,CAAC,CAAC,EAE9BG,IAEFA,EAAM,MAAQA,EAAM,QAAU,EAC9BA,EAAM,UAAY,EAClBA,EAAM,QAAU,GAChBA,EAAM,OAAS,GAGfP,EAAK,UAAUK,EAAID,CAAC,CAAC,EAEjBG,EAAM,QACJP,EAAK,UAEHO,EAAM,MAAM,eACV,OAAOA,EAAM,MAAM,aAAa,KAAS,IAC3CA,EAAM,MAAM,aAAa,QAAQ,CAAC,EAElCA,EAAM,MAAM,aAAa,KAAK,CAAC,EAIjCP,EAAK,aAAaO,EAAM,KAAK,IAEtB,CAAC,MAAMA,EAAM,MAAM,QAAQ,GAAKA,EAAM,MAAM,WAAa,OAClEA,EAAM,MAAM,YAAcA,EAAM,QAAU,EAC1CA,EAAM,MAAM,MAAO,EAGfA,EAAM,MAAM,WAAa,KAC3BP,EAAK,YAAYO,EAAM,KAAK,IAK7B0B,GACHjC,EAAK,MAAM,OAAQO,EAAM,GAAG,EAGxC,CAEM,OAAOP,CACR,EAQD,KAAM,SAASQ,EAAO0B,EAAI,CACxB,IAAIlC,EAAO,KAGX,GAAIA,EAAK,SAAW,UAAWA,EAAK,UAClC,OAAAA,EAAK,OAAO,KAAK,CACf,MAAO,OACP,OAAQ,UAAW,CACjBA,EAAK,KAAKQ,EAAO0B,CAAE,CAC/B,CACA,CAAS,EAEMlC,EAIT,GAAI,OAAOkC,EAAO,IAChB,GAAI,OAAO1B,GAAU,UACnBR,EAAK,OAASQ,MAEd,QAAOR,EAAK,OAOhB,QAFIK,EAAML,EAAK,aAAakC,CAAE,EAErB9B,EAAE,EAAGA,EAAEC,EAAI,OAAQD,IAAK,CAE/B,IAAIG,EAAQP,EAAK,WAAWK,EAAID,CAAC,CAAC,EAE9BG,IACFA,EAAM,OAASC,EAGXD,EAAM,WACRP,EAAK,UAAUO,EAAM,GAAG,EAGtBP,EAAK,WAAaO,EAAM,MAC1BA,EAAM,MAAM,KAAK,eAAeC,EAAQ,EAAID,EAAM,QAASN,EAAO,IAAI,WAAW,EACxEM,EAAM,QACfA,EAAM,MAAM,MAAQN,EAAO,OAAS,GAAOO,GAG7CR,EAAK,MAAM,OAAQO,EAAM,GAAG,EAEtC,CAEM,OAAOP,CACR,EAUD,OAAQ,UAAW,CACjB,IAAIA,EAAO,KACPkD,EAAO,UACPhD,EAAKgC,EAGT,GAAIgB,EAAK,SAAW,EAElB,OAAOlD,EAAK,QACP,GAAIkD,EAAK,SAAW,GAAKA,EAAK,SAAW,GAAK,OAAOA,EAAK,CAAC,EAAM,IAAa,CAEnF,IAAI7C,EAAML,EAAK,aAAc,EACzBmD,EAAQ9C,EAAI,QAAQ6C,EAAK,CAAC,CAAC,EAC3BC,GAAS,EACXjB,EAAK,SAASgB,EAAK,CAAC,EAAG,EAAE,EAEzBhD,EAAM,WAAWgD,EAAK,CAAC,CAAC,CAElC,MAAiBA,EAAK,QAAU,IACxBhD,EAAM,WAAWgD,EAAK,CAAC,CAAC,EACxBhB,EAAK,SAASgB,EAAK,CAAC,EAAG,EAAE,GAI3B,IAAI3C,EACJ,GAAI,OAAOL,EAAQ,KAAeA,GAAO,GAAKA,GAAO,EAAG,CAEtD,GAAIF,EAAK,SAAW,UAAWA,EAAK,UAClC,OAAAA,EAAK,OAAO,KAAK,CACf,MAAO,SACP,OAAQ,UAAW,CACjBA,EAAK,OAAO,MAAMA,EAAMkD,CAAI,CAC1C,CACA,CAAW,EAEMlD,EAIL,OAAOkC,EAAO,MAChBlC,EAAK,QAAUE,GAIjBgC,EAAKlC,EAAK,aAAakC,CAAE,EACzB,QAAS9B,EAAE,EAAGA,EAAE8B,EAAG,OAAQ9B,IAEzBG,EAAQP,EAAK,WAAWkC,EAAG9B,CAAC,CAAC,EAEzBG,IACFA,EAAM,QAAUL,EAGXgD,EAAK,CAAC,GACTlD,EAAK,UAAUkC,EAAG9B,CAAC,CAAC,EAGlBJ,EAAK,WAAaO,EAAM,OAAS,CAACA,EAAM,OAC1CA,EAAM,MAAM,KAAK,eAAeL,EAAKD,EAAO,IAAI,WAAW,EAClDM,EAAM,OAAS,CAACA,EAAM,SAC/BA,EAAM,MAAM,OAASL,EAAMD,EAAO,OAAQ,GAG5CD,EAAK,MAAM,SAAUO,EAAM,GAAG,EAG1C,KACQ,QAAAA,EAAQ2B,EAAKlC,EAAK,WAAWkC,CAAE,EAAIlC,EAAK,QAAQ,CAAC,EAC1CO,EAAQA,EAAM,QAAU,EAGjC,OAAOP,CACR,EAUD,KAAM,SAASoD,EAAMC,EAAIC,EAAKpB,EAAI,CAChC,IAAIlC,EAAO,KAGX,GAAIA,EAAK,SAAW,UAAYA,EAAK,UACnC,OAAAA,EAAK,OAAO,KAAK,CACf,MAAO,OACP,OAAQ,UAAW,CACjBA,EAAK,KAAKoD,EAAMC,EAAIC,EAAKpB,CAAE,CACvC,CACA,CAAS,EAEMlC,EAIToD,EAAO,KAAK,IAAI,KAAK,IAAI,EAAG,WAAWA,CAAI,CAAC,EAAG,CAAC,EAChDC,EAAK,KAAK,IAAI,KAAK,IAAI,EAAG,WAAWA,CAAE,CAAC,EAAG,CAAC,EAC5CC,EAAM,WAAWA,CAAG,EAGpBtD,EAAK,OAAOoD,EAAMlB,CAAE,EAIpB,QADI7B,EAAML,EAAK,aAAakC,CAAE,EACrB9B,EAAE,EAAGA,EAAEC,EAAI,OAAQD,IAAK,CAE/B,IAAIG,EAAQP,EAAK,WAAWK,EAAID,CAAC,CAAC,EAGlC,GAAIG,EAAO,CAOT,GALK2B,GACHlC,EAAK,UAAUK,EAAID,CAAC,CAAC,EAInBJ,EAAK,WAAa,CAACO,EAAM,OAAQ,CACnC,IAAIgD,EAActD,EAAO,IAAI,YACzBuD,EAAMD,EAAeD,EAAM,IAC/B/C,EAAM,QAAU6C,EAChB7C,EAAM,MAAM,KAAK,eAAe6C,EAAMG,CAAW,EACjDhD,EAAM,MAAM,KAAK,wBAAwB8C,EAAIG,CAAG,CAC5D,CAEUxD,EAAK,mBAAmBO,EAAO6C,EAAMC,EAAIC,EAAKjD,EAAID,CAAC,EAAG,OAAO8B,EAAO,GAAW,CACzF,CACA,CAEM,OAAOlC,CACR,EAWD,mBAAoB,SAASO,EAAO6C,EAAMC,EAAIC,EAAKpB,EAAIuB,EAAS,CAC9D,IAAIzD,EAAO,KACPE,EAAMkD,EACNM,EAAOL,EAAKD,EACZO,EAAQ,KAAK,IAAID,EAAO,GAAI,EAC5BE,EAAU,KAAK,IAAI,EAAID,EAAQ,EAAKL,EAAMK,EAAQL,CAAG,EACrDO,EAAW,KAAK,IAAK,EAGzBtD,EAAM,QAAU8C,EAGhB9C,EAAM,UAAY,YAAY,UAAW,CAEvC,IAAIuD,GAAQ,KAAK,IAAK,EAAGD,GAAYP,EACrCO,EAAW,KAAK,IAAK,EACrB3D,GAAOwD,EAAOI,EAGd5D,EAAM,KAAK,MAAMA,EAAM,GAAG,EAAI,IAG1BwD,EAAO,EACTxD,EAAM,KAAK,IAAImD,EAAInD,CAAG,EAEtBA,EAAM,KAAK,IAAImD,EAAInD,CAAG,EAIpBF,EAAK,UACPO,EAAM,QAAUL,EAEhBF,EAAK,OAAOE,EAAKK,EAAM,IAAK,EAAI,EAI9BkD,IACFzD,EAAK,QAAUE,IAIZmD,EAAKD,GAAQlD,GAAOmD,GAAQA,EAAKD,GAAQlD,GAAOmD,KACnD,cAAc9C,EAAM,SAAS,EAC7BA,EAAM,UAAY,KAClBA,EAAM,QAAU,KAChBP,EAAK,OAAOqD,EAAI9C,EAAM,GAAG,EACzBP,EAAK,MAAM,OAAQO,EAAM,GAAG,EAE/B,EAAEqD,CAAO,CACX,EAQD,UAAW,SAAS1B,EAAI,CACtB,IAAIlC,EAAO,KACPO,EAAQP,EAAK,WAAWkC,CAAE,EAE9B,OAAI3B,GAASA,EAAM,YACbP,EAAK,WACPO,EAAM,MAAM,KAAK,sBAAsBN,EAAO,IAAI,WAAW,EAG/D,cAAcM,EAAM,SAAS,EAC7BA,EAAM,UAAY,KAClBP,EAAK,OAAOO,EAAM,QAAS2B,CAAE,EAC7B3B,EAAM,QAAU,KAChBP,EAAK,MAAM,OAAQkC,CAAE,GAGhBlC,CACR,EAUD,KAAM,UAAW,CACf,IAAIA,EAAO,KACPkD,EAAO,UACPa,EAAM7B,EAAI3B,EAGd,GAAI2C,EAAK,SAAW,EAElB,OAAOlD,EAAK,MACP,GAAIkD,EAAK,SAAW,EACzB,GAAI,OAAOA,EAAK,CAAC,GAAM,UACrBa,EAAOb,EAAK,CAAC,EACblD,EAAK,MAAQ+D,MAGb,QAAAxD,EAAQP,EAAK,WAAW,SAASkD,EAAK,CAAC,EAAG,EAAE,CAAC,EACtC3C,EAAQA,EAAM,MAAQ,QAEtB2C,EAAK,SAAW,IACzBa,EAAOb,EAAK,CAAC,EACbhB,EAAK,SAASgB,EAAK,CAAC,EAAG,EAAE,GAK3B,QADI7C,EAAML,EAAK,aAAakC,CAAE,EACrB9B,EAAE,EAAGA,EAAEC,EAAI,OAAQD,IAC1BG,EAAQP,EAAK,WAAWK,EAAID,CAAC,CAAC,EAE1BG,IACFA,EAAM,MAAQwD,EACV/D,EAAK,WAAaO,EAAM,OAASA,EAAM,MAAM,eAC/CA,EAAM,MAAM,aAAa,KAAOwD,EAC5BA,IACFxD,EAAM,MAAM,aAAa,UAAYA,EAAM,QAAU,EACrDA,EAAM,MAAM,aAAa,QAAUA,EAAM,MAGrCP,EAAK,QAAQK,EAAID,CAAC,CAAC,IACrBJ,EAAK,MAAMK,EAAID,CAAC,EAAG,EAAI,EACvBJ,EAAK,KAAKK,EAAID,CAAC,EAAG,EAAI,MAOhC,OAAOJ,CACR,EAUD,KAAM,UAAW,CACf,IAAIA,EAAO,KACPkD,EAAO,UACPc,EAAM9B,EAGV,GAAIgB,EAAK,SAAW,EAElBhB,EAAKlC,EAAK,QAAQ,CAAC,EAAE,YACZkD,EAAK,SAAW,EAAG,CAE5B,IAAI7C,EAAML,EAAK,aAAc,EACzBmD,EAAQ9C,EAAI,QAAQ6C,EAAK,CAAC,CAAC,EAC3BC,GAAS,EACXjB,EAAK,SAASgB,EAAK,CAAC,EAAG,EAAE,EAEzBc,EAAO,WAAWd,EAAK,CAAC,CAAC,CAEnC,MAAiBA,EAAK,SAAW,IACzBc,EAAO,WAAWd,EAAK,CAAC,CAAC,EACzBhB,EAAK,SAASgB,EAAK,CAAC,EAAG,EAAE,GAI3B,IAAI3C,EACJ,GAAI,OAAOyD,GAAS,SAAU,CAE5B,GAAIhE,EAAK,SAAW,UAAYA,EAAK,UACnC,OAAAA,EAAK,OAAO,KAAK,CACf,MAAO,OACP,OAAQ,UAAW,CACjBA,EAAK,KAAK,MAAMA,EAAMkD,CAAI,CACxC,CACA,CAAW,EAEMlD,EAIL,OAAOkC,EAAO,MAChBlC,EAAK,MAAQgE,GAIf9B,EAAKlC,EAAK,aAAakC,CAAE,EACzB,QAAS9B,EAAE,EAAGA,EAAE8B,EAAG,OAAQ9B,IAIzB,GAFAG,EAAQP,EAAK,WAAWkC,EAAG9B,CAAC,CAAC,EAEzBG,EAAO,CAGLP,EAAK,QAAQkC,EAAG9B,CAAC,CAAC,IACpBG,EAAM,UAAYP,EAAK,KAAKkC,EAAG9B,CAAC,CAAC,EACjCG,EAAM,WAAaP,EAAK,UAAYC,EAAO,IAAI,YAAcM,EAAM,YAErEA,EAAM,MAAQyD,EAGVhE,EAAK,WAAaO,EAAM,OAASA,EAAM,MAAM,aAC/CA,EAAM,MAAM,aAAa,aAAa,eAAeyD,EAAM/D,EAAO,IAAI,WAAW,EACxEM,EAAM,QACfA,EAAM,MAAM,aAAeyD,GAI7B,IAAI3B,EAAOrC,EAAK,KAAKkC,EAAG9B,CAAC,CAAC,EACtBkC,GAAatC,EAAK,QAAQO,EAAM,OAAO,EAAE,CAAC,EAAIP,EAAK,QAAQO,EAAM,OAAO,EAAE,CAAC,GAAK,IAAQ8B,EACxFE,EAAWD,EAAW,IAAQ,KAAK,IAAI/B,EAAM,KAAK,GAGlDP,EAAK,WAAWkC,EAAG9B,CAAC,CAAC,GAAK,CAACG,EAAM,WACnCP,EAAK,YAAYkC,EAAG9B,CAAC,CAAC,EACtBJ,EAAK,WAAWkC,EAAG9B,CAAC,CAAC,EAAI,WAAWJ,EAAK,OAAO,KAAKA,EAAMO,CAAK,EAAGgC,CAAO,GAG5EvC,EAAK,MAAM,OAAQO,EAAM,GAAG,CACxC,CAEA,KACQ,QAAAA,EAAQP,EAAK,WAAWkC,CAAE,EACnB3B,EAAQA,EAAM,MAAQP,EAAK,MAGpC,OAAOA,CACR,EAUD,KAAM,UAAW,CACf,IAAIA,EAAO,KACPkD,EAAO,UACPb,EAAMH,EAGV,GAAIgB,EAAK,SAAW,EAEdlD,EAAK,QAAQ,SACfkC,EAAKlC,EAAK,QAAQ,CAAC,EAAE,aAEdkD,EAAK,SAAW,EAAG,CAE5B,IAAI7C,EAAML,EAAK,aAAc,EACzBmD,EAAQ9C,EAAI,QAAQ6C,EAAK,CAAC,CAAC,EAC3BC,GAAS,EACXjB,EAAK,SAASgB,EAAK,CAAC,EAAG,EAAE,EAChBlD,EAAK,QAAQ,SACtBkC,EAAKlC,EAAK,QAAQ,CAAC,EAAE,IACrBqC,EAAO,WAAWa,EAAK,CAAC,CAAC,EAEnC,MAAiBA,EAAK,SAAW,IACzBb,EAAO,WAAWa,EAAK,CAAC,CAAC,EACzBhB,EAAK,SAASgB,EAAK,CAAC,EAAG,EAAE,GAI3B,GAAI,OAAOhB,EAAO,IAChB,MAAO,GAIT,GAAI,OAAOG,GAAS,WAAarC,EAAK,SAAW,UAAYA,EAAK,WAChE,OAAAA,EAAK,OAAO,KAAK,CACf,MAAO,OACP,OAAQ,UAAW,CACjBA,EAAK,KAAK,MAAMA,EAAMkD,CAAI,CACtC,CACA,CAAS,EAEMlD,EAIT,IAAIO,EAAQP,EAAK,WAAWkC,CAAE,EAE9B,GAAI3B,EACF,GAAI,OAAO8B,GAAS,UAAYA,GAAQ,EAAG,CAEzC,IAAI4B,EAAUjE,EAAK,QAAQkC,CAAE,EACzB+B,GACFjE,EAAK,MAAMkC,EAAI,EAAI,EAIrB3B,EAAM,MAAQ8B,EACd9B,EAAM,OAAS,GACfP,EAAK,YAAYkC,CAAE,EAGf,CAAClC,EAAK,WAAaO,EAAM,OAAS,CAAC,MAAMA,EAAM,MAAM,QAAQ,IAC/DA,EAAM,MAAM,YAAc8B,GAI5B,IAAI6B,EAAc,UAAW,CAEvBD,GACFjE,EAAK,KAAKkC,EAAI,EAAI,EAGpBlC,EAAK,MAAM,OAAQkC,CAAE,CACtB,EAGD,GAAI+B,GAAW,CAACjE,EAAK,UAAW,CAC9B,IAAImE,EAAW,UAAW,CACnBnE,EAAK,UAGR,WAAWmE,EAAU,CAAC,EAFtBD,EAAa,CAIhB,EACD,WAAWC,EAAU,CAAC,CAClC,MACYD,EAAa,CAEzB,SACclE,EAAK,UAAW,CAClB,IAAIoE,EAAWpE,EAAK,QAAQkC,CAAE,EAAIjC,EAAO,IAAI,YAAcM,EAAM,WAAa,EAC1E8D,EAAW9D,EAAM,UAAYA,EAAM,UAAYA,EAAM,MAAQ,EACjE,OAAOA,EAAM,OAAS8D,EAAWD,EAAW,KAAK,IAAI7D,EAAM,KAAK,EAC5E,KACY,QAAOA,EAAM,MAAM,YAKzB,OAAOP,CACR,EAOD,QAAS,SAASkC,EAAI,CACpB,IAAIlC,EAAO,KAGX,GAAI,OAAOkC,GAAO,SAAU,CAC1B,IAAI3B,EAAQP,EAAK,WAAWkC,CAAE,EAC9B,OAAO3B,EAAQ,CAACA,EAAM,QAAU,EACxC,CAGM,QAASH,EAAE,EAAGA,EAAEJ,EAAK,QAAQ,OAAQI,IACnC,GAAI,CAACJ,EAAK,QAAQI,CAAC,EAAE,QACnB,MAAO,GAIX,MAAO,EACR,EAOD,SAAU,SAAS8B,EAAI,CACrB,IAAIlC,EAAO,KACPsC,EAAWtC,EAAK,UAGhBO,EAAQP,EAAK,WAAWkC,CAAE,EAC9B,OAAI3B,IACF+B,EAAWtC,EAAK,QAAQO,EAAM,OAAO,EAAE,CAAC,EAAI,KAGvC+B,CACR,EAMD,MAAO,UAAW,CAChB,OAAO,KAAK,MACb,EAMD,OAAQ,UAAW,CAKjB,QAJItC,EAAO,KAGPsE,EAAStE,EAAK,QACTI,EAAE,EAAGA,EAAEkE,EAAO,OAAQlE,IAExBkE,EAAOlE,CAAC,EAAE,SACbJ,EAAK,KAAKsE,EAAOlE,CAAC,EAAE,GAAG,EAIpBJ,EAAK,YAERA,EAAK,YAAYsE,EAAOlE,CAAC,EAAE,KAAK,EAGhCkE,EAAOlE,CAAC,EAAE,MAAM,oBAAoB,QAASkE,EAAOlE,CAAC,EAAE,SAAU,EAAK,EACtEkE,EAAOlE,CAAC,EAAE,MAAM,oBAAoBH,EAAO,cAAeqE,EAAOlE,CAAC,EAAE,QAAS,EAAK,EAClFkE,EAAOlE,CAAC,EAAE,MAAM,oBAAoB,QAASkE,EAAOlE,CAAC,EAAE,OAAQ,EAAK,EAGpEH,EAAO,mBAAmBqE,EAAOlE,CAAC,EAAE,KAAK,GAI3C,OAAOkE,EAAOlE,CAAC,EAAE,MAGjBJ,EAAK,YAAYsE,EAAOlE,CAAC,EAAE,GAAG,EAIhC,IAAI+C,EAAQlD,EAAO,OAAO,QAAQD,CAAI,EAClCmD,GAAS,GACXlD,EAAO,OAAO,OAAOkD,EAAO,CAAC,EAI/B,IAAIoB,EAAW,GACf,IAAKnE,EAAE,EAAGA,EAAEH,EAAO,OAAO,OAAQG,IAChC,GAAIH,EAAO,OAAOG,CAAC,EAAE,OAASJ,EAAK,MAAQA,EAAK,KAAK,QAAQC,EAAO,OAAOG,CAAC,EAAE,IAAI,GAAK,EAAG,CACxFmE,EAAW,GACX,KACV,CAGM,OAAIC,GAASD,GACX,OAAOC,EAAMxE,EAAK,IAAI,EAIxBC,EAAO,QAAU,GAGjBD,EAAK,OAAS,WACdA,EAAK,QAAU,CAAE,EACjBA,EAAO,KAEA,IACR,EAUD,GAAI,SAASyE,EAAOC,EAAIxC,EAAIyC,EAAM,CAChC,IAAI3E,EAAO,KACP4E,EAAS5E,EAAK,MAAQyE,CAAK,EAE/B,OAAI,OAAOC,GAAO,YAChBE,EAAO,KAAKD,EAAO,CAAC,GAAIzC,EAAI,GAAIwC,EAAI,KAAMC,CAAI,EAAI,CAAC,GAAIzC,EAAI,GAAIwC,CAAE,CAAC,EAG7D1E,CACR,EASD,IAAK,SAASyE,EAAOC,EAAIxC,EAAI,CAC3B,IAAIlC,EAAO,KACP4E,EAAS5E,EAAK,MAAQyE,CAAK,EAC3BrE,EAAI,EAQR,GALI,OAAOsE,GAAO,WAChBxC,EAAKwC,EACLA,EAAK,MAGHA,GAAMxC,EAER,IAAK9B,EAAE,EAAGA,EAAEwE,EAAO,OAAQxE,IAAK,CAC9B,IAAIyE,EAAQ3C,IAAO0C,EAAOxE,CAAC,EAAE,GAC7B,GAAIsE,IAAOE,EAAOxE,CAAC,EAAE,IAAMyE,GAAQ,CAACH,GAAMG,EAAM,CAC9CD,EAAO,OAAOxE,EAAG,CAAC,EAClB,KACZ,CACA,SACiBqE,EAETzE,EAAK,MAAQyE,CAAK,EAAI,CAAE,MACnB,CAEL,IAAIK,EAAO,OAAO,KAAK9E,CAAI,EAC3B,IAAKI,EAAE,EAAGA,EAAE0E,EAAK,OAAQ1E,IAClB0E,EAAK1E,CAAC,EAAE,QAAQ,KAAK,IAAM,GAAM,MAAM,QAAQJ,EAAK8E,EAAK1E,CAAC,CAAC,CAAC,IAC/DJ,EAAK8E,EAAK1E,CAAC,CAAC,EAAI,CAAE,EAG9B,CAEM,OAAOJ,CACR,EASD,KAAM,SAASyE,EAAOC,EAAIxC,EAAI,CAC5B,IAAIlC,EAAO,KAGX,OAAAA,EAAK,GAAGyE,EAAOC,EAAIxC,EAAI,CAAC,EAEjBlC,CACR,EASD,MAAO,SAASyE,EAAOvC,EAAI6C,EAAK,CAK9B,QAJI/E,EAAO,KACP4E,EAAS5E,EAAK,MAAQyE,CAAK,EAGtBrE,EAAEwE,EAAO,OAAO,EAAGxE,GAAG,EAAGA,KAE5B,CAACwE,EAAOxE,CAAC,EAAE,IAAMwE,EAAOxE,CAAC,EAAE,KAAO8B,GAAMuC,IAAU,UACpD,YAAW,SAASC,EAAI,CACtBA,EAAG,KAAK,KAAMxC,EAAI6C,CAAG,CACjC,GAAY,KAAK/E,EAAM4E,EAAOxE,CAAC,EAAE,EAAE,EAAG,CAAC,EAGzBwE,EAAOxE,CAAC,EAAE,MACZJ,EAAK,IAAIyE,EAAOG,EAAOxE,CAAC,EAAE,GAAIwE,EAAOxE,CAAC,EAAE,EAAE,GAMhD,OAAAJ,EAAK,WAAWyE,CAAK,EAEdzE,CACR,EAQD,WAAY,SAASyE,EAAO,CAC1B,IAAIzE,EAAO,KAEX,GAAIA,EAAK,OAAO,OAAS,EAAG,CAC1B,IAAIgF,EAAOhF,EAAK,OAAO,CAAC,EAGpBgF,EAAK,QAAUP,IACjBzE,EAAK,OAAO,MAAO,EACnBA,EAAK,WAAY,GAIdyE,GACHO,EAAK,OAAQ,CAEvB,CAEM,OAAOhF,CACR,EAOD,OAAQ,SAASO,EAAO,CACtB,IAAIP,EAAO,KACPgC,EAASzB,EAAM,QAKnB,GAAI,CAACP,EAAK,WAAaO,EAAM,OAAS,CAACA,EAAM,MAAM,QAAU,CAACA,EAAM,MAAM,OAASA,EAAM,MAAM,YAAcA,EAAM,MACjH,kBAAWP,EAAK,OAAO,KAAKA,EAAMO,CAAK,EAAG,GAAG,EACtCP,EAIT,IAAI+D,EAAO,CAAC,EAAExD,EAAM,OAASP,EAAK,QAAQgC,CAAM,EAAE,CAAC,GAWnD,GARAhC,EAAK,MAAM,MAAOO,EAAM,GAAG,EAGvB,CAACP,EAAK,WAAa+D,GACrB/D,EAAK,KAAKO,EAAM,IAAK,EAAI,EAAE,KAAKA,EAAM,GAAG,EAIvCP,EAAK,WAAa+D,EAAM,CAC1B/D,EAAK,MAAM,OAAQO,EAAM,GAAG,EAC5BA,EAAM,MAAQA,EAAM,QAAU,EAC9BA,EAAM,UAAY,EAClBA,EAAM,WAAaN,EAAO,IAAI,YAE9B,IAAIsC,GAAYhC,EAAM,MAAQA,EAAM,QAAU,IAAQ,KAAK,IAAIA,EAAM,KAAK,EAC1EP,EAAK,WAAWO,EAAM,GAAG,EAAI,WAAWP,EAAK,OAAO,KAAKA,EAAMO,CAAK,EAAGgC,CAAO,CACtF,CAGM,OAAIvC,EAAK,WAAa,CAAC+D,IACrBxD,EAAM,QAAU,GAChBA,EAAM,OAAS,GACfA,EAAM,MAAQA,EAAM,QAAU,EAC9BA,EAAM,UAAY,EAClBP,EAAK,YAAYO,EAAM,GAAG,EAG1BP,EAAK,aAAaO,EAAM,KAAK,EAG7BN,EAAO,aAAc,GAInB,CAACD,EAAK,WAAa,CAAC+D,GACtB/D,EAAK,KAAKO,EAAM,IAAK,EAAI,EAGpBP,CACR,EAOD,YAAa,SAASkC,EAAI,CACxB,IAAIlC,EAAO,KAEX,GAAIA,EAAK,WAAWkC,CAAE,EAAG,CAEvB,GAAI,OAAOlC,EAAK,WAAWkC,CAAE,GAAM,WACjC,aAAalC,EAAK,WAAWkC,CAAE,CAAC,MAC3B,CACL,IAAI3B,EAAQP,EAAK,WAAWkC,CAAE,EAC1B3B,GAASA,EAAM,OACjBA,EAAM,MAAM,oBAAoB,QAASP,EAAK,WAAWkC,CAAE,EAAG,EAAK,CAE/E,CAEQ,OAAOlC,EAAK,WAAWkC,CAAE,CACjC,CAEM,OAAOlC,CACR,EAOD,WAAY,SAASkC,EAAI,CAIvB,QAHIlC,EAAO,KAGFI,EAAE,EAAGA,EAAEJ,EAAK,QAAQ,OAAQI,IACnC,GAAI8B,IAAOlC,EAAK,QAAQI,CAAC,EAAE,IACzB,OAAOJ,EAAK,QAAQI,CAAC,EAIzB,OAAO,IACR,EAMD,eAAgB,UAAW,CACzB,IAAIJ,EAAO,KAEXA,EAAK,OAAQ,EAGb,QAASI,EAAE,EAAGA,EAAEJ,EAAK,QAAQ,OAAQI,IACnC,GAAIJ,EAAK,QAAQI,CAAC,EAAE,OAClB,OAAOJ,EAAK,QAAQI,CAAC,EAAE,MAAO,EAKlC,OAAO,IAAI0B,EAAM9B,CAAI,CACtB,EAKD,OAAQ,UAAW,CACjB,IAAIA,EAAO,KACPiF,EAAQjF,EAAK,MACbkF,EAAM,EACN9E,EAAI,EAGR,GAAI,EAAAJ,EAAK,QAAQ,OAASiF,GAK1B,KAAK7E,EAAE,EAAGA,EAAEJ,EAAK,QAAQ,OAAQI,IAC3BJ,EAAK,QAAQI,CAAC,EAAE,QAClB8E,IAKJ,IAAK9E,EAAEJ,EAAK,QAAQ,OAAS,EAAGI,GAAG,EAAGA,IAAK,CACzC,GAAI8E,GAAOD,EACT,OAGEjF,EAAK,QAAQI,CAAC,EAAE,SAEdJ,EAAK,WAAaA,EAAK,QAAQI,CAAC,EAAE,OACpCJ,EAAK,QAAQI,CAAC,EAAE,MAAM,WAAW,CAAC,EAIpCJ,EAAK,QAAQ,OAAOI,EAAG,CAAC,EACxB8E,IAEV,EACK,EAOD,aAAc,SAAShD,EAAI,CACzB,IAAIlC,EAAO,KAEX,GAAI,OAAOkC,EAAO,IAAa,CAE7B,QADI7B,EAAM,CAAE,EACHD,EAAE,EAAGA,EAAEJ,EAAK,QAAQ,OAAQI,IACnCC,EAAI,KAAKL,EAAK,QAAQI,CAAC,EAAE,GAAG,EAG9B,OAAOC,CACf,KACQ,OAAO,CAAC6B,CAAE,CAEb,EAOD,eAAgB,SAAS3B,EAAO,CAC9B,IAAIP,EAAO,KAGX,OAAAO,EAAM,MAAM,aAAeN,EAAO,IAAI,mBAAoB,EAC1DM,EAAM,MAAM,aAAa,OAASiE,EAAMxE,EAAK,IAAI,EAG7CO,EAAM,QACRA,EAAM,MAAM,aAAa,QAAQA,EAAM,OAAO,EAE9CA,EAAM,MAAM,aAAa,QAAQA,EAAM,KAAK,EAI9CA,EAAM,MAAM,aAAa,KAAOA,EAAM,MAClCA,EAAM,QACRA,EAAM,MAAM,aAAa,UAAYA,EAAM,QAAU,EACrDA,EAAM,MAAM,aAAa,QAAUA,EAAM,OAAS,GAEpDA,EAAM,MAAM,aAAa,aAAa,eAAeA,EAAM,MAAON,EAAO,IAAI,WAAW,EAEjFD,CACR,EAOD,aAAc,SAAS2C,EAAM,CAC3B,IAAI3C,EAAO,KACPmF,EAAQlF,EAAO,YAAcA,EAAO,WAAW,OAAO,QAAQ,OAAO,GAAK,EAE9E,GAAI,CAAC0C,EAAK,aACR,OAAO3C,EAGT,GAAIC,EAAO,gBAAkB0C,EAAK,eAChCA,EAAK,aAAa,QAAU,KAC5BA,EAAK,aAAa,WAAW,CAAC,EAC1BwC,GACF,GAAI,CAAExC,EAAK,aAAa,OAAS1C,EAAO,cAAe,MAAY,CAAA,CAGvE,OAAA0C,EAAK,aAAe,KAEb3C,CACR,EAMD,YAAa,SAAS2C,EAAM,CAC1B,IAAIyC,EAAU,kBAAkB,KAAKnF,EAAO,YAAcA,EAAO,WAAW,SAAS,EAChFmF,IACHzC,EAAK,IAAM,yFAEnB,CACG,EASD,IAAIb,EAAQ,SAASuD,EAAM,CACzB,KAAK,QAAUA,EACf,KAAK,KAAM,CACZ,EACDvD,EAAM,UAAY,CAKhB,KAAM,UAAW,CACf,IAAI9B,EAAO,KACPsF,EAAStF,EAAK,QAGlB,OAAAA,EAAK,OAASsF,EAAO,OACrBtF,EAAK,MAAQsF,EAAO,MACpBtF,EAAK,QAAUsF,EAAO,QACtBtF,EAAK,MAAQsF,EAAO,MACpBtF,EAAK,MAAQ,EACbA,EAAK,QAAU,GACfA,EAAK,OAAS,GACdA,EAAK,QAAU,YAGfA,EAAK,IAAM,EAAEC,EAAO,SAGpBqF,EAAO,QAAQ,KAAKtF,CAAI,EAGxBA,EAAK,OAAQ,EAENA,CACR,EAMD,OAAQ,UAAW,CACjB,IAAIA,EAAO,KACPsF,EAAStF,EAAK,QACduF,EAAUtF,EAAO,QAAUD,EAAK,QAAUA,EAAK,QAAQ,OAAU,EAAIA,EAAK,QAE9E,OAAIsF,EAAO,WAETtF,EAAK,MAAS,OAAOC,EAAO,IAAI,WAAe,IAAeA,EAAO,IAAI,eAAc,EAAKA,EAAO,IAAI,WAAY,EACnHD,EAAK,MAAM,KAAK,eAAeuF,EAAQtF,EAAO,IAAI,WAAW,EAC7DD,EAAK,MAAM,OAAS,GACpBA,EAAK,MAAM,QAAQC,EAAO,UAAU,GAC1BA,EAAO,UAEjBD,EAAK,MAAQC,EAAO,kBAAmB,EAGvCD,EAAK,SAAWA,EAAK,eAAe,KAAKA,CAAI,EAC7CA,EAAK,MAAM,iBAAiB,QAASA,EAAK,SAAU,EAAK,EAGzDA,EAAK,QAAUA,EAAK,cAAc,KAAKA,CAAI,EAC3CA,EAAK,MAAM,iBAAiBC,EAAO,cAAeD,EAAK,QAAS,EAAK,EAIrEA,EAAK,OAASA,EAAK,aAAa,KAAKA,CAAI,EACzCA,EAAK,MAAM,iBAAiB,QAASA,EAAK,OAAQ,EAAK,EAGvDA,EAAK,MAAM,IAAMsF,EAAO,KACxBtF,EAAK,MAAM,QAAUsF,EAAO,WAAa,GAAO,OAASA,EAAO,SAChEtF,EAAK,MAAM,OAASuF,EAAStF,EAAO,OAAQ,EAG5CD,EAAK,MAAM,KAAM,GAGZA,CACR,EAMD,MAAO,UAAW,CAChB,IAAIA,EAAO,KACPsF,EAAStF,EAAK,QAGlB,OAAAA,EAAK,OAASsF,EAAO,OACrBtF,EAAK,MAAQsF,EAAO,MACpBtF,EAAK,QAAUsF,EAAO,QACtBtF,EAAK,MAAQsF,EAAO,MACpBtF,EAAK,MAAQ,EACbA,EAAK,UAAY,EACjBA,EAAK,QAAU,GACfA,EAAK,OAAS,GACdA,EAAK,QAAU,YAGfA,EAAK,IAAM,EAAEC,EAAO,SAEbD,CACR,EAKD,eAAgB,UAAW,CACzB,IAAIA,EAAO,KAGXA,EAAK,QAAQ,MAAM,YAAaA,EAAK,IAAKA,EAAK,MAAM,MAAQA,EAAK,MAAM,MAAM,KAAO,CAAC,EAGtFA,EAAK,MAAM,oBAAoB,QAASA,EAAK,SAAU,EAAK,CAC7D,EAKD,cAAe,UAAW,CACxB,IAAIA,EAAO,KACPsF,EAAStF,EAAK,QAGlBsF,EAAO,UAAY,KAAK,KAAKtF,EAAK,MAAM,SAAW,EAAE,EAAI,GAGrD,OAAO,KAAKsF,EAAO,OAAO,EAAE,SAAW,IACzCA,EAAO,QAAU,CAAC,UAAW,CAAC,EAAGA,EAAO,UAAY,GAAI,CAAC,GAGvDA,EAAO,SAAW,WACpBA,EAAO,OAAS,SAChBA,EAAO,MAAM,MAAM,EACnBA,EAAO,WAAY,GAIrBtF,EAAK,MAAM,oBAAoBC,EAAO,cAAeD,EAAK,QAAS,EAAK,CACzE,EAKD,aAAc,UAAW,CACvB,IAAIA,EAAO,KACPsF,EAAStF,EAAK,QAGdsF,EAAO,YAAc,MAGvBA,EAAO,UAAY,KAAK,KAAKtF,EAAK,MAAM,SAAW,EAAE,EAAI,GAGrDsF,EAAO,QAAQ,UAAU,CAAC,IAAM,MAClCA,EAAO,QAAQ,UAAU,CAAC,EAAIA,EAAO,UAAY,KAInDA,EAAO,OAAOtF,CAAI,GAIpBA,EAAK,MAAM,oBAAoB,QAASA,EAAK,OAAQ,EAAK,CAChE,CACG,EAKD,IAAIwE,EAAQ,CAAE,EAMVzC,EAAa,SAAS/B,EAAM,CAC9B,IAAI4B,EAAM5B,EAAK,KAGf,GAAIwE,EAAM5C,CAAG,EAAG,CAEd5B,EAAK,UAAYwE,EAAM5C,CAAG,EAAE,SAG5B4D,EAAUxF,CAAI,EAEd,MACN,CAEI,GAAI,sBAAsB,KAAK4B,CAAG,EAAG,CAInC,QAFI6D,EAAO,KAAK7D,EAAI,MAAM,GAAG,EAAE,CAAC,CAAC,EAC7B8D,EAAW,IAAI,WAAWD,EAAK,MAAM,EAChCrF,EAAE,EAAGA,EAAEqF,EAAK,OAAQ,EAAErF,EAC7BsF,EAAStF,CAAC,EAAIqF,EAAK,WAAWrF,CAAC,EAGjCuF,EAAgBD,EAAS,OAAQ1F,CAAI,CAC3C,KAAW,CAEL,IAAI4F,EAAM,IAAI,eACdA,EAAI,KAAK5F,EAAK,KAAK,OAAQ4B,EAAK,EAAI,EACpCgE,EAAI,gBAAkB5F,EAAK,KAAK,gBAChC4F,EAAI,aAAe,cAGf5F,EAAK,KAAK,SACZ,OAAO,KAAKA,EAAK,KAAK,OAAO,EAAE,QAAQ,SAAS6F,EAAK,CACnDD,EAAI,iBAAiBC,EAAK7F,EAAK,KAAK,QAAQ6F,CAAG,CAAC,CAC1D,CAAS,EAGHD,EAAI,OAAS,UAAW,CAEtB,IAAIE,GAAQF,EAAI,OAAS,IAAI,CAAC,EAC9B,GAAIE,IAAS,KAAOA,IAAS,KAAOA,IAAS,IAAK,CAChD9F,EAAK,MAAM,YAAa,KAAM,0CAA4C4F,EAAI,OAAS,GAAG,EAC1F,MACV,CAEQD,EAAgBC,EAAI,SAAU5F,CAAI,CACnC,EACD4F,EAAI,QAAU,UAAW,CAEnB5F,EAAK,YACPA,EAAK,OAAS,GACdA,EAAK,UAAY,GACjBA,EAAK,QAAU,CAAE,EACjB,OAAOwE,EAAM5C,CAAG,EAChB5B,EAAK,KAAM,EAEd,EACD+F,EAAYH,CAAG,CACrB,CACG,EAMGG,EAAc,SAASH,EAAK,CAC9B,GAAI,CACFA,EAAI,KAAM,CACX,MAAW,CACVA,EAAI,QAAS,CACnB,CACG,EAOGD,EAAkB,SAASK,EAAahG,EAAM,CAEhD,IAAIiG,EAAQ,UAAW,CACrBjG,EAAK,MAAM,YAAa,KAAM,6BAA6B,CAC5D,EAGGkG,EAAU,SAASC,EAAQ,CACzBA,GAAUnG,EAAK,QAAQ,OAAS,GAClCwE,EAAMxE,EAAK,IAAI,EAAImG,EACnBX,EAAUxF,EAAMmG,CAAM,GAEtBF,EAAO,CAEV,EAGG,OAAO,QAAY,KAAehG,EAAO,IAAI,gBAAgB,SAAW,EAC1EA,EAAO,IAAI,gBAAgB+F,CAAW,EAAE,KAAKE,CAAO,EAAE,MAAMD,CAAK,EAEjEhG,EAAO,IAAI,gBAAgB+F,EAAaE,EAASD,CAAK,CAE5D,EAOMT,EAAY,SAASxF,EAAMmG,EAAQ,CAEjCA,GAAU,CAACnG,EAAK,YAClBA,EAAK,UAAYmG,EAAO,UAItB,OAAO,KAAKnG,EAAK,OAAO,EAAE,SAAW,IACvCA,EAAK,QAAU,CAAC,UAAW,CAAC,EAAGA,EAAK,UAAY,GAAI,CAAC,GAInDA,EAAK,SAAW,WAClBA,EAAK,OAAS,SACdA,EAAK,MAAM,MAAM,EACjBA,EAAK,WAAY,EAEpB,EAKGG,EAAoB,UAAW,CAEjC,GAAKF,EAAO,cAKZ,IAAI,CACE,OAAO,aAAiB,IAC1BA,EAAO,IAAM,IAAI,aACR,OAAO,mBAAuB,IACvCA,EAAO,IAAM,IAAI,mBAEjBA,EAAO,cAAgB,EAE1B,MAAU,CACTA,EAAO,cAAgB,EAC7B,CAGSA,EAAO,MACVA,EAAO,cAAgB,IAKzB,IAAImG,EAAO,iBAAiB,KAAKnG,EAAO,YAAcA,EAAO,WAAW,QAAQ,EAC5EoG,EAAapG,EAAO,YAAcA,EAAO,WAAW,WAAW,MAAM,wBAAwB,EAC7FqG,EAAUD,EAAa,SAASA,EAAW,CAAC,EAAG,EAAE,EAAI,KACzD,GAAID,GAAOE,GAAWA,EAAU,EAAG,CACjC,IAAIC,EAAS,SAAS,KAAKtG,EAAO,YAAcA,EAAO,WAAW,UAAU,aAAa,EACrFA,EAAO,YAAc,CAACsG,IACxBtG,EAAO,cAAgB,GAE/B,CAGQA,EAAO,gBACTA,EAAO,WAAc,OAAOA,EAAO,IAAI,WAAe,IAAeA,EAAO,IAAI,eAAc,EAAKA,EAAO,IAAI,WAAY,EAC1HA,EAAO,WAAW,KAAK,eAAeA,EAAO,OAAS,EAAIA,EAAO,QAASA,EAAO,IAAI,WAAW,EAChGA,EAAO,WAAW,QAAQA,EAAO,IAAI,WAAW,GAIlDA,EAAO,OAAQ,EAChB,EAcCuG,SAAiBvG,EACjBuG,OAAe9E,EAIb,OAAO+E,EAAW,KACpBA,EAAO,aAAe1G,EACtB0G,EAAO,OAASxG,EAChBwG,EAAO,KAAO/E,EACd+E,EAAO,MAAQ3E,GACN,OAAO,OAAW,MAC3B,OAAO,aAAe/B,EACtB,OAAO,OAASE,EAChB,OAAO,KAAOyB,EACd,OAAO,MAAQI,EAEnB,GAAI,EAGJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAYC,UAAW,CAKV,aAAa,UAAU,KAAO,CAAC,EAAG,EAAG,CAAC,EACtC,aAAa,UAAU,aAAe,CAAC,EAAG,EAAG,GAAI,EAAG,EAAG,CAAC,EAWxD,aAAa,UAAU,OAAS,SAAS4E,EAAK,CAC5C,IAAI1G,EAAO,KAGX,GAAI,CAACA,EAAK,KAAO,CAACA,EAAK,IAAI,SACzB,OAAOA,EAIT,QAAS,EAAEA,EAAK,OAAO,OAAO,EAAG,GAAG,EAAG,IACrCA,EAAK,OAAO,CAAC,EAAE,OAAO0G,CAAG,EAG3B,OAAO1G,CACR,EAUD,aAAa,UAAU,IAAM,SAAS2G,EAAGC,EAAGC,EAAG,CAC7C,IAAI7G,EAAO,KAGX,GAAI,CAACA,EAAK,KAAO,CAACA,EAAK,IAAI,SACzB,OAAOA,EAOT,GAHA4G,EAAK,OAAOA,GAAM,SAAY5G,EAAK,KAAK,CAAC,EAAI4G,EAC7CC,EAAK,OAAOA,GAAM,SAAY7G,EAAK,KAAK,CAAC,EAAI6G,EAEzC,OAAOF,GAAM,SACf3G,EAAK,KAAO,CAAC2G,EAAGC,EAAGC,CAAC,EAEhB,OAAO7G,EAAK,IAAI,SAAS,UAAc,KACzCA,EAAK,IAAI,SAAS,UAAU,gBAAgBA,EAAK,KAAK,CAAC,EAAG,OAAO,IAAI,YAAa,EAAG,EACrFA,EAAK,IAAI,SAAS,UAAU,gBAAgBA,EAAK,KAAK,CAAC,EAAG,OAAO,IAAI,YAAa,EAAG,EACrFA,EAAK,IAAI,SAAS,UAAU,gBAAgBA,EAAK,KAAK,CAAC,EAAG,OAAO,IAAI,YAAa,EAAG,GAErFA,EAAK,IAAI,SAAS,YAAYA,EAAK,KAAK,CAAC,EAAGA,EAAK,KAAK,CAAC,EAAGA,EAAK,KAAK,CAAC,CAAC,MAGxE,QAAOA,EAAK,KAGd,OAAOA,CACR,EAgBD,aAAa,UAAU,YAAc,SAAS2G,EAAGC,EAAGC,EAAGC,EAAKC,EAAKC,EAAK,CACpE,IAAIhH,EAAO,KAGX,GAAI,CAACA,EAAK,KAAO,CAACA,EAAK,IAAI,SACzB,OAAOA,EAIT,IAAIiH,EAAKjH,EAAK,aAOd,GANA4G,EAAK,OAAOA,GAAM,SAAYK,EAAG,CAAC,EAAIL,EACtCC,EAAK,OAAOA,GAAM,SAAYI,EAAG,CAAC,EAAIJ,EACtCC,EAAO,OAAOA,GAAQ,SAAYG,EAAG,CAAC,EAAIH,EAC1CC,EAAO,OAAOA,GAAQ,SAAYE,EAAG,CAAC,EAAIF,EAC1CC,EAAO,OAAOA,GAAQ,SAAYC,EAAG,CAAC,EAAID,EAEtC,OAAOL,GAAM,SACf3G,EAAK,aAAe,CAAC2G,EAAGC,EAAGC,EAAGC,EAAKC,EAAKC,CAAG,EAEvC,OAAOhH,EAAK,IAAI,SAAS,SAAa,KACxCA,EAAK,IAAI,SAAS,SAAS,gBAAgB2G,EAAG,OAAO,IAAI,YAAa,EAAG,EACzE3G,EAAK,IAAI,SAAS,SAAS,gBAAgB4G,EAAG,OAAO,IAAI,YAAa,EAAG,EACzE5G,EAAK,IAAI,SAAS,SAAS,gBAAgB6G,EAAG,OAAO,IAAI,YAAa,EAAG,EACzE7G,EAAK,IAAI,SAAS,IAAI,gBAAgB8G,EAAK,OAAO,IAAI,YAAa,EAAG,EACtE9G,EAAK,IAAI,SAAS,IAAI,gBAAgB+G,EAAK,OAAO,IAAI,YAAa,EAAG,EACtE/G,EAAK,IAAI,SAAS,IAAI,gBAAgBgH,EAAK,OAAO,IAAI,YAAa,EAAG,GAEtEhH,EAAK,IAAI,SAAS,eAAe2G,EAAGC,EAAGC,EAAGC,EAAKC,EAAKC,CAAG,MAGzD,QAAOC,EAGT,OAAOjH,CACR,EAUD,KAAK,UAAU,KAAQ,SAASkH,EAAQ,CACtC,OAAO,SAASvF,EAAG,CACjB,IAAI3B,EAAO,KAGX,OAAAA,EAAK,aAAe2B,EAAE,aAAe,CAAC,EAAG,EAAG,CAAC,EAC7C3B,EAAK,QAAU2B,EAAE,QAAU,KAC3B3B,EAAK,KAAO2B,EAAE,KAAO,KACrB3B,EAAK,YAAc,CACjB,eAAgB,OAAO2B,EAAE,eAAmB,IAAcA,EAAE,eAAiB,IAC7E,eAAgB,OAAOA,EAAE,eAAmB,IAAcA,EAAE,eAAiB,IAC7E,cAAe,OAAOA,EAAE,cAAkB,IAAcA,EAAE,cAAgB,EAC1E,cAAe,OAAOA,EAAE,cAAkB,IAAcA,EAAE,cAAgB,UAC1E,YAAa,OAAOA,EAAE,YAAgB,IAAcA,EAAE,YAAc,IACpE,aAAc,OAAOA,EAAE,aAAiB,IAAcA,EAAE,aAAe,OACvE,YAAa,OAAOA,EAAE,YAAgB,IAAcA,EAAE,YAAc,EACpE,cAAe,OAAOA,EAAE,cAAkB,IAAcA,EAAE,cAAgB,CAC3E,EAGD3B,EAAK,UAAY2B,EAAE,SAAW,CAAC,CAAC,GAAIA,EAAE,QAAQ,CAAC,EAAI,CAAE,EACrD3B,EAAK,OAAS2B,EAAE,MAAQ,CAAC,CAAC,GAAIA,EAAE,KAAK,CAAC,EAAI,CAAE,EAC5C3B,EAAK,eAAiB2B,EAAE,cAAgB,CAAC,CAAC,GAAIA,EAAE,aAAa,CAAC,EAAI,CAAE,EAG7DuF,EAAO,KAAK,KAAMvF,CAAC,CAC3B,CACL,EAAK,KAAK,UAAU,IAAI,EAQtB,KAAK,UAAU,OAAS,SAAS+E,EAAKxE,EAAI,CACxC,IAAIlC,EAAO,KAGX,GAAI,CAACA,EAAK,UACR,OAAOA,EAIT,GAAIA,EAAK,SAAW,SAClB,OAAAA,EAAK,OAAO,KAAK,CACf,MAAO,SACP,OAAQ,UAAW,CACjBA,EAAK,OAAO0G,EAAKxE,CAAE,CAC7B,CACA,CAAO,EAEMlC,EAIT,IAAImH,EAAc,OAAO,OAAO,IAAI,mBAAuB,IAAe,UAAY,SAGtF,GAAI,OAAOjF,EAAO,IAEhB,GAAI,OAAOwE,GAAQ,SACjB1G,EAAK,QAAU0G,EACf1G,EAAK,KAAO,CAAC0G,EAAK,EAAG,CAAC,MAEtB,QAAO1G,EAAK,QAMhB,QADIK,EAAML,EAAK,aAAakC,CAAE,EACrB9B,EAAE,EAAGA,EAAEC,EAAI,OAAQD,IAAK,CAE/B,IAAIG,EAAQP,EAAK,WAAWK,EAAID,CAAC,CAAC,EAElC,GAAIG,EACF,GAAI,OAAOmG,GAAQ,SACjBnG,EAAM,QAAUmG,EAChBnG,EAAM,KAAO,CAACmG,EAAK,EAAG,CAAC,EAEnBnG,EAAM,QAERA,EAAM,YAAY,aAAe,cAG7B,CAACA,EAAM,SAAW,CAACA,EAAM,QAAQ,MACnC6G,EAAY7G,EAAO4G,CAAU,EAG3BA,IAAe,UACb,OAAO5G,EAAM,QAAQ,UAAc,KACrCA,EAAM,QAAQ,UAAU,eAAemG,EAAK,OAAO,IAAI,WAAW,EAClEnG,EAAM,QAAQ,UAAU,eAAe,EAAG,OAAO,IAAI,WAAW,EAChEA,EAAM,QAAQ,UAAU,eAAe,EAAG,OAAO,IAAI,WAAW,GAEhEA,EAAM,QAAQ,YAAYmG,EAAK,EAAG,CAAC,EAGrCnG,EAAM,QAAQ,IAAI,eAAemG,EAAK,OAAO,IAAI,WAAW,GAIhE1G,EAAK,MAAM,SAAUO,EAAM,GAAG,MAE9B,QAAOA,EAAM,OAGvB,CAEI,OAAOP,CACR,EAUD,KAAK,UAAU,IAAM,SAAS2G,EAAGC,EAAGC,EAAG3E,EAAI,CACzC,IAAIlC,EAAO,KAGX,GAAI,CAACA,EAAK,UACR,OAAOA,EAIT,GAAIA,EAAK,SAAW,SAClB,OAAAA,EAAK,OAAO,KAAK,CACf,MAAO,MACP,OAAQ,UAAW,CACjBA,EAAK,IAAI2G,EAAGC,EAAGC,EAAG3E,CAAE,CAC9B,CACA,CAAO,EAEMlC,EAQT,GAJA4G,EAAK,OAAOA,GAAM,SAAY,EAAIA,EAClCC,EAAK,OAAOA,GAAM,SAAY,IAAOA,EAGjC,OAAO3E,EAAO,IAEhB,GAAI,OAAOyE,GAAM,SACf3G,EAAK,KAAO,CAAC2G,EAAGC,EAAGC,CAAC,MAEpB,QAAO7G,EAAK,KAMhB,QADIK,EAAML,EAAK,aAAakC,CAAE,EACrB9B,EAAE,EAAGA,EAAEC,EAAI,OAAQD,IAAK,CAE/B,IAAIG,EAAQP,EAAK,WAAWK,EAAID,CAAC,CAAC,EAElC,GAAIG,EACF,GAAI,OAAOoG,GAAM,SACfpG,EAAM,KAAO,CAACoG,EAAGC,EAAGC,CAAC,EAEjBtG,EAAM,SAEJ,CAACA,EAAM,SAAWA,EAAM,QAAQ,MAClC6G,EAAY7G,EAAO,SAAS,EAG1B,OAAOA,EAAM,QAAQ,UAAc,KACrCA,EAAM,QAAQ,UAAU,eAAeoG,EAAG,OAAO,IAAI,WAAW,EAChEpG,EAAM,QAAQ,UAAU,eAAeqG,EAAG,OAAO,IAAI,WAAW,EAChErG,EAAM,QAAQ,UAAU,eAAesG,EAAG,OAAO,IAAI,WAAW,GAEhEtG,EAAM,QAAQ,YAAYoG,EAAGC,EAAGC,CAAC,GAIrC7G,EAAK,MAAM,MAAOO,EAAM,GAAG,MAE3B,QAAOA,EAAM,IAGvB,CAEI,OAAOP,CACR,EAYD,KAAK,UAAU,YAAc,SAAS2G,EAAGC,EAAGC,EAAG3E,EAAI,CACjD,IAAIlC,EAAO,KAGX,GAAI,CAACA,EAAK,UACR,OAAOA,EAIT,GAAIA,EAAK,SAAW,SAClB,OAAAA,EAAK,OAAO,KAAK,CACf,MAAO,cACP,OAAQ,UAAW,CACjBA,EAAK,YAAY2G,EAAGC,EAAGC,EAAG3E,CAAE,CACtC,CACA,CAAO,EAEMlC,EAQT,GAJA4G,EAAK,OAAOA,GAAM,SAAY5G,EAAK,aAAa,CAAC,EAAI4G,EACrDC,EAAK,OAAOA,GAAM,SAAY7G,EAAK,aAAa,CAAC,EAAI6G,EAGjD,OAAO3E,EAAO,IAEhB,GAAI,OAAOyE,GAAM,SACf3G,EAAK,aAAe,CAAC2G,EAAGC,EAAGC,CAAC,MAE5B,QAAO7G,EAAK,aAMhB,QADIK,EAAML,EAAK,aAAakC,CAAE,EACrB9B,EAAE,EAAGA,EAAEC,EAAI,OAAQD,IAAK,CAE/B,IAAIG,EAAQP,EAAK,WAAWK,EAAID,CAAC,CAAC,EAElC,GAAIG,EACF,GAAI,OAAOoG,GAAM,SACfpG,EAAM,aAAe,CAACoG,EAAGC,EAAGC,CAAC,EAEzBtG,EAAM,QAEHA,EAAM,UAEJA,EAAM,OACTA,EAAM,KAAOP,EAAK,MAAQ,CAAC,EAAG,EAAG,GAAI,GAGvCoH,EAAY7G,EAAO,SAAS,GAG1B,OAAOA,EAAM,QAAQ,aAAiB,KACxCA,EAAM,QAAQ,aAAa,eAAeoG,EAAG,OAAO,IAAI,WAAW,EACnEpG,EAAM,QAAQ,aAAa,eAAeqG,EAAG,OAAO,IAAI,WAAW,EACnErG,EAAM,QAAQ,aAAa,eAAesG,EAAG,OAAO,IAAI,WAAW,GAEnEtG,EAAM,QAAQ,eAAeoG,EAAGC,EAAGC,CAAC,GAIxC7G,EAAK,MAAM,cAAeO,EAAM,GAAG,MAEnC,QAAOA,EAAM,YAGvB,CAEI,OAAOP,CACR,EAgCD,KAAK,UAAU,WAAa,UAAW,CACrC,IAAIA,EAAO,KACPkD,EAAO,UACPvB,EAAGO,EAAI3B,EAGX,GAAI,CAACP,EAAK,UACR,OAAOA,EAIT,GAAIkD,EAAK,SAAW,EAElB,OAAOlD,EAAK,YACP,GAAIkD,EAAK,SAAW,EACzB,GAAI,OAAOA,EAAK,CAAC,GAAM,SACrBvB,EAAIuB,EAAK,CAAC,EAGN,OAAOhB,EAAO,MACXP,EAAE,aACLA,EAAE,WAAa,CACb,eAAgBA,EAAE,eAClB,eAAgBA,EAAE,eAClB,cAAeA,EAAE,cACjB,cAAeA,EAAE,cACjB,YAAaA,EAAE,YACf,YAAaA,EAAE,YACf,cAAeA,EAAE,cACjB,aAAcA,EAAE,YACjB,GAGH3B,EAAK,YAAc,CACjB,eAAgB,OAAO2B,EAAE,WAAW,eAAmB,IAAcA,EAAE,WAAW,eAAiB3B,EAAK,gBACxG,eAAgB,OAAO2B,EAAE,WAAW,eAAmB,IAAcA,EAAE,WAAW,eAAiB3B,EAAK,gBACxG,cAAe,OAAO2B,EAAE,WAAW,cAAkB,IAAcA,EAAE,WAAW,cAAgB3B,EAAK,eACrG,cAAe,OAAO2B,EAAE,WAAW,cAAkB,IAAcA,EAAE,WAAW,cAAgB3B,EAAK,eACrG,YAAa,OAAO2B,EAAE,WAAW,YAAgB,IAAcA,EAAE,WAAW,YAAc3B,EAAK,aAC/F,YAAa,OAAO2B,EAAE,WAAW,YAAgB,IAAcA,EAAE,WAAW,YAAc3B,EAAK,aAC/F,cAAe,OAAO2B,EAAE,WAAW,cAAkB,IAAcA,EAAE,WAAW,cAAgB3B,EAAK,eACrG,aAAc,OAAO2B,EAAE,WAAW,aAAiB,IAAcA,EAAE,WAAW,aAAe3B,EAAK,aACnG,OAIH,QAAAO,EAAQP,EAAK,WAAW,SAASkD,EAAK,CAAC,EAAG,EAAE,CAAC,EACtC3C,EAAQA,EAAM,YAAcP,EAAK,iBAEjCkD,EAAK,SAAW,IACzBvB,EAAIuB,EAAK,CAAC,EACVhB,EAAK,SAASgB,EAAK,CAAC,EAAG,EAAE,GAK3B,QADI7C,EAAML,EAAK,aAAakC,CAAE,EACrB9B,EAAE,EAAGA,EAAEC,EAAI,OAAQD,IAG1B,GAFAG,EAAQP,EAAK,WAAWK,EAAID,CAAC,CAAC,EAE1BG,EAAO,CAET,IAAI8G,EAAK9G,EAAM,YACf8G,EAAK,CACH,eAAgB,OAAO1F,EAAE,eAAmB,IAAcA,EAAE,eAAiB0F,EAAG,eAChF,eAAgB,OAAO1F,EAAE,eAAmB,IAAcA,EAAE,eAAiB0F,EAAG,eAChF,cAAe,OAAO1F,EAAE,cAAkB,IAAcA,EAAE,cAAgB0F,EAAG,cAC7E,cAAe,OAAO1F,EAAE,cAAkB,IAAcA,EAAE,cAAgB0F,EAAG,cAC7E,YAAa,OAAO1F,EAAE,YAAgB,IAAcA,EAAE,YAAc0F,EAAG,YACvE,YAAa,OAAO1F,EAAE,YAAgB,IAAcA,EAAE,YAAc0F,EAAG,YACvE,cAAe,OAAO1F,EAAE,cAAkB,IAAcA,EAAE,cAAgB0F,EAAG,cAC7E,aAAc,OAAO1F,EAAE,aAAiB,IAAcA,EAAE,aAAe0F,EAAG,YAC3E,EAGD,IAAIC,EAAS/G,EAAM,QACd+G,IAEE/G,EAAM,OACTA,EAAM,KAAOP,EAAK,MAAQ,CAAC,EAAG,EAAG,GAAI,GAIvCoH,EAAY7G,EAAO,SAAS,EAC5B+G,EAAS/G,EAAM,SAIjB+G,EAAO,eAAiBD,EAAG,eAC3BC,EAAO,eAAiBD,EAAG,eAC3BC,EAAO,cAAgBD,EAAG,cAC1BC,EAAO,cAAgBD,EAAG,cAC1BC,EAAO,YAAcD,EAAG,YACxBC,EAAO,YAAcD,EAAG,YACxBC,EAAO,cAAgBD,EAAG,cAC1BC,EAAO,aAAeD,EAAG,YACjC,CAGI,OAAOrH,CACR,EAUD,MAAM,UAAU,KAAQ,SAASkH,EAAQ,CACvC,OAAO,UAAW,CAChB,IAAIlH,EAAO,KACPsF,EAAStF,EAAK,QAGlBA,EAAK,aAAesF,EAAO,aAC3BtF,EAAK,QAAUsF,EAAO,QACtBtF,EAAK,KAAOsF,EAAO,KACnBtF,EAAK,YAAcsF,EAAO,YAG1B4B,EAAO,KAAK,IAAI,EAGZlH,EAAK,QACPsF,EAAO,OAAOtF,EAAK,OAAO,EACjBA,EAAK,MACdsF,EAAO,IAAItF,EAAK,KAAK,CAAC,EAAGA,EAAK,KAAK,CAAC,EAAGA,EAAK,KAAK,CAAC,EAAGA,EAAK,GAAG,CAEhE,CACL,EAAK,MAAM,UAAU,IAAI,EAOvB,MAAM,UAAU,MAAS,SAASkH,EAAQ,CACxC,OAAO,UAAW,CAChB,IAAIlH,EAAO,KACPsF,EAAStF,EAAK,QAGlB,OAAAA,EAAK,aAAesF,EAAO,aAC3BtF,EAAK,QAAUsF,EAAO,QACtBtF,EAAK,KAAOsF,EAAO,KACnBtF,EAAK,YAAcsF,EAAO,YAGtBtF,EAAK,QACPsF,EAAO,OAAOtF,EAAK,OAAO,EACjBA,EAAK,KACdsF,EAAO,IAAItF,EAAK,KAAK,CAAC,EAAGA,EAAK,KAAK,CAAC,EAAGA,EAAK,KAAK,CAAC,EAAGA,EAAK,GAAG,EACpDA,EAAK,UAEdA,EAAK,QAAQ,WAAW,CAAC,EACzBA,EAAK,QAAU,OACfsF,EAAO,eAAetF,CAAI,GAIrBkH,EAAO,KAAK,IAAI,CACxB,CACL,EAAK,MAAM,UAAU,KAAK,EAUxB,IAAIE,EAAc,SAAS7G,EAAOgH,EAAM,CACtCA,EAAOA,GAAQ,UAGXA,IAAS,WACXhH,EAAM,QAAU,OAAO,IAAI,aAAc,EACzCA,EAAM,QAAQ,eAAiBA,EAAM,YAAY,eACjDA,EAAM,QAAQ,eAAiBA,EAAM,YAAY,eACjDA,EAAM,QAAQ,cAAgBA,EAAM,YAAY,cAChDA,EAAM,QAAQ,cAAgBA,EAAM,YAAY,cAChDA,EAAM,QAAQ,YAAcA,EAAM,YAAY,YAC9CA,EAAM,QAAQ,YAAcA,EAAM,YAAY,YAC9CA,EAAM,QAAQ,cAAgBA,EAAM,YAAY,cAChDA,EAAM,QAAQ,aAAeA,EAAM,YAAY,aAE3C,OAAOA,EAAM,QAAQ,UAAc,KACrCA,EAAM,QAAQ,UAAU,eAAeA,EAAM,KAAK,CAAC,EAAG,OAAO,IAAI,WAAW,EAC5EA,EAAM,QAAQ,UAAU,eAAeA,EAAM,KAAK,CAAC,EAAG,OAAO,IAAI,WAAW,EAC5EA,EAAM,QAAQ,UAAU,eAAeA,EAAM,KAAK,CAAC,EAAG,OAAO,IAAI,WAAW,GAE5EA,EAAM,QAAQ,YAAYA,EAAM,KAAK,CAAC,EAAGA,EAAM,KAAK,CAAC,EAAGA,EAAM,KAAK,CAAC,CAAC,EAGnE,OAAOA,EAAM,QAAQ,aAAiB,KACxCA,EAAM,QAAQ,aAAa,eAAeA,EAAM,aAAa,CAAC,EAAG,OAAO,IAAI,WAAW,EACvFA,EAAM,QAAQ,aAAa,eAAeA,EAAM,aAAa,CAAC,EAAG,OAAO,IAAI,WAAW,EACvFA,EAAM,QAAQ,aAAa,eAAeA,EAAM,aAAa,CAAC,EAAG,OAAO,IAAI,WAAW,GAEvFA,EAAM,QAAQ,eAAeA,EAAM,aAAa,CAAC,EAAGA,EAAM,aAAa,CAAC,EAAGA,EAAM,aAAa,CAAC,CAAC,IAGlGA,EAAM,QAAU,OAAO,IAAI,mBAAoB,EAC/CA,EAAM,QAAQ,IAAI,eAAeA,EAAM,QAAS,OAAO,IAAI,WAAW,GAGxEA,EAAM,QAAQ,QAAQA,EAAM,KAAK,EAG5BA,EAAM,SACTA,EAAM,QAAQ,MAAMA,EAAM,IAAK,EAAI,EAAE,KAAKA,EAAM,IAAK,EAAI,CAE5D,CACH", "x_google_ignoreList": [0]}