{"version": 3, "file": "ts-tags.js", "sources": ["../../../../node_modules/.pnpm/@shikijs+langs@3.4.1/node_modules/@shikijs/langs/dist/es-tag-css.mjs", "../../../../node_modules/.pnpm/@shikijs+langs@3.4.1/node_modules/@shikijs/langs/dist/es-tag-glsl.mjs", "../../../../node_modules/.pnpm/@shikijs+langs@3.4.1/node_modules/@shikijs/langs/dist/es-tag-html.mjs", "../../../../node_modules/.pnpm/@shikijs+langs@3.4.1/node_modules/@shikijs/langs/dist/es-tag-sql.mjs", "../../../../node_modules/.pnpm/@shikijs+langs@3.4.1/node_modules/@shikijs/langs/dist/es-tag-xml.mjs", "../../../../node_modules/.pnpm/@shikijs+langs@3.4.1/node_modules/@shikijs/langs/dist/ts-tags.mjs"], "sourcesContent": ["import typescript from './typescript.mjs'\nimport css from './css.mjs'\nimport javascript from './javascript.mjs'\n\nconst lang = Object.freeze(JSON.parse(\"{\\\"fileTypes\\\":[\\\"js\\\",\\\"jsx\\\",\\\"ts\\\",\\\"tsx\\\",\\\"html\\\",\\\"vue\\\",\\\"svelte\\\",\\\"php\\\",\\\"res\\\"],\\\"injectTo\\\":[\\\"source.ts\\\",\\\"source.js\\\"],\\\"injectionSelector\\\":\\\"L:source.js -comment -string, L:source.js -comment -string, L:source.jsx -comment -string,  L:source.js.jsx -comment -string, L:source.ts -comment -string, L:source.tsx -comment -string, L:source.rescript -comment -string, L:source.vue -comment -string, L:source.svelte -comment -string, L:source.php -comment -string, L:source.rescript -comment -string\\\",\\\"injections\\\":{\\\"L:source\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"<\\\",\\\"name\\\":\\\"invalid.illegal.bad-angle-bracket.html\\\"}]}},\\\"name\\\":\\\"es-tag-css\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)(\\\\\\\\s?/\\\\\\\\*\\\\\\\\s?((?:|inline-)css)\\\\\\\\s?\\\\\\\\*/\\\\\\\\s?)(`)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"comment.block\\\"}},\\\"end\\\":\\\"(`)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.ts#template-substitution-element\\\"},{\\\"include\\\":\\\"source.css\\\"},{\\\"include\\\":\\\"inline.es6-htmlx#template\\\"}]},{\\\"begin\\\":\\\"(?i)(\\\\\\\\s*((?:|inline-)css))(`)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"comment.block\\\"}},\\\"end\\\":\\\"(`)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.ts#template-substitution-element\\\"},{\\\"include\\\":\\\"source.css\\\"},{\\\"include\\\":\\\"inline.es6-htmlx#template\\\"},{\\\"include\\\":\\\"string.quoted.other.template.js\\\"}]},{\\\"begin\\\":\\\"(?i)(?<=[(,:=\\\\\\\\s]|\\\\\\\\$\\\\\\\\()\\\\\\\\s*(((/\\\\\\\\*)|(//))\\\\\\\\s?((?:|inline-)css) {0,1000}\\\\\\\\*?/?) {0,1000}$\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"comment.line\\\"}},\\\"end\\\":\\\"(`).*\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G()\\\",\\\"end\\\":\\\"(`)\\\"},{\\\"include\\\":\\\"source.ts#template-substitution-element\\\"},{\\\"include\\\":\\\"source.css\\\"}]},{\\\"begin\\\":\\\"(\\\\\\\\$\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.tag\\\"}},\\\"end\\\":\\\"(})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.tag\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"source.ts#template-substitution-element\\\"},{\\\"include\\\":\\\"source.js\\\"}]}],\\\"scopeName\\\":\\\"inline.es6-css\\\",\\\"embeddedLangs\\\":[\\\"typescript\\\",\\\"css\\\",\\\"javascript\\\"]}\"))\n\nexport default [\n...typescript,\n...css,\n...javascript,\nlang\n]\n", "import typescript from './typescript.mjs'\nimport glsl from './glsl.mjs'\nimport javascript from './javascript.mjs'\n\nconst lang = Object.freeze(JSON.parse(\"{\\\"fileTypes\\\":[\\\"js\\\",\\\"jsx\\\",\\\"ts\\\",\\\"tsx\\\",\\\"html\\\",\\\"vue\\\",\\\"svelte\\\",\\\"php\\\",\\\"res\\\"],\\\"injectTo\\\":[\\\"source.ts\\\",\\\"source.js\\\"],\\\"injectionSelector\\\":\\\"L:source.js -comment -string, L:source.js -comment -string, L:source.jsx -comment -string,  L:source.js.jsx -comment -string, L:source.ts -comment -string, L:source.tsx -comment -string, L:source.rescript -comment -string\\\",\\\"injections\\\":{\\\"L:source\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"<\\\",\\\"name\\\":\\\"invalid.illegal.bad-angle-bracket.html\\\"}]}},\\\"name\\\":\\\"es-tag-glsl\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)(\\\\\\\\s?/\\\\\\\\*\\\\\\\\s?((?:|inline-)glsl)\\\\\\\\s?\\\\\\\\*/\\\\\\\\s?)(`)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"comment.block\\\"}},\\\"end\\\":\\\"(`)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.ts#template-substitution-element\\\"},{\\\"include\\\":\\\"source.glsl\\\"},{\\\"include\\\":\\\"inline.es6-htmlx#template\\\"}]},{\\\"begin\\\":\\\"(?i)(\\\\\\\\s*((?:|inline-)glsl))(`)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"comment.block\\\"}},\\\"end\\\":\\\"(`)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.ts#template-substitution-element\\\"},{\\\"include\\\":\\\"source.glsl\\\"},{\\\"include\\\":\\\"inline.es6-htmlx#template\\\"},{\\\"include\\\":\\\"string.quoted.other.template.js\\\"}]},{\\\"begin\\\":\\\"(?i)(?<=[(,:=\\\\\\\\s]|\\\\\\\\$\\\\\\\\()\\\\\\\\s*(((/\\\\\\\\*)|(//))\\\\\\\\s?((?:|inline-)glsl) {0,1000}\\\\\\\\*?/?) {0,1000}$\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"comment.line\\\"}},\\\"end\\\":\\\"(`).*\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G()\\\",\\\"end\\\":\\\"(`)\\\"},{\\\"include\\\":\\\"source.ts#template-substitution-element\\\"},{\\\"include\\\":\\\"source.glsl\\\"}]},{\\\"begin\\\":\\\"(\\\\\\\\$\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.tag\\\"}},\\\"end\\\":\\\"(})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.tag\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"source.ts#template-substitution-element\\\"},{\\\"include\\\":\\\"source.js\\\"}]}],\\\"scopeName\\\":\\\"inline.es6-glsl\\\",\\\"embeddedLangs\\\":[\\\"typescript\\\",\\\"glsl\\\",\\\"javascript\\\"]}\"))\n\nexport default [\n...typescript,\n...glsl,\n...javascript,\nlang\n]\n", "import typescript from './typescript.mjs'\nimport html from './html.mjs'\nimport javascript from './javascript.mjs'\n\nconst lang = Object.freeze(JSON.parse(\"{\\\"fileTypes\\\":[\\\"js\\\",\\\"jsx\\\",\\\"ts\\\",\\\"tsx\\\",\\\"html\\\",\\\"vue\\\",\\\"svelte\\\",\\\"php\\\",\\\"res\\\"],\\\"injectTo\\\":[\\\"source.ts\\\",\\\"source.js\\\"],\\\"injectionSelector\\\":\\\"L:source.js -comment -string, L:source.js -comment -string, L:source.jsx -comment -string,  L:source.js.jsx -comment -string, L:source.ts -comment -string, L:source.tsx -comment -string, L:source.rescript -comment -string\\\",\\\"injections\\\":{\\\"L:source\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"<\\\",\\\"name\\\":\\\"invalid.illegal.bad-angle-bracket.html\\\"}]}},\\\"name\\\":\\\"es-tag-html\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)(\\\\\\\\s?/\\\\\\\\*\\\\\\\\s?(html|template|inline-html|inline-template)\\\\\\\\s?\\\\\\\\*/\\\\\\\\s?)(`)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"comment.block\\\"}},\\\"end\\\":\\\"(`)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.ts#template-substitution-element\\\"},{\\\"include\\\":\\\"text.html.basic\\\"},{\\\"include\\\":\\\"inline.es6-htmlx#template\\\"}]},{\\\"begin\\\":\\\"(?i)(\\\\\\\\s*(html|template|inline-html|inline-template))(`)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"comment.block\\\"}},\\\"end\\\":\\\"(`)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.ts#template-substitution-element\\\"},{\\\"include\\\":\\\"text.html.basic\\\"},{\\\"include\\\":\\\"inline.es6-htmlx#template\\\"},{\\\"include\\\":\\\"string.quoted.other.template.js\\\"}]},{\\\"begin\\\":\\\"(?i)(?<=[(,:=\\\\\\\\s]|\\\\\\\\$\\\\\\\\()\\\\\\\\s*(((/\\\\\\\\*)|(//))\\\\\\\\s?(html|template|inline-html|inline-template) {0,1000}\\\\\\\\*?/?) {0,1000}$\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"comment.line\\\"}},\\\"end\\\":\\\"(`).*\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G()\\\",\\\"end\\\":\\\"(`)\\\"},{\\\"include\\\":\\\"source.ts#template-substitution-element\\\"},{\\\"include\\\":\\\"text.html.basic\\\"}]},{\\\"begin\\\":\\\"(\\\\\\\\$\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.tag\\\"}},\\\"end\\\":\\\"(})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.tag\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"source.ts#template-substitution-element\\\"},{\\\"include\\\":\\\"source.js\\\"}]},{\\\"begin\\\":\\\"(\\\\\\\\$\\\\\\\\(`)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.tag\\\"}},\\\"end\\\":\\\"(`\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.tag\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"source.ts#template-substitution-element\\\"},{\\\"include\\\":\\\"source.js\\\"}]}],\\\"scopeName\\\":\\\"inline.es6-html\\\",\\\"embeddedLangs\\\":[\\\"typescript\\\",\\\"html\\\",\\\"javascript\\\"]}\"))\n\nexport default [\n...typescript,\n...html,\n...javascript,\nlang\n]\n", "import typescript from './typescript.mjs'\nimport sql from './sql.mjs'\n\nconst lang = Object.freeze(JSON.parse(\"{\\\"fileTypes\\\":[\\\"js\\\",\\\"jsx\\\",\\\"ts\\\",\\\"tsx\\\",\\\"html\\\",\\\"vue\\\",\\\"svelte\\\",\\\"php\\\",\\\"res\\\"],\\\"injectTo\\\":[\\\"source.ts\\\",\\\"source.js\\\"],\\\"injectionSelector\\\":\\\"L:source.js -comment -string, L:source.jsx -comment -string,  L:source.js.jsx -comment -string, L:source.ts -comment -string, L:source.tsx -comment -string, L:source.rescript -comment -string\\\",\\\"injections\\\":{\\\"L:source\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"<\\\",\\\"name\\\":\\\"invalid.illegal.bad-angle-bracket.html\\\"}]}},\\\"name\\\":\\\"es-tag-sql\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)\\\\\\\\b(\\\\\\\\w+\\\\\\\\.sql)\\\\\\\\s*(`)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.parameter\\\"}},\\\"end\\\":\\\"(`)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.ts#template-substitution-element\\\"},{\\\"include\\\":\\\"source.ts#string-character-escape\\\"},{\\\"include\\\":\\\"source.sql\\\"},{\\\"include\\\":\\\"source.plpgsql.postgres\\\"},{\\\"match\\\":\\\".\\\"}]},{\\\"begin\\\":\\\"(?i)(\\\\\\\\s?/?\\\\\\\\*?\\\\\\\\s?((?:|inline-)sql)\\\\\\\\s?\\\\\\\\*?/?\\\\\\\\s?)(`)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"comment.block\\\"}},\\\"end\\\":\\\"(`)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.ts#template-substitution-element\\\"},{\\\"include\\\":\\\"source.ts#string-character-escape\\\"},{\\\"include\\\":\\\"source.sql\\\"},{\\\"include\\\":\\\"source.plpgsql.postgres\\\"},{\\\"match\\\":\\\".\\\"}]},{\\\"begin\\\":\\\"(?i)(?<=[(,:=\\\\\\\\s]|\\\\\\\\$\\\\\\\\()\\\\\\\\s*(((/\\\\\\\\*)|(//))\\\\\\\\s?((?:|inline-)sql) {0,1000}\\\\\\\\*?/?) {0,1000}$\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"comment.line\\\"}},\\\"end\\\":\\\"(`)\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G()\\\",\\\"end\\\":\\\"(`)\\\"},{\\\"include\\\":\\\"source.ts#template-substitution-element\\\"},{\\\"include\\\":\\\"source.ts#string-character-escape\\\"},{\\\"include\\\":\\\"source.sql\\\"},{\\\"include\\\":\\\"source.plpgsql.postgres\\\"},{\\\"match\\\":\\\".\\\"}]}],\\\"scopeName\\\":\\\"inline.es6-sql\\\",\\\"embeddedLangs\\\":[\\\"typescript\\\",\\\"sql\\\"]}\"))\n\nexport default [\n...typescript,\n...sql,\nlang\n]\n", "import xml from './xml.mjs'\n\nconst lang = Object.freeze(JSON.parse(\"{\\\"fileTypes\\\":[\\\"js\\\",\\\"jsx\\\",\\\"ts\\\",\\\"tsx\\\",\\\"html\\\",\\\"vue\\\",\\\"svelte\\\",\\\"php\\\",\\\"res\\\"],\\\"injectTo\\\":[\\\"source.ts\\\",\\\"source.js\\\"],\\\"injectionSelector\\\":\\\"L:source.js -comment -string, L:source.js -comment -string, L:source.jsx -comment -string,  L:source.js.jsx -comment -string, L:source.ts -comment -string, L:source.tsx -comment -string, L:source.rescript -comment -string\\\",\\\"injections\\\":{\\\"L:source\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"<\\\",\\\"name\\\":\\\"invalid.illegal.bad-angle-bracket.html\\\"}]}},\\\"name\\\":\\\"es-tag-xml\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)(\\\\\\\\s?/\\\\\\\\*\\\\\\\\s?(xml|svg|inline-svg|inline-xml)\\\\\\\\s?\\\\\\\\*/\\\\\\\\s?)(`)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"comment.block\\\"}},\\\"end\\\":\\\"(`)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.xml\\\"}]},{\\\"begin\\\":\\\"(?i)(\\\\\\\\s*((?:|inline-)xml))(`)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"comment.block\\\"}},\\\"end\\\":\\\"(`)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.xml\\\"}]},{\\\"begin\\\":\\\"(?i)(?<=[(,:=\\\\\\\\s]|\\\\\\\\$\\\\\\\\()\\\\\\\\s*(((/\\\\\\\\*)|(//))\\\\\\\\s?(xml|svg|inline-svg|inline-xml) {0,1000}\\\\\\\\*?/?) {0,1000}$\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"comment.line\\\"}},\\\"end\\\":\\\"(`).*\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G()\\\",\\\"end\\\":\\\"(`)\\\"},{\\\"include\\\":\\\"text.xml\\\"}]}],\\\"scopeName\\\":\\\"inline.es6-xml\\\",\\\"embeddedLangs\\\":[\\\"xml\\\"]}\"))\n\nexport default [\n...xml,\nlang\n]\n", "import typescript from './typescript.mjs'\nimport es_tag_css from './es-tag-css.mjs'\nimport es_tag_glsl from './es-tag-glsl.mjs'\nimport es_tag_html from './es-tag-html.mjs'\nimport es_tag_sql from './es-tag-sql.mjs'\nimport es_tag_xml from './es-tag-xml.mjs'\n\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"TypeScript with Tags\\\",\\\"name\\\":\\\"ts-tags\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.ts\\\"}],\\\"scopeName\\\":\\\"source.ts.tags\\\",\\\"embeddedLangs\\\":[\\\"typescript\\\",\\\"es-tag-css\\\",\\\"es-tag-glsl\\\",\\\"es-tag-html\\\",\\\"es-tag-sql\\\",\\\"es-tag-xml\\\"],\\\"aliases\\\":[\\\"lit\\\"]}\"))\n\nexport default [\n...typescript,\n...es_tag_css,\n...es_tag_glsl,\n...es_tag_html,\n...es_tag_sql,\n...es_tag_xml,\nlang\n]\n"], "names": ["lang", "es_tag_css", "typescript", "css", "javascript", "es_tag_glsl", "glsl", "es_tag_html", "html", "es_tag_sql", "sql", "es_tag_xml", "xml", "tsTags"], "mappings": "yNAIA,MAAMA,EAAO,OAAO,OAAO,KAAK,MAAM,8uDAAg7D,CAAC,EAEx8DC,EAAA,CACf,GAAGC,EACH,GAAGC,EACH,GAAGC,EACHJ,CACA,ECPMA,EAAO,OAAO,OAAO,KAAK,MAAM,mnDAAqzD,CAAC,EAE70DK,EAAA,CACf,GAAGH,EACH,GAAGI,EACH,GAAGF,EACHJ,CACA,ECPMA,EAAO,OAAO,OAAO,KAAK,MAAM,g7DAAopE,CAAC,EAE5qEO,EAAA,CACf,GAAGL,EACH,GAAGM,EACH,GAAGJ,EACHJ,CACA,ECRMA,EAAO,OAAO,OAAO,KAAK,MAAM,shDAA4sD,CAAC,EAEpuDS,EAAA,CACf,GAAGP,EACH,GAAGQ,EACHV,CACA,ECPMA,EAAO,OAAO,OAAO,KAAK,MAAM,smCAA0uC,CAAC,EAElwCW,EAAA,CACf,GAAGC,EACHZ,CACA,ECAMA,EAAO,OAAO,OAAO,KAAK,MAAM,+OAAmR,CAAC,EAE3Sa,EAAA,CACf,GAAGX,EACH,GAAGD,EACH,GAAGI,EACH,GAAGE,EACH,GAAGE,EACH,GAAGE,EACHX,CACA", "x_google_ignoreList": [0, 1, 2, 3, 4, 5]}