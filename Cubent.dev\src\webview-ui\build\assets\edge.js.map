{"version": 3, "file": "edge.js", "sources": ["../../../../node_modules/.pnpm/@shikijs+langs@3.4.1/node_modules/@shikijs/langs/dist/edge.mjs"], "sourcesContent": ["import typescript from './typescript.mjs'\nimport html from './html.mjs'\nimport html_derivative from './html-derivative.mjs'\n\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Edge\\\",\\\"injections\\\":{\\\"text.html.edge - (meta.embedded | meta.tag | comment.block.edge), L:(text.html.edge meta.tag - (comment.block.edge | meta.embedded.block.edge)), L:(source.ts.embedded.html - (comment.block.edge | meta.embedded.block.edge))\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#escapedMustache\\\"},{\\\"include\\\":\\\"#safeMustache\\\"},{\\\"include\\\":\\\"#mustache\\\"},{\\\"include\\\":\\\"#nonSeekableTag\\\"},{\\\"include\\\":\\\"#tag\\\"}]}},\\\"name\\\":\\\"edge\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.html.basic\\\"},{\\\"include\\\":\\\"text.html.derivative\\\"}],\\\"repository\\\":{\\\"comment\\\":{\\\"begin\\\":\\\"\\\\\\\\{\\\\\\\\{--\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.begin.edge\\\"}},\\\"end\\\":\\\"--}}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.end.edge\\\"}},\\\"name\\\":\\\"comment.block\\\"},\\\"escapedMustache\\\":{\\\"begin\\\":\\\"@\\\\\\\\{\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.begin.edge\\\"}},\\\"end\\\":\\\"}}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.end.edge\\\"}},\\\"name\\\":\\\"comment.block\\\"},\\\"mustache\\\":{\\\"begin\\\":\\\"\\\\\\\\{\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.mustache.begin\\\"}},\\\"end\\\":\\\"}}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.mustache.end\\\"}},\\\"name\\\":\\\"meta.embedded.block.javascript\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.ts#expression\\\"}]},\\\"nonSeekableTag\\\":{\\\"captures\\\":{\\\"2\\\":{\\\"name\\\":\\\"support.function.edge\\\"}},\\\"match\\\":\\\"^(\\\\\\\\s*)((@{1,2})(!)?([.A-Z_a-z]+))(~)?$\\\",\\\"name\\\":\\\"meta.embedded.block.javascript\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.ts#expression\\\"}]},\\\"safeMustache\\\":{\\\"begin\\\":\\\"\\\\\\\\{\\\\\\\\{\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.mustache.begin\\\"}},\\\"end\\\":\\\"}}}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.mustache.end\\\"}},\\\"name\\\":\\\"meta.embedded.block.javascript\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.ts#expression\\\"}]},\\\"tag\\\":{\\\"begin\\\":\\\"^(\\\\\\\\s*)((@{1,2})(!)?([.A-Z_a-z]+)(\\\\\\\\s{0,2}))(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"2\\\":{\\\"name\\\":\\\"support.function.edge\\\"},\\\"7\\\":{\\\"name\\\":\\\"punctuation.paren.open\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.paren.close\\\"}},\\\"name\\\":\\\"meta.embedded.block.javascript\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.ts#expression\\\"}]}},\\\"scopeName\\\":\\\"text.html.edge\\\",\\\"embeddedLangs\\\":[\\\"typescript\\\",\\\"html\\\",\\\"html-derivative\\\"]}\"))\n\nexport default [\n...typescript,\n...html,\n...html_derivative,\nlang\n]\n"], "names": ["lang", "edge", "typescript", "html", "html_derivative"], "mappings": "qIAIA,MAAMA,EAAO,OAAO,OAAO,KAAK,MAAM,0jEAA8zE,CAAC,EAEt1EC,EAAA,CACf,GAAGC,EACH,GAAGC,EACH,GAAGC,EACHJ,CACA", "x_google_ignoreList": [0]}