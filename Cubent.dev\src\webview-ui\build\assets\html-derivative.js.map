{"version": 3, "file": "html-derivative.js", "sources": ["../../../../node_modules/.pnpm/@shikijs+langs@3.4.1/node_modules/@shikijs/langs/dist/html-derivative.mjs"], "sourcesContent": ["import html from './html.mjs'\n\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"HTML (Derivative)\\\",\\\"injections\\\":{\\\"R:text.html - (comment.block, text.html meta.embedded, meta.tag.*.*.html, meta.tag.*.*.*.html, meta.tag.*.*.*.*.html)\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"<\\\",\\\"name\\\":\\\"invalid.illegal.bad-angle-bracket.html\\\"}]}},\\\"name\\\":\\\"html-derivative\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.html.basic#core-minus-invalid\\\"},{\\\"begin\\\":\\\"(</?)(\\\\\\\\w[^<>\\\\\\\\s]*)(?<!/)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.tag.begin.html\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.tag.html\\\"}},\\\"end\\\":\\\"((?: ?/)?>)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.tag.end.html\\\"}},\\\"name\\\":\\\"meta.tag.other.unrecognized.html.derivative\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.html.basic#attribute\\\"}]}],\\\"scopeName\\\":\\\"text.html.derivative\\\",\\\"embeddedLangs\\\":[\\\"html\\\"]}\"))\n\nexport default [\n...html,\nlang\n]\n"], "names": ["lang", "html_derivative", "html"], "mappings": "kEAEA,MAAMA,EAAO,OAAO,OAAO,KAAK,MAAM,kuBAA8yB,CAAC,EAEt0BC,EAAA,CACf,GAAGC,EACHF,CACA", "x_google_ignoreList": [0]}