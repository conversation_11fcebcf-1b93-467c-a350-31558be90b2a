import{aU as nn,aV as An,aW as rn,aX as an,aY as sn,aZ as se,a_ as Ln,aF as we,_ as h,g as In,s as Wn,t as On,q as Hn,a as Nn,b as Vn,c as _t,d as Bt,e as Pn,a$ as at,l as Kt,k as zn,j as Rn,z as qn,u as Bn}from"./index.js";import{b as Zn,t as Oe,c as Xn,a as Gn,l as Qn}from"./linear.js";import{i as jn}from"./init.js";function $n(t,e){let n;if(e===void 0)for(const r of t)r!=null&&(n<r||n===void 0&&r>=r)&&(n=r);else{let r=-1;for(let a of t)(a=e(a,++r,t))!=null&&(n<a||n===void 0&&a>=a)&&(n=a)}return n}function Jn(t,e){let n;if(e===void 0)for(const r of t)r!=null&&(n>r||n===void 0&&r>=r)&&(n=r);else{let r=-1;for(let a of t)(a=e(a,++r,t))!=null&&(n>a||n===void 0&&a>=a)&&(n=a)}return n}function Kn(t){return t}var Xt=1,oe=2,ke=3,Zt=4,He=1e-6;function tr(t){return"translate("+t+",0)"}function er(t){return"translate(0,"+t+")"}function nr(t){return e=>+t(e)}function rr(t,e){return e=Math.max(0,t.bandwidth()-e*2)/2,t.round()&&(e=Math.round(e)),n=>+t(n)+e}function ar(){return!this.__axis}function on(t,e){var n=[],r=null,a=null,i=6,s=6,k=3,M=typeof window<"u"&&window.devicePixelRatio>1?0:.5,T=t===Xt||t===Zt?-1:1,g=t===Zt||t===oe?"x":"y",U=t===Xt||t===ke?tr:er;function C(x){var X=r??(e.ticks?e.ticks.apply(e,n):e.domain()),O=a??(e.tickFormat?e.tickFormat.apply(e,n):Kn),D=Math.max(i,0)+k,I=e.range(),V=+I[0]+M,W=+I[I.length-1]+M,B=(e.bandwidth?rr:nr)(e.copy(),M),j=x.selection?x.selection():x,w=j.selectAll(".domain").data([null]),H=j.selectAll(".tick").data(X,e).order(),b=H.exit(),F=H.enter().append("g").attr("class","tick"),_=H.select("line"),S=H.select("text");w=w.merge(w.enter().insert("path",".tick").attr("class","domain").attr("stroke","currentColor")),H=H.merge(F),_=_.merge(F.append("line").attr("stroke","currentColor").attr(g+"2",T*i)),S=S.merge(F.append("text").attr("fill","currentColor").attr(g,T*D).attr("dy",t===Xt?"0em":t===ke?"0.71em":"0.32em")),x!==j&&(w=w.transition(x),H=H.transition(x),_=_.transition(x),S=S.transition(x),b=b.transition(x).attr("opacity",He).attr("transform",function(p){return isFinite(p=B(p))?U(p+M):this.getAttribute("transform")}),F.attr("opacity",He).attr("transform",function(p){var Y=this.parentNode.__axis;return U((Y&&isFinite(Y=Y(p))?Y:B(p))+M)})),b.remove(),w.attr("d",t===Zt||t===oe?s?"M"+T*s+","+V+"H"+M+"V"+W+"H"+T*s:"M"+M+","+V+"V"+W:s?"M"+V+","+T*s+"V"+M+"H"+W+"V"+T*s:"M"+V+","+M+"H"+W),H.attr("opacity",1).attr("transform",function(p){return U(B(p)+M)}),_.attr(g+"2",T*i),S.attr(g,T*D).text(O),j.filter(ar).attr("fill","none").attr("font-size",10).attr("font-family","sans-serif").attr("text-anchor",t===oe?"start":t===Zt?"end":"middle"),j.each(function(){this.__axis=B})}return C.scale=function(x){return arguments.length?(e=x,C):e},C.ticks=function(){return n=Array.from(arguments),C},C.tickArguments=function(x){return arguments.length?(n=x==null?[]:Array.from(x),C):n.slice()},C.tickValues=function(x){return arguments.length?(r=x==null?null:Array.from(x),C):r&&r.slice()},C.tickFormat=function(x){return arguments.length?(a=x,C):a},C.tickSize=function(x){return arguments.length?(i=s=+x,C):i},C.tickSizeInner=function(x){return arguments.length?(i=+x,C):i},C.tickSizeOuter=function(x){return arguments.length?(s=+x,C):s},C.tickPadding=function(x){return arguments.length?(k=+x,C):k},C.offset=function(x){return arguments.length?(M=+x,C):M},C}function ir(t){return on(Xt,t)}function sr(t){return on(ke,t)}const or=Math.PI/180,cr=180/Math.PI,te=18,cn=.96422,un=1,ln=.82521,fn=4/29,St=6/29,hn=3*St*St,ur=St*St*St;function dn(t){if(t instanceof ft)return new ft(t.l,t.a,t.b,t.opacity);if(t instanceof dt)return mn(t);t instanceof nn||(t=An(t));var e=fe(t.r),n=fe(t.g),r=fe(t.b),a=ce((.2225045*e+.7168786*n+.0606169*r)/un),i,s;return e===n&&n===r?i=s=a:(i=ce((.4360747*e+.3850649*n+.1430804*r)/cn),s=ce((.0139322*e+.0971045*n+.7141733*r)/ln)),new ft(116*a-16,500*(i-a),200*(a-s),t.opacity)}function lr(t,e,n,r){return arguments.length===1?dn(t):new ft(t,e,n,r??1)}function ft(t,e,n,r){this.l=+t,this.a=+e,this.b=+n,this.opacity=+r}rn(ft,lr,an(sn,{brighter(t){return new ft(this.l+te*(t??1),this.a,this.b,this.opacity)},darker(t){return new ft(this.l-te*(t??1),this.a,this.b,this.opacity)},rgb(){var t=(this.l+16)/116,e=isNaN(this.a)?t:t+this.a/500,n=isNaN(this.b)?t:t-this.b/200;return e=cn*ue(e),t=un*ue(t),n=ln*ue(n),new nn(le(3.1338561*e-1.6168667*t-.4906146*n),le(-.9787684*e+1.9161415*t+.033454*n),le(.0719453*e-.2289914*t+1.4052427*n),this.opacity)}}));function ce(t){return t>ur?Math.pow(t,1/3):t/hn+fn}function ue(t){return t>St?t*t*t:hn*(t-fn)}function le(t){return 255*(t<=.0031308?12.92*t:1.055*Math.pow(t,1/2.4)-.055)}function fe(t){return(t/=255)<=.04045?t/12.92:Math.pow((t+.055)/1.055,2.4)}function fr(t){if(t instanceof dt)return new dt(t.h,t.c,t.l,t.opacity);if(t instanceof ft||(t=dn(t)),t.a===0&&t.b===0)return new dt(NaN,0<t.l&&t.l<100?0:NaN,t.l,t.opacity);var e=Math.atan2(t.b,t.a)*cr;return new dt(e<0?e+360:e,Math.sqrt(t.a*t.a+t.b*t.b),t.l,t.opacity)}function pe(t,e,n,r){return arguments.length===1?fr(t):new dt(t,e,n,r??1)}function dt(t,e,n,r){this.h=+t,this.c=+e,this.l=+n,this.opacity=+r}function mn(t){if(isNaN(t.h))return new ft(t.l,0,0,t.opacity);var e=t.h*or;return new ft(t.l,Math.cos(e)*t.c,Math.sin(e)*t.c,t.opacity)}rn(dt,pe,an(sn,{brighter(t){return new dt(this.h,this.c,this.l+te*(t??1),this.opacity)},darker(t){return new dt(this.h,this.c,this.l-te*(t??1),this.opacity)},rgb(){return mn(this).rgb()}}));function hr(t){return function(e,n){var r=t((e=pe(e)).h,(n=pe(n)).h),a=se(e.c,n.c),i=se(e.l,n.l),s=se(e.opacity,n.opacity);return function(k){return e.h=r(k),e.c=a(k),e.l=i(k),e.opacity=s(k),e+""}}}const dr=hr(Ln);function mr(t,e){t=t.slice();var n=0,r=t.length-1,a=t[n],i=t[r],s;return i<a&&(s=n,n=r,r=s,s=a,a=i,i=s),t[n]=e.floor(a),t[r]=e.ceil(i),t}const he=new Date,de=new Date;function et(t,e,n,r){function a(i){return t(i=arguments.length===0?new Date:new Date(+i)),i}return a.floor=i=>(t(i=new Date(+i)),i),a.ceil=i=>(t(i=new Date(i-1)),e(i,1),t(i),i),a.round=i=>{const s=a(i),k=a.ceil(i);return i-s<k-i?s:k},a.offset=(i,s)=>(e(i=new Date(+i),s==null?1:Math.floor(s)),i),a.range=(i,s,k)=>{const M=[];if(i=a.ceil(i),k=k==null?1:Math.floor(k),!(i<s)||!(k>0))return M;let T;do M.push(T=new Date(+i)),e(i,k),t(i);while(T<i&&i<s);return M},a.filter=i=>et(s=>{if(s>=s)for(;t(s),!i(s);)s.setTime(s-1)},(s,k)=>{if(s>=s)if(k<0)for(;++k<=0;)for(;e(s,-1),!i(s););else for(;--k>=0;)for(;e(s,1),!i(s););}),n&&(a.count=(i,s)=>(he.setTime(+i),de.setTime(+s),t(he),t(de),Math.floor(n(he,de))),a.every=i=>(i=Math.floor(i),!isFinite(i)||!(i>0)?null:i>1?a.filter(r?s=>r(s)%i===0:s=>a.count(0,s)%i===0):a)),a}const Yt=et(()=>{},(t,e)=>{t.setTime(+t+e)},(t,e)=>e-t);Yt.every=t=>(t=Math.floor(t),!isFinite(t)||!(t>0)?null:t>1?et(e=>{e.setTime(Math.floor(e/t)*t)},(e,n)=>{e.setTime(+e+n*t)},(e,n)=>(n-e)/t):Yt);Yt.range;const mt=1e3,ct=mt*60,gt=ct*60,yt=gt*24,Ce=yt*7,Ne=yt*30,me=yt*365,vt=et(t=>{t.setTime(t-t.getMilliseconds())},(t,e)=>{t.setTime(+t+e*mt)},(t,e)=>(e-t)/mt,t=>t.getUTCSeconds());vt.range;const Wt=et(t=>{t.setTime(t-t.getMilliseconds()-t.getSeconds()*mt)},(t,e)=>{t.setTime(+t+e*ct)},(t,e)=>(e-t)/ct,t=>t.getMinutes());Wt.range;const gr=et(t=>{t.setUTCSeconds(0,0)},(t,e)=>{t.setTime(+t+e*ct)},(t,e)=>(e-t)/ct,t=>t.getUTCMinutes());gr.range;const Ot=et(t=>{t.setTime(t-t.getMilliseconds()-t.getSeconds()*mt-t.getMinutes()*ct)},(t,e)=>{t.setTime(+t+e*gt)},(t,e)=>(e-t)/gt,t=>t.getHours());Ot.range;const yr=et(t=>{t.setUTCMinutes(0,0,0)},(t,e)=>{t.setTime(+t+e*gt)},(t,e)=>(e-t)/gt,t=>t.getUTCHours());yr.range;const Tt=et(t=>t.setHours(0,0,0,0),(t,e)=>t.setDate(t.getDate()+e),(t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*ct)/yt,t=>t.getDate()-1);Tt.range;const De=et(t=>{t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+e)},(t,e)=>(e-t)/yt,t=>t.getUTCDate()-1);De.range;const kr=et(t=>{t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+e)},(t,e)=>(e-t)/yt,t=>Math.floor(t/yt));kr.range;function wt(t){return et(e=>{e.setDate(e.getDate()-(e.getDay()+7-t)%7),e.setHours(0,0,0,0)},(e,n)=>{e.setDate(e.getDate()+n*7)},(e,n)=>(n-e-(n.getTimezoneOffset()-e.getTimezoneOffset())*ct)/Ce)}const Vt=wt(0),Ht=wt(1),gn=wt(2),yn=wt(3),xt=wt(4),kn=wt(5),pn=wt(6);Vt.range;Ht.range;gn.range;yn.range;xt.range;kn.range;pn.range;function Ct(t){return et(e=>{e.setUTCDate(e.getUTCDate()-(e.getUTCDay()+7-t)%7),e.setUTCHours(0,0,0,0)},(e,n)=>{e.setUTCDate(e.getUTCDate()+n*7)},(e,n)=>(n-e)/Ce)}const vn=Ct(0),ee=Ct(1),pr=Ct(2),vr=Ct(3),Ut=Ct(4),Tr=Ct(5),xr=Ct(6);vn.range;ee.range;pr.range;vr.range;Ut.range;Tr.range;xr.range;const Nt=et(t=>{t.setDate(1),t.setHours(0,0,0,0)},(t,e)=>{t.setMonth(t.getMonth()+e)},(t,e)=>e.getMonth()-t.getMonth()+(e.getFullYear()-t.getFullYear())*12,t=>t.getMonth());Nt.range;const br=et(t=>{t.setUTCDate(1),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCMonth(t.getUTCMonth()+e)},(t,e)=>e.getUTCMonth()-t.getUTCMonth()+(e.getUTCFullYear()-t.getUTCFullYear())*12,t=>t.getUTCMonth());br.range;const kt=et(t=>{t.setMonth(0,1),t.setHours(0,0,0,0)},(t,e)=>{t.setFullYear(t.getFullYear()+e)},(t,e)=>e.getFullYear()-t.getFullYear(),t=>t.getFullYear());kt.every=t=>!isFinite(t=Math.floor(t))||!(t>0)?null:et(e=>{e.setFullYear(Math.floor(e.getFullYear()/t)*t),e.setMonth(0,1),e.setHours(0,0,0,0)},(e,n)=>{e.setFullYear(e.getFullYear()+n*t)});kt.range;const bt=et(t=>{t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCFullYear(t.getUTCFullYear()+e)},(t,e)=>e.getUTCFullYear()-t.getUTCFullYear(),t=>t.getUTCFullYear());bt.every=t=>!isFinite(t=Math.floor(t))||!(t>0)?null:et(e=>{e.setUTCFullYear(Math.floor(e.getUTCFullYear()/t)*t),e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,n)=>{e.setUTCFullYear(e.getUTCFullYear()+n*t)});bt.range;function wr(t,e,n,r,a,i){const s=[[vt,1,mt],[vt,5,5*mt],[vt,15,15*mt],[vt,30,30*mt],[i,1,ct],[i,5,5*ct],[i,15,15*ct],[i,30,30*ct],[a,1,gt],[a,3,3*gt],[a,6,6*gt],[a,12,12*gt],[r,1,yt],[r,2,2*yt],[n,1,Ce],[e,1,Ne],[e,3,3*Ne],[t,1,me]];function k(T,g,U){const C=g<T;C&&([T,g]=[g,T]);const x=U&&typeof U.range=="function"?U:M(T,g,U),X=x?x.range(T,+g+1):[];return C?X.reverse():X}function M(T,g,U){const C=Math.abs(g-T)/U,x=Zn(([,,D])=>D).right(s,C);if(x===s.length)return t.every(Oe(T/me,g/me,U));if(x===0)return Yt.every(Math.max(Oe(T,g,U),1));const[X,O]=s[C/s[x-1][2]<s[x][2]/C?x-1:x];return X.every(O)}return[k,M]}const[Cr,Dr]=wr(kt,Nt,Vt,Tt,Ot,Wt);function ge(t){if(0<=t.y&&t.y<100){var e=new Date(-1,t.m,t.d,t.H,t.M,t.S,t.L);return e.setFullYear(t.y),e}return new Date(t.y,t.m,t.d,t.H,t.M,t.S,t.L)}function ye(t){if(0<=t.y&&t.y<100){var e=new Date(Date.UTC(-1,t.m,t.d,t.H,t.M,t.S,t.L));return e.setUTCFullYear(t.y),e}return new Date(Date.UTC(t.y,t.m,t.d,t.H,t.M,t.S,t.L))}function At(t,e,n){return{y:t,m:e,d:n,H:0,M:0,S:0,L:0}}function Mr(t){var e=t.dateTime,n=t.date,r=t.time,a=t.periods,i=t.days,s=t.shortDays,k=t.months,M=t.shortMonths,T=Lt(a),g=It(a),U=Lt(i),C=It(i),x=Lt(s),X=It(s),O=Lt(k),D=It(k),I=Lt(M),V=It(M),W={a:m,A:E,b:c,B:d,c:null,d:Be,e:Be,f:Qr,g:ia,G:oa,H:Zr,I:Xr,j:Gr,L:Tn,m:jr,M:$r,p:o,q:z,Q:Ge,s:Qe,S:Jr,u:Kr,U:ta,V:ea,w:na,W:ra,x:null,X:null,y:aa,Y:sa,Z:ca,"%":Xe},B={a:P,A:R,b:K,B:G,c:null,d:Ze,e:Ze,f:ha,g:ba,G:Ca,H:ua,I:la,j:fa,L:bn,m:da,M:ma,p:$,q:it,Q:Ge,s:Qe,S:ga,u:ya,U:ka,V:pa,w:va,W:Ta,x:null,X:null,y:xa,Y:wa,Z:Da,"%":Xe},j={a:_,A:S,b:p,B:Y,c:u,d:Re,e:Re,f:zr,g:ze,G:Pe,H:qe,I:qe,j:Hr,L:Pr,m:Or,M:Nr,p:F,q:Wr,Q:qr,s:Br,S:Vr,u:Ur,U:Er,V:Ar,w:Yr,W:Lr,x:f,X:y,y:ze,Y:Pe,Z:Ir,"%":Rr};W.x=w(n,W),W.X=w(r,W),W.c=w(e,W),B.x=w(n,B),B.X=w(r,B),B.c=w(e,B);function w(v,L){return function(N){var l=[],J=-1,A=0,Q=v.length,Z,rt,st;for(N instanceof Date||(N=new Date(+N));++J<Q;)v.charCodeAt(J)===37&&(l.push(v.slice(A,J)),(rt=Ve[Z=v.charAt(++J)])!=null?Z=v.charAt(++J):rt=Z==="e"?" ":"0",(st=L[Z])&&(Z=st(N,rt)),l.push(Z),A=J+1);return l.push(v.slice(A,J)),l.join("")}}function H(v,L){return function(N){var l=At(1900,void 0,1),J=b(l,v,N+="",0),A,Q;if(J!=N.length)return null;if("Q"in l)return new Date(l.Q);if("s"in l)return new Date(l.s*1e3+("L"in l?l.L:0));if(L&&!("Z"in l)&&(l.Z=0),"p"in l&&(l.H=l.H%12+l.p*12),l.m===void 0&&(l.m="q"in l?l.q:0),"V"in l){if(l.V<1||l.V>53)return null;"w"in l||(l.w=1),"Z"in l?(A=ye(At(l.y,0,1)),Q=A.getUTCDay(),A=Q>4||Q===0?ee.ceil(A):ee(A),A=De.offset(A,(l.V-1)*7),l.y=A.getUTCFullYear(),l.m=A.getUTCMonth(),l.d=A.getUTCDate()+(l.w+6)%7):(A=ge(At(l.y,0,1)),Q=A.getDay(),A=Q>4||Q===0?Ht.ceil(A):Ht(A),A=Tt.offset(A,(l.V-1)*7),l.y=A.getFullYear(),l.m=A.getMonth(),l.d=A.getDate()+(l.w+6)%7)}else("W"in l||"U"in l)&&("w"in l||(l.w="u"in l?l.u%7:"W"in l?1:0),Q="Z"in l?ye(At(l.y,0,1)).getUTCDay():ge(At(l.y,0,1)).getDay(),l.m=0,l.d="W"in l?(l.w+6)%7+l.W*7-(Q+5)%7:l.w+l.U*7-(Q+6)%7);return"Z"in l?(l.H+=l.Z/100|0,l.M+=l.Z%100,ye(l)):ge(l)}}function b(v,L,N,l){for(var J=0,A=L.length,Q=N.length,Z,rt;J<A;){if(l>=Q)return-1;if(Z=L.charCodeAt(J++),Z===37){if(Z=L.charAt(J++),rt=j[Z in Ve?L.charAt(J++):Z],!rt||(l=rt(v,N,l))<0)return-1}else if(Z!=N.charCodeAt(l++))return-1}return l}function F(v,L,N){var l=T.exec(L.slice(N));return l?(v.p=g.get(l[0].toLowerCase()),N+l[0].length):-1}function _(v,L,N){var l=x.exec(L.slice(N));return l?(v.w=X.get(l[0].toLowerCase()),N+l[0].length):-1}function S(v,L,N){var l=U.exec(L.slice(N));return l?(v.w=C.get(l[0].toLowerCase()),N+l[0].length):-1}function p(v,L,N){var l=I.exec(L.slice(N));return l?(v.m=V.get(l[0].toLowerCase()),N+l[0].length):-1}function Y(v,L,N){var l=O.exec(L.slice(N));return l?(v.m=D.get(l[0].toLowerCase()),N+l[0].length):-1}function u(v,L,N){return b(v,e,L,N)}function f(v,L,N){return b(v,n,L,N)}function y(v,L,N){return b(v,r,L,N)}function m(v){return s[v.getDay()]}function E(v){return i[v.getDay()]}function c(v){return M[v.getMonth()]}function d(v){return k[v.getMonth()]}function o(v){return a[+(v.getHours()>=12)]}function z(v){return 1+~~(v.getMonth()/3)}function P(v){return s[v.getUTCDay()]}function R(v){return i[v.getUTCDay()]}function K(v){return M[v.getUTCMonth()]}function G(v){return k[v.getUTCMonth()]}function $(v){return a[+(v.getUTCHours()>=12)]}function it(v){return 1+~~(v.getUTCMonth()/3)}return{format:function(v){var L=w(v+="",W);return L.toString=function(){return v},L},parse:function(v){var L=H(v+="",!1);return L.toString=function(){return v},L},utcFormat:function(v){var L=w(v+="",B);return L.toString=function(){return v},L},utcParse:function(v){var L=H(v+="",!0);return L.toString=function(){return v},L}}}var Ve={"-":"",_:" ",0:"0"},nt=/^\s*\d+/,_r=/^%/,Sr=/[\\^$*+?|[\]().{}]/g;function q(t,e,n){var r=t<0?"-":"",a=(r?-t:t)+"",i=a.length;return r+(i<n?new Array(n-i+1).join(e)+a:a)}function Fr(t){return t.replace(Sr,"\\$&")}function Lt(t){return new RegExp("^(?:"+t.map(Fr).join("|")+")","i")}function It(t){return new Map(t.map((e,n)=>[e.toLowerCase(),n]))}function Yr(t,e,n){var r=nt.exec(e.slice(n,n+1));return r?(t.w=+r[0],n+r[0].length):-1}function Ur(t,e,n){var r=nt.exec(e.slice(n,n+1));return r?(t.u=+r[0],n+r[0].length):-1}function Er(t,e,n){var r=nt.exec(e.slice(n,n+2));return r?(t.U=+r[0],n+r[0].length):-1}function Ar(t,e,n){var r=nt.exec(e.slice(n,n+2));return r?(t.V=+r[0],n+r[0].length):-1}function Lr(t,e,n){var r=nt.exec(e.slice(n,n+2));return r?(t.W=+r[0],n+r[0].length):-1}function Pe(t,e,n){var r=nt.exec(e.slice(n,n+4));return r?(t.y=+r[0],n+r[0].length):-1}function ze(t,e,n){var r=nt.exec(e.slice(n,n+2));return r?(t.y=+r[0]+(+r[0]>68?1900:2e3),n+r[0].length):-1}function Ir(t,e,n){var r=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(e.slice(n,n+6));return r?(t.Z=r[1]?0:-(r[2]+(r[3]||"00")),n+r[0].length):-1}function Wr(t,e,n){var r=nt.exec(e.slice(n,n+1));return r?(t.q=r[0]*3-3,n+r[0].length):-1}function Or(t,e,n){var r=nt.exec(e.slice(n,n+2));return r?(t.m=r[0]-1,n+r[0].length):-1}function Re(t,e,n){var r=nt.exec(e.slice(n,n+2));return r?(t.d=+r[0],n+r[0].length):-1}function Hr(t,e,n){var r=nt.exec(e.slice(n,n+3));return r?(t.m=0,t.d=+r[0],n+r[0].length):-1}function qe(t,e,n){var r=nt.exec(e.slice(n,n+2));return r?(t.H=+r[0],n+r[0].length):-1}function Nr(t,e,n){var r=nt.exec(e.slice(n,n+2));return r?(t.M=+r[0],n+r[0].length):-1}function Vr(t,e,n){var r=nt.exec(e.slice(n,n+2));return r?(t.S=+r[0],n+r[0].length):-1}function Pr(t,e,n){var r=nt.exec(e.slice(n,n+3));return r?(t.L=+r[0],n+r[0].length):-1}function zr(t,e,n){var r=nt.exec(e.slice(n,n+6));return r?(t.L=Math.floor(r[0]/1e3),n+r[0].length):-1}function Rr(t,e,n){var r=_r.exec(e.slice(n,n+1));return r?n+r[0].length:-1}function qr(t,e,n){var r=nt.exec(e.slice(n));return r?(t.Q=+r[0],n+r[0].length):-1}function Br(t,e,n){var r=nt.exec(e.slice(n));return r?(t.s=+r[0],n+r[0].length):-1}function Be(t,e){return q(t.getDate(),e,2)}function Zr(t,e){return q(t.getHours(),e,2)}function Xr(t,e){return q(t.getHours()%12||12,e,2)}function Gr(t,e){return q(1+Tt.count(kt(t),t),e,3)}function Tn(t,e){return q(t.getMilliseconds(),e,3)}function Qr(t,e){return Tn(t,e)+"000"}function jr(t,e){return q(t.getMonth()+1,e,2)}function $r(t,e){return q(t.getMinutes(),e,2)}function Jr(t,e){return q(t.getSeconds(),e,2)}function Kr(t){var e=t.getDay();return e===0?7:e}function ta(t,e){return q(Vt.count(kt(t)-1,t),e,2)}function xn(t){var e=t.getDay();return e>=4||e===0?xt(t):xt.ceil(t)}function ea(t,e){return t=xn(t),q(xt.count(kt(t),t)+(kt(t).getDay()===4),e,2)}function na(t){return t.getDay()}function ra(t,e){return q(Ht.count(kt(t)-1,t),e,2)}function aa(t,e){return q(t.getFullYear()%100,e,2)}function ia(t,e){return t=xn(t),q(t.getFullYear()%100,e,2)}function sa(t,e){return q(t.getFullYear()%1e4,e,4)}function oa(t,e){var n=t.getDay();return t=n>=4||n===0?xt(t):xt.ceil(t),q(t.getFullYear()%1e4,e,4)}function ca(t){var e=t.getTimezoneOffset();return(e>0?"-":(e*=-1,"+"))+q(e/60|0,"0",2)+q(e%60,"0",2)}function Ze(t,e){return q(t.getUTCDate(),e,2)}function ua(t,e){return q(t.getUTCHours(),e,2)}function la(t,e){return q(t.getUTCHours()%12||12,e,2)}function fa(t,e){return q(1+De.count(bt(t),t),e,3)}function bn(t,e){return q(t.getUTCMilliseconds(),e,3)}function ha(t,e){return bn(t,e)+"000"}function da(t,e){return q(t.getUTCMonth()+1,e,2)}function ma(t,e){return q(t.getUTCMinutes(),e,2)}function ga(t,e){return q(t.getUTCSeconds(),e,2)}function ya(t){var e=t.getUTCDay();return e===0?7:e}function ka(t,e){return q(vn.count(bt(t)-1,t),e,2)}function wn(t){var e=t.getUTCDay();return e>=4||e===0?Ut(t):Ut.ceil(t)}function pa(t,e){return t=wn(t),q(Ut.count(bt(t),t)+(bt(t).getUTCDay()===4),e,2)}function va(t){return t.getUTCDay()}function Ta(t,e){return q(ee.count(bt(t)-1,t),e,2)}function xa(t,e){return q(t.getUTCFullYear()%100,e,2)}function ba(t,e){return t=wn(t),q(t.getUTCFullYear()%100,e,2)}function wa(t,e){return q(t.getUTCFullYear()%1e4,e,4)}function Ca(t,e){var n=t.getUTCDay();return t=n>=4||n===0?Ut(t):Ut.ceil(t),q(t.getUTCFullYear()%1e4,e,4)}function Da(){return"+0000"}function Xe(){return"%"}function Ge(t){return+t}function Qe(t){return Math.floor(+t/1e3)}var Mt,ne;Ma({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]});function Ma(t){return Mt=Mr(t),ne=Mt.format,Mt.parse,Mt.utcFormat,Mt.utcParse,Mt}function _a(t){return new Date(t)}function Sa(t){return t instanceof Date?+t:+new Date(+t)}function Cn(t,e,n,r,a,i,s,k,M,T){var g=Xn(),U=g.invert,C=g.domain,x=T(".%L"),X=T(":%S"),O=T("%I:%M"),D=T("%I %p"),I=T("%a %d"),V=T("%b %d"),W=T("%B"),B=T("%Y");function j(w){return(M(w)<w?x:k(w)<w?X:s(w)<w?O:i(w)<w?D:r(w)<w?a(w)<w?I:V:n(w)<w?W:B)(w)}return g.invert=function(w){return new Date(U(w))},g.domain=function(w){return arguments.length?C(Array.from(w,Sa)):C().map(_a)},g.ticks=function(w){var H=C();return t(H[0],H[H.length-1],w??10)},g.tickFormat=function(w,H){return H==null?j:T(H)},g.nice=function(w){var H=C();return(!w||typeof w.range!="function")&&(w=e(H[0],H[H.length-1],w??10)),w?C(mr(H,w)):g},g.copy=function(){return Gn(g,Cn(t,e,n,r,a,i,s,k,M,T))},g}function Fa(){return jn.apply(Cn(Cr,Dr,kt,Nt,Vt,Tt,Ot,Wt,vt,ne).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}var Gt={exports:{}},Ya=Gt.exports,je;function Ua(){return je||(je=1,function(t,e){(function(n,r){t.exports=r()})(Ya,function(){var n="day";return function(r,a,i){var s=function(T){return T.add(4-T.isoWeekday(),n)},k=a.prototype;k.isoWeekYear=function(){return s(this).year()},k.isoWeek=function(T){if(!this.$utils().u(T))return this.add(7*(T-this.isoWeek()),n);var g,U,C,x,X=s(this),O=(g=this.isoWeekYear(),U=this.$u,C=(U?i.utc:i)().year(g).startOf("year"),x=4-C.isoWeekday(),C.isoWeekday()>4&&(x+=7),C.add(x,n));return X.diff(O,"week")+1},k.isoWeekday=function(T){return this.$utils().u(T)?this.day()||7:this.day(this.day()%7?T:T-7)};var M=k.startOf;k.startOf=function(T,g){var U=this.$utils(),C=!!U.u(g)||g;return U.p(T)==="isoweek"?C?this.date(this.date()-(this.isoWeekday()-1)).startOf("day"):this.date(this.date()-1-(this.isoWeekday()-1)+7).endOf("day"):M.bind(this)(T,g)}}})}(Gt)),Gt.exports}var Ea=Ua();const Aa=we(Ea);var Qt={exports:{}},La=Qt.exports,$e;function Ia(){return $e||($e=1,function(t,e){(function(n,r){t.exports=r()})(La,function(){var n={LTS:"h:mm:ss A",LT:"h:mm A",L:"MM/DD/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY h:mm A",LLLL:"dddd, MMMM D, YYYY h:mm A"},r=/(\[[^[]*\])|([-_:/.,()\s]+)|(A|a|Q|YYYY|YY?|ww?|MM?M?M?|Do|DD?|hh?|HH?|mm?|ss?|S{1,3}|z|ZZ?)/g,a=/\d/,i=/\d\d/,s=/\d\d?/,k=/\d*[^-_:/,()\s\d]+/,M={},T=function(D){return(D=+D)+(D>68?1900:2e3)},g=function(D){return function(I){this[D]=+I}},U=[/[+-]\d\d:?(\d\d)?|Z/,function(D){(this.zone||(this.zone={})).offset=function(I){if(!I||I==="Z")return 0;var V=I.match(/([+-]|\d\d)/g),W=60*V[1]+(+V[2]||0);return W===0?0:V[0]==="+"?-W:W}(D)}],C=function(D){var I=M[D];return I&&(I.indexOf?I:I.s.concat(I.f))},x=function(D,I){var V,W=M.meridiem;if(W){for(var B=1;B<=24;B+=1)if(D.indexOf(W(B,0,I))>-1){V=B>12;break}}else V=D===(I?"pm":"PM");return V},X={A:[k,function(D){this.afternoon=x(D,!1)}],a:[k,function(D){this.afternoon=x(D,!0)}],Q:[a,function(D){this.month=3*(D-1)+1}],S:[a,function(D){this.milliseconds=100*+D}],SS:[i,function(D){this.milliseconds=10*+D}],SSS:[/\d{3}/,function(D){this.milliseconds=+D}],s:[s,g("seconds")],ss:[s,g("seconds")],m:[s,g("minutes")],mm:[s,g("minutes")],H:[s,g("hours")],h:[s,g("hours")],HH:[s,g("hours")],hh:[s,g("hours")],D:[s,g("day")],DD:[i,g("day")],Do:[k,function(D){var I=M.ordinal,V=D.match(/\d+/);if(this.day=V[0],I)for(var W=1;W<=31;W+=1)I(W).replace(/\[|\]/g,"")===D&&(this.day=W)}],w:[s,g("week")],ww:[i,g("week")],M:[s,g("month")],MM:[i,g("month")],MMM:[k,function(D){var I=C("months"),V=(C("monthsShort")||I.map(function(W){return W.slice(0,3)})).indexOf(D)+1;if(V<1)throw new Error;this.month=V%12||V}],MMMM:[k,function(D){var I=C("months").indexOf(D)+1;if(I<1)throw new Error;this.month=I%12||I}],Y:[/[+-]?\d+/,g("year")],YY:[i,function(D){this.year=T(D)}],YYYY:[/\d{4}/,g("year")],Z:U,ZZ:U};function O(D){var I,V;I=D,V=M&&M.formats;for(var W=(D=I.replace(/(\[[^\]]+])|(LTS?|l{1,4}|L{1,4})/g,function(_,S,p){var Y=p&&p.toUpperCase();return S||V[p]||n[p]||V[Y].replace(/(\[[^\]]+])|(MMMM|MM|DD|dddd)/g,function(u,f,y){return f||y.slice(1)})})).match(r),B=W.length,j=0;j<B;j+=1){var w=W[j],H=X[w],b=H&&H[0],F=H&&H[1];W[j]=F?{regex:b,parser:F}:w.replace(/^\[|\]$/g,"")}return function(_){for(var S={},p=0,Y=0;p<B;p+=1){var u=W[p];if(typeof u=="string")Y+=u.length;else{var f=u.regex,y=u.parser,m=_.slice(Y),E=f.exec(m)[0];y.call(S,E),_=_.replace(E,"")}}return function(c){var d=c.afternoon;if(d!==void 0){var o=c.hours;d?o<12&&(c.hours+=12):o===12&&(c.hours=0),delete c.afternoon}}(S),S}}return function(D,I,V){V.p.customParseFormat=!0,D&&D.parseTwoDigitYear&&(T=D.parseTwoDigitYear);var W=I.prototype,B=W.parse;W.parse=function(j){var w=j.date,H=j.utc,b=j.args;this.$u=H;var F=b[1];if(typeof F=="string"){var _=b[2]===!0,S=b[3]===!0,p=_||S,Y=b[2];S&&(Y=b[2]),M=this.$locale(),!_&&Y&&(M=V.Ls[Y]),this.$d=function(m,E,c,d){try{if(["x","X"].indexOf(E)>-1)return new Date((E==="X"?1e3:1)*m);var o=O(E)(m),z=o.year,P=o.month,R=o.day,K=o.hours,G=o.minutes,$=o.seconds,it=o.milliseconds,v=o.zone,L=o.week,N=new Date,l=R||(z||P?1:N.getDate()),J=z||N.getFullYear(),A=0;z&&!P||(A=P>0?P-1:N.getMonth());var Q,Z=K||0,rt=G||0,st=$||0,pt=it||0;return v?new Date(Date.UTC(J,A,l,Z,rt,st,pt+60*v.offset*1e3)):c?new Date(Date.UTC(J,A,l,Z,rt,st,pt)):(Q=new Date(J,A,l,Z,rt,st,pt),L&&(Q=d(Q).week(L).toDate()),Q)}catch{return new Date("")}}(w,F,H,V),this.init(),Y&&Y!==!0&&(this.$L=this.locale(Y).$L),p&&w!=this.format(F)&&(this.$d=new Date("")),M={}}else if(F instanceof Array)for(var u=F.length,f=1;f<=u;f+=1){b[1]=F[f-1];var y=V.apply(this,b);if(y.isValid()){this.$d=y.$d,this.$L=y.$L,this.init();break}f===u&&(this.$d=new Date(""))}else B.call(this,j)}}})}(Qt)),Qt.exports}var Wa=Ia();const Oa=we(Wa);var jt={exports:{}},Ha=jt.exports,Je;function Na(){return Je||(Je=1,function(t,e){(function(n,r){t.exports=r()})(Ha,function(){return function(n,r){var a=r.prototype,i=a.format;a.format=function(s){var k=this,M=this.$locale();if(!this.isValid())return i.bind(this)(s);var T=this.$utils(),g=(s||"YYYY-MM-DDTHH:mm:ssZ").replace(/\[([^\]]+)]|Q|wo|ww|w|WW|W|zzz|z|gggg|GGGG|Do|X|x|k{1,2}|S/g,function(U){switch(U){case"Q":return Math.ceil((k.$M+1)/3);case"Do":return M.ordinal(k.$D);case"gggg":return k.weekYear();case"GGGG":return k.isoWeekYear();case"wo":return M.ordinal(k.week(),"W");case"w":case"ww":return T.s(k.week(),U==="w"?1:2,"0");case"W":case"WW":return T.s(k.isoWeek(),U==="W"?1:2,"0");case"k":case"kk":return T.s(String(k.$H===0?24:k.$H),U==="k"?1:2,"0");case"X":return Math.floor(k.$d.getTime()/1e3);case"x":return k.$d.getTime();case"z":return"["+k.offsetName()+"]";case"zzz":return"["+k.offsetName("long")+"]";default:return U}});return i.bind(this)(g)}}})}(jt)),jt.exports}var Va=Na();const Pa=we(Va);var ve=function(){var t=h(function(Y,u,f,y){for(f=f||{},y=Y.length;y--;f[Y[y]]=u);return f},"o"),e=[6,8,10,12,13,14,15,16,17,18,20,21,22,23,24,25,26,27,28,29,30,31,33,35,36,38,40],n=[1,26],r=[1,27],a=[1,28],i=[1,29],s=[1,30],k=[1,31],M=[1,32],T=[1,33],g=[1,34],U=[1,9],C=[1,10],x=[1,11],X=[1,12],O=[1,13],D=[1,14],I=[1,15],V=[1,16],W=[1,19],B=[1,20],j=[1,21],w=[1,22],H=[1,23],b=[1,25],F=[1,35],_={trace:h(function(){},"trace"),yy:{},symbols_:{error:2,start:3,gantt:4,document:5,EOF:6,line:7,SPACE:8,statement:9,NL:10,weekday:11,weekday_monday:12,weekday_tuesday:13,weekday_wednesday:14,weekday_thursday:15,weekday_friday:16,weekday_saturday:17,weekday_sunday:18,weekend:19,weekend_friday:20,weekend_saturday:21,dateFormat:22,inclusiveEndDates:23,topAxis:24,axisFormat:25,tickInterval:26,excludes:27,includes:28,todayMarker:29,title:30,acc_title:31,acc_title_value:32,acc_descr:33,acc_descr_value:34,acc_descr_multiline_value:35,section:36,clickStatement:37,taskTxt:38,taskData:39,click:40,callbackname:41,callbackargs:42,href:43,clickStatementDebug:44,$accept:0,$end:1},terminals_:{2:"error",4:"gantt",6:"EOF",8:"SPACE",10:"NL",12:"weekday_monday",13:"weekday_tuesday",14:"weekday_wednesday",15:"weekday_thursday",16:"weekday_friday",17:"weekday_saturday",18:"weekday_sunday",20:"weekend_friday",21:"weekend_saturday",22:"dateFormat",23:"inclusiveEndDates",24:"topAxis",25:"axisFormat",26:"tickInterval",27:"excludes",28:"includes",29:"todayMarker",30:"title",31:"acc_title",32:"acc_title_value",33:"acc_descr",34:"acc_descr_value",35:"acc_descr_multiline_value",36:"section",38:"taskTxt",39:"taskData",40:"click",41:"callbackname",42:"callbackargs",43:"href"},productions_:[0,[3,3],[5,0],[5,2],[7,2],[7,1],[7,1],[7,1],[11,1],[11,1],[11,1],[11,1],[11,1],[11,1],[11,1],[19,1],[19,1],[9,1],[9,1],[9,1],[9,1],[9,1],[9,1],[9,1],[9,1],[9,1],[9,1],[9,1],[9,2],[9,2],[9,1],[9,1],[9,1],[9,2],[37,2],[37,3],[37,3],[37,4],[37,3],[37,4],[37,2],[44,2],[44,3],[44,3],[44,4],[44,3],[44,4],[44,2]],performAction:h(function(u,f,y,m,E,c,d){var o=c.length-1;switch(E){case 1:return c[o-1];case 2:this.$=[];break;case 3:c[o-1].push(c[o]),this.$=c[o-1];break;case 4:case 5:this.$=c[o];break;case 6:case 7:this.$=[];break;case 8:m.setWeekday("monday");break;case 9:m.setWeekday("tuesday");break;case 10:m.setWeekday("wednesday");break;case 11:m.setWeekday("thursday");break;case 12:m.setWeekday("friday");break;case 13:m.setWeekday("saturday");break;case 14:m.setWeekday("sunday");break;case 15:m.setWeekend("friday");break;case 16:m.setWeekend("saturday");break;case 17:m.setDateFormat(c[o].substr(11)),this.$=c[o].substr(11);break;case 18:m.enableInclusiveEndDates(),this.$=c[o].substr(18);break;case 19:m.TopAxis(),this.$=c[o].substr(8);break;case 20:m.setAxisFormat(c[o].substr(11)),this.$=c[o].substr(11);break;case 21:m.setTickInterval(c[o].substr(13)),this.$=c[o].substr(13);break;case 22:m.setExcludes(c[o].substr(9)),this.$=c[o].substr(9);break;case 23:m.setIncludes(c[o].substr(9)),this.$=c[o].substr(9);break;case 24:m.setTodayMarker(c[o].substr(12)),this.$=c[o].substr(12);break;case 27:m.setDiagramTitle(c[o].substr(6)),this.$=c[o].substr(6);break;case 28:this.$=c[o].trim(),m.setAccTitle(this.$);break;case 29:case 30:this.$=c[o].trim(),m.setAccDescription(this.$);break;case 31:m.addSection(c[o].substr(8)),this.$=c[o].substr(8);break;case 33:m.addTask(c[o-1],c[o]),this.$="task";break;case 34:this.$=c[o-1],m.setClickEvent(c[o-1],c[o],null);break;case 35:this.$=c[o-2],m.setClickEvent(c[o-2],c[o-1],c[o]);break;case 36:this.$=c[o-2],m.setClickEvent(c[o-2],c[o-1],null),m.setLink(c[o-2],c[o]);break;case 37:this.$=c[o-3],m.setClickEvent(c[o-3],c[o-2],c[o-1]),m.setLink(c[o-3],c[o]);break;case 38:this.$=c[o-2],m.setClickEvent(c[o-2],c[o],null),m.setLink(c[o-2],c[o-1]);break;case 39:this.$=c[o-3],m.setClickEvent(c[o-3],c[o-1],c[o]),m.setLink(c[o-3],c[o-2]);break;case 40:this.$=c[o-1],m.setLink(c[o-1],c[o]);break;case 41:case 47:this.$=c[o-1]+" "+c[o];break;case 42:case 43:case 45:this.$=c[o-2]+" "+c[o-1]+" "+c[o];break;case 44:case 46:this.$=c[o-3]+" "+c[o-2]+" "+c[o-1]+" "+c[o];break}},"anonymous"),table:[{3:1,4:[1,2]},{1:[3]},t(e,[2,2],{5:3}),{6:[1,4],7:5,8:[1,6],9:7,10:[1,8],11:17,12:n,13:r,14:a,15:i,16:s,17:k,18:M,19:18,20:T,21:g,22:U,23:C,24:x,25:X,26:O,27:D,28:I,29:V,30:W,31:B,33:j,35:w,36:H,37:24,38:b,40:F},t(e,[2,7],{1:[2,1]}),t(e,[2,3]),{9:36,11:17,12:n,13:r,14:a,15:i,16:s,17:k,18:M,19:18,20:T,21:g,22:U,23:C,24:x,25:X,26:O,27:D,28:I,29:V,30:W,31:B,33:j,35:w,36:H,37:24,38:b,40:F},t(e,[2,5]),t(e,[2,6]),t(e,[2,17]),t(e,[2,18]),t(e,[2,19]),t(e,[2,20]),t(e,[2,21]),t(e,[2,22]),t(e,[2,23]),t(e,[2,24]),t(e,[2,25]),t(e,[2,26]),t(e,[2,27]),{32:[1,37]},{34:[1,38]},t(e,[2,30]),t(e,[2,31]),t(e,[2,32]),{39:[1,39]},t(e,[2,8]),t(e,[2,9]),t(e,[2,10]),t(e,[2,11]),t(e,[2,12]),t(e,[2,13]),t(e,[2,14]),t(e,[2,15]),t(e,[2,16]),{41:[1,40],43:[1,41]},t(e,[2,4]),t(e,[2,28]),t(e,[2,29]),t(e,[2,33]),t(e,[2,34],{42:[1,42],43:[1,43]}),t(e,[2,40],{41:[1,44]}),t(e,[2,35],{43:[1,45]}),t(e,[2,36]),t(e,[2,38],{42:[1,46]}),t(e,[2,37]),t(e,[2,39])],defaultActions:{},parseError:h(function(u,f){if(f.recoverable)this.trace(u);else{var y=new Error(u);throw y.hash=f,y}},"parseError"),parse:h(function(u){var f=this,y=[0],m=[],E=[null],c=[],d=this.table,o="",z=0,P=0,R=2,K=1,G=c.slice.call(arguments,1),$=Object.create(this.lexer),it={yy:{}};for(var v in this.yy)Object.prototype.hasOwnProperty.call(this.yy,v)&&(it.yy[v]=this.yy[v]);$.setInput(u,it.yy),it.yy.lexer=$,it.yy.parser=this,typeof $.yylloc>"u"&&($.yylloc={});var L=$.yylloc;c.push(L);var N=$.options&&$.options.ranges;typeof it.yy.parseError=="function"?this.parseError=it.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError;function l(ot){y.length=y.length-2*ot,E.length=E.length-ot,c.length=c.length-ot}h(l,"popStack");function J(){var ot;return ot=m.pop()||$.lex()||K,typeof ot!="number"&&(ot instanceof Array&&(m=ot,ot=m.pop()),ot=f.symbols_[ot]||ot),ot}h(J,"lex");for(var A,Q,Z,rt,st={},pt,ut,We,qt;;){if(Q=y[y.length-1],this.defaultActions[Q]?Z=this.defaultActions[Q]:((A===null||typeof A>"u")&&(A=J()),Z=d[Q]&&d[Q][A]),typeof Z>"u"||!Z.length||!Z[0]){var ie="";qt=[];for(pt in d[Q])this.terminals_[pt]&&pt>R&&qt.push("'"+this.terminals_[pt]+"'");$.showPosition?ie="Parse error on line "+(z+1)+`:
`+$.showPosition()+`
Expecting `+qt.join(", ")+", got '"+(this.terminals_[A]||A)+"'":ie="Parse error on line "+(z+1)+": Unexpected "+(A==K?"end of input":"'"+(this.terminals_[A]||A)+"'"),this.parseError(ie,{text:$.match,token:this.terminals_[A]||A,line:$.yylineno,loc:L,expected:qt})}if(Z[0]instanceof Array&&Z.length>1)throw new Error("Parse Error: multiple actions possible at state: "+Q+", token: "+A);switch(Z[0]){case 1:y.push(A),E.push($.yytext),c.push($.yylloc),y.push(Z[1]),A=null,P=$.yyleng,o=$.yytext,z=$.yylineno,L=$.yylloc;break;case 2:if(ut=this.productions_[Z[1]][1],st.$=E[E.length-ut],st._$={first_line:c[c.length-(ut||1)].first_line,last_line:c[c.length-1].last_line,first_column:c[c.length-(ut||1)].first_column,last_column:c[c.length-1].last_column},N&&(st._$.range=[c[c.length-(ut||1)].range[0],c[c.length-1].range[1]]),rt=this.performAction.apply(st,[o,P,z,it.yy,Z[1],E,c].concat(G)),typeof rt<"u")return rt;ut&&(y=y.slice(0,-1*ut*2),E=E.slice(0,-1*ut),c=c.slice(0,-1*ut)),y.push(this.productions_[Z[1]][0]),E.push(st.$),c.push(st._$),We=d[y[y.length-2]][y[y.length-1]],y.push(We);break;case 3:return!0}}return!0},"parse")},S=function(){var Y={EOF:1,parseError:h(function(f,y){if(this.yy.parser)this.yy.parser.parseError(f,y);else throw new Error(f)},"parseError"),setInput:h(function(u,f){return this.yy=f||this.yy||{},this._input=u,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:h(function(){var u=this._input[0];this.yytext+=u,this.yyleng++,this.offset++,this.match+=u,this.matched+=u;var f=u.match(/(?:\r\n?|\n).*/g);return f?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),u},"input"),unput:h(function(u){var f=u.length,y=u.split(/(?:\r\n?|\n)/g);this._input=u+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-f),this.offset-=f;var m=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),y.length-1&&(this.yylineno-=y.length-1);var E=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:y?(y.length===m.length?this.yylloc.first_column:0)+m[m.length-y.length].length-y[0].length:this.yylloc.first_column-f},this.options.ranges&&(this.yylloc.range=[E[0],E[0]+this.yyleng-f]),this.yyleng=this.yytext.length,this},"unput"),more:h(function(){return this._more=!0,this},"more"),reject:h(function(){if(this.options.backtrack_lexer)this._backtrack=!0;else return this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno});return this},"reject"),less:h(function(u){this.unput(this.match.slice(u))},"less"),pastInput:h(function(){var u=this.matched.substr(0,this.matched.length-this.match.length);return(u.length>20?"...":"")+u.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:h(function(){var u=this.match;return u.length<20&&(u+=this._input.substr(0,20-u.length)),(u.substr(0,20)+(u.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:h(function(){var u=this.pastInput(),f=new Array(u.length+1).join("-");return u+this.upcomingInput()+`
`+f+"^"},"showPosition"),test_match:h(function(u,f){var y,m,E;if(this.options.backtrack_lexer&&(E={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(E.yylloc.range=this.yylloc.range.slice(0))),m=u[0].match(/(?:\r\n?|\n).*/g),m&&(this.yylineno+=m.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:m?m[m.length-1].length-m[m.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+u[0].length},this.yytext+=u[0],this.match+=u[0],this.matches=u,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(u[0].length),this.matched+=u[0],y=this.performAction.call(this,this.yy,this,f,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),y)return y;if(this._backtrack){for(var c in E)this[c]=E[c];return!1}return!1},"test_match"),next:h(function(){if(this.done)return this.EOF;this._input||(this.done=!0);var u,f,y,m;this._more||(this.yytext="",this.match="");for(var E=this._currentRules(),c=0;c<E.length;c++)if(y=this._input.match(this.rules[E[c]]),y&&(!f||y[0].length>f[0].length)){if(f=y,m=c,this.options.backtrack_lexer){if(u=this.test_match(y,E[c]),u!==!1)return u;if(this._backtrack){f=!1;continue}else return!1}else if(!this.options.flex)break}return f?(u=this.test_match(f,E[m]),u!==!1?u:!1):this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:h(function(){var f=this.next();return f||this.lex()},"lex"),begin:h(function(f){this.conditionStack.push(f)},"begin"),popState:h(function(){var f=this.conditionStack.length-1;return f>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:h(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:h(function(f){return f=this.conditionStack.length-1-Math.abs(f||0),f>=0?this.conditionStack[f]:"INITIAL"},"topState"),pushState:h(function(f){this.begin(f)},"pushState"),stateStackSize:h(function(){return this.conditionStack.length},"stateStackSize"),options:{"case-insensitive":!0},performAction:h(function(f,y,m,E){switch(m){case 0:return this.begin("open_directive"),"open_directive";case 1:return this.begin("acc_title"),31;case 2:return this.popState(),"acc_title_value";case 3:return this.begin("acc_descr"),33;case 4:return this.popState(),"acc_descr_value";case 5:this.begin("acc_descr_multiline");break;case 6:this.popState();break;case 7:return"acc_descr_multiline_value";case 8:break;case 9:break;case 10:break;case 11:return 10;case 12:break;case 13:break;case 14:this.begin("href");break;case 15:this.popState();break;case 16:return 43;case 17:this.begin("callbackname");break;case 18:this.popState();break;case 19:this.popState(),this.begin("callbackargs");break;case 20:return 41;case 21:this.popState();break;case 22:return 42;case 23:this.begin("click");break;case 24:this.popState();break;case 25:return 40;case 26:return 4;case 27:return 22;case 28:return 23;case 29:return 24;case 30:return 25;case 31:return 26;case 32:return 28;case 33:return 27;case 34:return 29;case 35:return 12;case 36:return 13;case 37:return 14;case 38:return 15;case 39:return 16;case 40:return 17;case 41:return 18;case 42:return 20;case 43:return 21;case 44:return"date";case 45:return 30;case 46:return"accDescription";case 47:return 36;case 48:return 38;case 49:return 39;case 50:return":";case 51:return 6;case 52:return"INVALID"}},"anonymous"),rules:[/^(?:%%\{)/i,/^(?:accTitle\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*\{\s*)/i,/^(?:[\}])/i,/^(?:[^\}]*)/i,/^(?:%%(?!\{)*[^\n]*)/i,/^(?:[^\}]%%*[^\n]*)/i,/^(?:%%*[^\n]*[\n]*)/i,/^(?:[\n]+)/i,/^(?:\s+)/i,/^(?:%[^\n]*)/i,/^(?:href[\s]+["])/i,/^(?:["])/i,/^(?:[^"]*)/i,/^(?:call[\s]+)/i,/^(?:\([\s]*\))/i,/^(?:\()/i,/^(?:[^(]*)/i,/^(?:\))/i,/^(?:[^)]*)/i,/^(?:click[\s]+)/i,/^(?:[\s\n])/i,/^(?:[^\s\n]*)/i,/^(?:gantt\b)/i,/^(?:dateFormat\s[^#\n;]+)/i,/^(?:inclusiveEndDates\b)/i,/^(?:topAxis\b)/i,/^(?:axisFormat\s[^#\n;]+)/i,/^(?:tickInterval\s[^#\n;]+)/i,/^(?:includes\s[^#\n;]+)/i,/^(?:excludes\s[^#\n;]+)/i,/^(?:todayMarker\s[^\n;]+)/i,/^(?:weekday\s+monday\b)/i,/^(?:weekday\s+tuesday\b)/i,/^(?:weekday\s+wednesday\b)/i,/^(?:weekday\s+thursday\b)/i,/^(?:weekday\s+friday\b)/i,/^(?:weekday\s+saturday\b)/i,/^(?:weekday\s+sunday\b)/i,/^(?:weekend\s+friday\b)/i,/^(?:weekend\s+saturday\b)/i,/^(?:\d\d\d\d-\d\d-\d\d\b)/i,/^(?:title\s[^\n]+)/i,/^(?:accDescription\s[^#\n;]+)/i,/^(?:section\s[^\n]+)/i,/^(?:[^:\n]+)/i,/^(?::[^#\n;]+)/i,/^(?::)/i,/^(?:$)/i,/^(?:.)/i],conditions:{acc_descr_multiline:{rules:[6,7],inclusive:!1},acc_descr:{rules:[4],inclusive:!1},acc_title:{rules:[2],inclusive:!1},callbackargs:{rules:[21,22],inclusive:!1},callbackname:{rules:[18,19,20],inclusive:!1},href:{rules:[15,16],inclusive:!1},click:{rules:[24,25],inclusive:!1},INITIAL:{rules:[0,1,3,5,8,9,10,11,12,13,14,17,23,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52],inclusive:!0}}};return Y}();_.lexer=S;function p(){this.yy={}}return h(p,"Parser"),p.prototype=_,_.Parser=p,new p}();ve.parser=ve;var za=ve;at.extend(Aa);at.extend(Oa);at.extend(Pa);var Ke={friday:5,saturday:6},lt="",Me="",_e=void 0,Se="",Pt=[],zt=[],Fe=new Map,Ye=[],re=[],Et="",Ue="",Dn=["active","done","crit","milestone"],Ee=[],Rt=!1,Ae=!1,Le="sunday",ae="saturday",Te=0,Ra=h(function(){Ye=[],re=[],Et="",Ee=[],$t=0,be=void 0,Jt=void 0,tt=[],lt="",Me="",Ue="",_e=void 0,Se="",Pt=[],zt=[],Rt=!1,Ae=!1,Te=0,Fe=new Map,qn(),Le="sunday",ae="saturday"},"clear"),qa=h(function(t){Me=t},"setAxisFormat"),Ba=h(function(){return Me},"getAxisFormat"),Za=h(function(t){_e=t},"setTickInterval"),Xa=h(function(){return _e},"getTickInterval"),Ga=h(function(t){Se=t},"setTodayMarker"),Qa=h(function(){return Se},"getTodayMarker"),ja=h(function(t){lt=t},"setDateFormat"),$a=h(function(){Rt=!0},"enableInclusiveEndDates"),Ja=h(function(){return Rt},"endDatesAreInclusive"),Ka=h(function(){Ae=!0},"enableTopAxis"),ti=h(function(){return Ae},"topAxisEnabled"),ei=h(function(t){Ue=t},"setDisplayMode"),ni=h(function(){return Ue},"getDisplayMode"),ri=h(function(){return lt},"getDateFormat"),ai=h(function(t){Pt=t.toLowerCase().split(/[\s,]+/)},"setIncludes"),ii=h(function(){return Pt},"getIncludes"),si=h(function(t){zt=t.toLowerCase().split(/[\s,]+/)},"setExcludes"),oi=h(function(){return zt},"getExcludes"),ci=h(function(){return Fe},"getLinks"),ui=h(function(t){Et=t,Ye.push(t)},"addSection"),li=h(function(){return Ye},"getSections"),fi=h(function(){let t=tn();const e=10;let n=0;for(;!t&&n<e;)t=tn(),n++;return re=tt,re},"getTasks"),Mn=h(function(t,e,n,r){return r.includes(t.format(e.trim()))?!1:n.includes("weekends")&&(t.isoWeekday()===Ke[ae]||t.isoWeekday()===Ke[ae]+1)||n.includes(t.format("dddd").toLowerCase())?!0:n.includes(t.format(e.trim()))},"isInvalidDate"),hi=h(function(t){Le=t},"setWeekday"),di=h(function(){return Le},"getWeekday"),mi=h(function(t){ae=t},"setWeekend"),_n=h(function(t,e,n,r){if(!n.length||t.manualEndTime)return;let a;t.startTime instanceof Date?a=at(t.startTime):a=at(t.startTime,e,!0),a=a.add(1,"d");let i;t.endTime instanceof Date?i=at(t.endTime):i=at(t.endTime,e,!0);const[s,k]=gi(a,i,e,n,r);t.endTime=s.toDate(),t.renderEndTime=k},"checkTaskDates"),gi=h(function(t,e,n,r,a){let i=!1,s=null;for(;t<=e;)i||(s=e.toDate()),i=Mn(t,n,r,a),i&&(e=e.add(1,"d")),t=t.add(1,"d");return[e,s]},"fixTaskDates"),xe=h(function(t,e,n){n=n.trim();const a=/^after\s+(?<ids>[\d\w- ]+)/.exec(n);if(a!==null){let s=null;for(const M of a.groups.ids.split(" ")){let T=Dt(M);T!==void 0&&(!s||T.endTime>s.endTime)&&(s=T)}if(s)return s.endTime;const k=new Date;return k.setHours(0,0,0,0),k}let i=at(n,e.trim(),!0);if(i.isValid())return i.toDate();{Kt.debug("Invalid date:"+n),Kt.debug("With date format:"+e.trim());const s=new Date(n);if(s===void 0||isNaN(s.getTime())||s.getFullYear()<-1e4||s.getFullYear()>1e4)throw new Error("Invalid date:"+n);return s}},"getStartDate"),Sn=h(function(t){const e=/^(\d+(?:\.\d+)?)([Mdhmswy]|ms)$/.exec(t.trim());return e!==null?[Number.parseFloat(e[1]),e[2]]:[NaN,"ms"]},"parseDuration"),Fn=h(function(t,e,n,r=!1){n=n.trim();const i=/^until\s+(?<ids>[\d\w- ]+)/.exec(n);if(i!==null){let g=null;for(const C of i.groups.ids.split(" ")){let x=Dt(C);x!==void 0&&(!g||x.startTime<g.startTime)&&(g=x)}if(g)return g.startTime;const U=new Date;return U.setHours(0,0,0,0),U}let s=at(n,e.trim(),!0);if(s.isValid())return r&&(s=s.add(1,"d")),s.toDate();let k=at(t);const[M,T]=Sn(n);if(!Number.isNaN(M)){const g=k.add(M,T);g.isValid()&&(k=g)}return k.toDate()},"getEndDate"),$t=0,Ft=h(function(t){return t===void 0?($t=$t+1,"task"+$t):t},"parseId"),yi=h(function(t,e){let n;e.substr(0,1)===":"?n=e.substr(1,e.length):n=e;const r=n.split(","),a={};Ie(r,a,Dn);for(let s=0;s<r.length;s++)r[s]=r[s].trim();let i="";switch(r.length){case 1:a.id=Ft(),a.startTime=t.endTime,i=r[0];break;case 2:a.id=Ft(),a.startTime=xe(void 0,lt,r[0]),i=r[1];break;case 3:a.id=Ft(r[0]),a.startTime=xe(void 0,lt,r[1]),i=r[2];break}return i&&(a.endTime=Fn(a.startTime,lt,i,Rt),a.manualEndTime=at(i,"YYYY-MM-DD",!0).isValid(),_n(a,lt,zt,Pt)),a},"compileData"),ki=h(function(t,e){let n;e.substr(0,1)===":"?n=e.substr(1,e.length):n=e;const r=n.split(","),a={};Ie(r,a,Dn);for(let i=0;i<r.length;i++)r[i]=r[i].trim();switch(r.length){case 1:a.id=Ft(),a.startTime={type:"prevTaskEnd",id:t},a.endTime={data:r[0]};break;case 2:a.id=Ft(),a.startTime={type:"getStartDate",startData:r[0]},a.endTime={data:r[1]};break;case 3:a.id=Ft(r[0]),a.startTime={type:"getStartDate",startData:r[1]},a.endTime={data:r[2]};break}return a},"parseData"),be,Jt,tt=[],Yn={},pi=h(function(t,e){const n={section:Et,type:Et,processed:!1,manualEndTime:!1,renderEndTime:null,raw:{data:e},task:t,classes:[]},r=ki(Jt,e);n.raw.startTime=r.startTime,n.raw.endTime=r.endTime,n.id=r.id,n.prevTaskId=Jt,n.active=r.active,n.done=r.done,n.crit=r.crit,n.milestone=r.milestone,n.order=Te,Te++;const a=tt.push(n);Jt=n.id,Yn[n.id]=a-1},"addTask"),Dt=h(function(t){const e=Yn[t];return tt[e]},"findTaskById"),vi=h(function(t,e){const n={section:Et,type:Et,description:t,task:t,classes:[]},r=yi(be,e);n.startTime=r.startTime,n.endTime=r.endTime,n.id=r.id,n.active=r.active,n.done=r.done,n.crit=r.crit,n.milestone=r.milestone,be=n,re.push(n)},"addTaskOrg"),tn=h(function(){const t=h(function(n){const r=tt[n];let a="";switch(tt[n].raw.startTime.type){case"prevTaskEnd":{const i=Dt(r.prevTaskId);r.startTime=i.endTime;break}case"getStartDate":a=xe(void 0,lt,tt[n].raw.startTime.startData),a&&(tt[n].startTime=a);break}return tt[n].startTime&&(tt[n].endTime=Fn(tt[n].startTime,lt,tt[n].raw.endTime.data,Rt),tt[n].endTime&&(tt[n].processed=!0,tt[n].manualEndTime=at(tt[n].raw.endTime.data,"YYYY-MM-DD",!0).isValid(),_n(tt[n],lt,zt,Pt))),tt[n].processed},"compileTask");let e=!0;for(const[n,r]of tt.entries())t(n),e=e&&r.processed;return e},"compileTasks"),Ti=h(function(t,e){let n=e;_t().securityLevel!=="loose"&&(n=Rn.sanitizeUrl(e)),t.split(",").forEach(function(r){Dt(r)!==void 0&&(En(r,()=>{window.open(n,"_self")}),Fe.set(r,n))}),Un(t,"clickable")},"setLink"),Un=h(function(t,e){t.split(",").forEach(function(n){let r=Dt(n);r!==void 0&&r.classes.push(e)})},"setClass"),xi=h(function(t,e,n){if(_t().securityLevel!=="loose"||e===void 0)return;let r=[];if(typeof n=="string"){r=n.split(/,(?=(?:(?:[^"]*"){2})*[^"]*$)/);for(let i=0;i<r.length;i++){let s=r[i].trim();s.startsWith('"')&&s.endsWith('"')&&(s=s.substr(1,s.length-2)),r[i]=s}}r.length===0&&r.push(t),Dt(t)!==void 0&&En(t,()=>{Bn.runFunc(e,...r)})},"setClickFun"),En=h(function(t,e){Ee.push(function(){const n=document.querySelector(`[id="${t}"]`);n!==null&&n.addEventListener("click",function(){e()})},function(){const n=document.querySelector(`[id="${t}-text"]`);n!==null&&n.addEventListener("click",function(){e()})})},"pushFun"),bi=h(function(t,e,n){t.split(",").forEach(function(r){xi(r,e,n)}),Un(t,"clickable")},"setClickEvent"),wi=h(function(t){Ee.forEach(function(e){e(t)})},"bindFunctions"),Ci={getConfig:h(()=>_t().gantt,"getConfig"),clear:Ra,setDateFormat:ja,getDateFormat:ri,enableInclusiveEndDates:$a,endDatesAreInclusive:Ja,enableTopAxis:Ka,topAxisEnabled:ti,setAxisFormat:qa,getAxisFormat:Ba,setTickInterval:Za,getTickInterval:Xa,setTodayMarker:Ga,getTodayMarker:Qa,setAccTitle:Vn,getAccTitle:Nn,setDiagramTitle:Hn,getDiagramTitle:On,setDisplayMode:ei,getDisplayMode:ni,setAccDescription:Wn,getAccDescription:In,addSection:ui,getSections:li,getTasks:fi,addTask:pi,findTaskById:Dt,addTaskOrg:vi,setIncludes:ai,getIncludes:ii,setExcludes:si,getExcludes:oi,setClickEvent:bi,setLink:Ti,getLinks:ci,bindFunctions:wi,parseDuration:Sn,isInvalidDate:Mn,setWeekday:hi,getWeekday:di,setWeekend:mi};function Ie(t,e,n){let r=!0;for(;r;)r=!1,n.forEach(function(a){const i="^\\s*"+a+"\\s*$",s=new RegExp(i);t[0].match(s)&&(e[a]=!0,t.shift(1),r=!0)})}h(Ie,"getTaskTags");var Di=h(function(){Kt.debug("Something is calling, setConf, remove the call")},"setConf"),en={monday:Ht,tuesday:gn,wednesday:yn,thursday:xt,friday:kn,saturday:pn,sunday:Vt},Mi=h((t,e)=>{let n=[...t].map(()=>-1/0),r=[...t].sort((i,s)=>i.startTime-s.startTime||i.order-s.order),a=0;for(const i of r)for(let s=0;s<n.length;s++)if(i.startTime>=n[s]){n[s]=i.endTime,i.order=s+e,s>a&&(a=s);break}return a},"getMaxIntersections"),ht,_i=h(function(t,e,n,r){const a=_t().gantt,i=_t().securityLevel;let s;i==="sandbox"&&(s=Bt("#i"+e));const k=i==="sandbox"?Bt(s.nodes()[0].contentDocument.body):Bt("body"),M=i==="sandbox"?s.nodes()[0].contentDocument:document,T=M.getElementById(e);ht=T.parentElement.offsetWidth,ht===void 0&&(ht=1200),a.useWidth!==void 0&&(ht=a.useWidth);const g=r.db.getTasks();let U=[];for(const b of g)U.push(b.type);U=H(U);const C={};let x=2*a.topPadding;if(r.db.getDisplayMode()==="compact"||a.displayMode==="compact"){const b={};for(const _ of g)b[_.section]===void 0?b[_.section]=[_]:b[_.section].push(_);let F=0;for(const _ of Object.keys(b)){const S=Mi(b[_],F)+1;F+=S,x+=S*(a.barHeight+a.barGap),C[_]=S}}else{x+=g.length*(a.barHeight+a.barGap);for(const b of U)C[b]=g.filter(F=>F.type===b).length}T.setAttribute("viewBox","0 0 "+ht+" "+x);const X=k.select(`[id="${e}"]`),O=Fa().domain([Jn(g,function(b){return b.startTime}),$n(g,function(b){return b.endTime})]).rangeRound([0,ht-a.leftPadding-a.rightPadding]);function D(b,F){const _=b.startTime,S=F.startTime;let p=0;return _>S?p=1:_<S&&(p=-1),p}h(D,"taskCompare"),g.sort(D),I(g,ht,x),Pn(X,x,ht,a.useMaxWidth),X.append("text").text(r.db.getDiagramTitle()).attr("x",ht/2).attr("y",a.titleTopMargin).attr("class","titleText");function I(b,F,_){const S=a.barHeight,p=S+a.barGap,Y=a.topPadding,u=a.leftPadding,f=Qn().domain([0,U.length]).range(["#00B9FA","#F95002"]).interpolate(dr);W(p,Y,u,F,_,b,r.db.getExcludes(),r.db.getIncludes()),B(u,Y,F,_),V(b,p,Y,u,S,f,F),j(p,Y),w(u,Y,F,_)}h(I,"makeGantt");function V(b,F,_,S,p,Y,u){const y=[...new Set(b.map(d=>d.order))].map(d=>b.find(o=>o.order===d));X.append("g").selectAll("rect").data(y).enter().append("rect").attr("x",0).attr("y",function(d,o){return o=d.order,o*F+_-2}).attr("width",function(){return u-a.rightPadding/2}).attr("height",F).attr("class",function(d){for(const[o,z]of U.entries())if(d.type===z)return"section section"+o%a.numberSectionStyles;return"section section0"});const m=X.append("g").selectAll("rect").data(b).enter(),E=r.db.getLinks();if(m.append("rect").attr("id",function(d){return d.id}).attr("rx",3).attr("ry",3).attr("x",function(d){return d.milestone?O(d.startTime)+S+.5*(O(d.endTime)-O(d.startTime))-.5*p:O(d.startTime)+S}).attr("y",function(d,o){return o=d.order,o*F+_}).attr("width",function(d){return d.milestone?p:O(d.renderEndTime||d.endTime)-O(d.startTime)}).attr("height",p).attr("transform-origin",function(d,o){return o=d.order,(O(d.startTime)+S+.5*(O(d.endTime)-O(d.startTime))).toString()+"px "+(o*F+_+.5*p).toString()+"px"}).attr("class",function(d){const o="task";let z="";d.classes.length>0&&(z=d.classes.join(" "));let P=0;for(const[K,G]of U.entries())d.type===G&&(P=K%a.numberSectionStyles);let R="";return d.active?d.crit?R+=" activeCrit":R=" active":d.done?d.crit?R=" doneCrit":R=" done":d.crit&&(R+=" crit"),R.length===0&&(R=" task"),d.milestone&&(R=" milestone "+R),R+=P,R+=" "+z,o+R}),m.append("text").attr("id",function(d){return d.id+"-text"}).text(function(d){return d.task}).attr("font-size",a.fontSize).attr("x",function(d){let o=O(d.startTime),z=O(d.renderEndTime||d.endTime);d.milestone&&(o+=.5*(O(d.endTime)-O(d.startTime))-.5*p),d.milestone&&(z=o+p);const P=this.getBBox().width;return P>z-o?z+P+1.5*a.leftPadding>u?o+S-5:z+S+5:(z-o)/2+o+S}).attr("y",function(d,o){return o=d.order,o*F+a.barHeight/2+(a.fontSize/2-2)+_}).attr("text-height",p).attr("class",function(d){const o=O(d.startTime);let z=O(d.endTime);d.milestone&&(z=o+p);const P=this.getBBox().width;let R="";d.classes.length>0&&(R=d.classes.join(" "));let K=0;for(const[$,it]of U.entries())d.type===it&&(K=$%a.numberSectionStyles);let G="";return d.active&&(d.crit?G="activeCritText"+K:G="activeText"+K),d.done?d.crit?G=G+" doneCritText"+K:G=G+" doneText"+K:d.crit&&(G=G+" critText"+K),d.milestone&&(G+=" milestoneText"),P>z-o?z+P+1.5*a.leftPadding>u?R+" taskTextOutsideLeft taskTextOutside"+K+" "+G:R+" taskTextOutsideRight taskTextOutside"+K+" "+G+" width-"+P:R+" taskText taskText"+K+" "+G+" width-"+P}),_t().securityLevel==="sandbox"){let d;d=Bt("#i"+e);const o=d.nodes()[0].contentDocument;m.filter(function(z){return E.has(z.id)}).each(function(z){var P=o.querySelector("#"+z.id),R=o.querySelector("#"+z.id+"-text");const K=P.parentNode;var G=o.createElement("a");G.setAttribute("xlink:href",E.get(z.id)),G.setAttribute("target","_top"),K.appendChild(G),G.appendChild(P),G.appendChild(R)})}}h(V,"drawRects");function W(b,F,_,S,p,Y,u,f){if(u.length===0&&f.length===0)return;let y,m;for(const{startTime:P,endTime:R}of Y)(y===void 0||P<y)&&(y=P),(m===void 0||R>m)&&(m=R);if(!y||!m)return;if(at(m).diff(at(y),"year")>5){Kt.warn("The difference between the min and max time is more than 5 years. This will cause performance issues. Skipping drawing exclude days.");return}const E=r.db.getDateFormat(),c=[];let d=null,o=at(y);for(;o.valueOf()<=m;)r.db.isInvalidDate(o,E,u,f)?d?d.end=o:d={start:o,end:o}:d&&(c.push(d),d=null),o=o.add(1,"d");X.append("g").selectAll("rect").data(c).enter().append("rect").attr("id",function(P){return"exclude-"+P.start.format("YYYY-MM-DD")}).attr("x",function(P){return O(P.start)+_}).attr("y",a.gridLineStartPadding).attr("width",function(P){const R=P.end.add(1,"day");return O(R)-O(P.start)}).attr("height",p-F-a.gridLineStartPadding).attr("transform-origin",function(P,R){return(O(P.start)+_+.5*(O(P.end)-O(P.start))).toString()+"px "+(R*b+.5*p).toString()+"px"}).attr("class","exclude-range")}h(W,"drawExcludeDays");function B(b,F,_,S){let p=sr(O).tickSize(-S+F+a.gridLineStartPadding).tickFormat(ne(r.db.getAxisFormat()||a.axisFormat||"%Y-%m-%d"));const u=/^([1-9]\d*)(millisecond|second|minute|hour|day|week|month)$/.exec(r.db.getTickInterval()||a.tickInterval);if(u!==null){const f=u[1],y=u[2],m=r.db.getWeekday()||a.weekday;switch(y){case"millisecond":p.ticks(Yt.every(f));break;case"second":p.ticks(vt.every(f));break;case"minute":p.ticks(Wt.every(f));break;case"hour":p.ticks(Ot.every(f));break;case"day":p.ticks(Tt.every(f));break;case"week":p.ticks(en[m].every(f));break;case"month":p.ticks(Nt.every(f));break}}if(X.append("g").attr("class","grid").attr("transform","translate("+b+", "+(S-50)+")").call(p).selectAll("text").style("text-anchor","middle").attr("fill","#000").attr("stroke","none").attr("font-size",10).attr("dy","1em"),r.db.topAxisEnabled()||a.topAxis){let f=ir(O).tickSize(-S+F+a.gridLineStartPadding).tickFormat(ne(r.db.getAxisFormat()||a.axisFormat||"%Y-%m-%d"));if(u!==null){const y=u[1],m=u[2],E=r.db.getWeekday()||a.weekday;switch(m){case"millisecond":f.ticks(Yt.every(y));break;case"second":f.ticks(vt.every(y));break;case"minute":f.ticks(Wt.every(y));break;case"hour":f.ticks(Ot.every(y));break;case"day":f.ticks(Tt.every(y));break;case"week":f.ticks(en[E].every(y));break;case"month":f.ticks(Nt.every(y));break}}X.append("g").attr("class","grid").attr("transform","translate("+b+", "+F+")").call(f).selectAll("text").style("text-anchor","middle").attr("fill","#000").attr("stroke","none").attr("font-size",10)}}h(B,"makeGrid");function j(b,F){let _=0;const S=Object.keys(C).map(p=>[p,C[p]]);X.append("g").selectAll("text").data(S).enter().append(function(p){const Y=p[0].split(zn.lineBreakRegex),u=-(Y.length-1)/2,f=M.createElementNS("http://www.w3.org/2000/svg","text");f.setAttribute("dy",u+"em");for(const[y,m]of Y.entries()){const E=M.createElementNS("http://www.w3.org/2000/svg","tspan");E.setAttribute("alignment-baseline","central"),E.setAttribute("x","10"),y>0&&E.setAttribute("dy","1em"),E.textContent=m,f.appendChild(E)}return f}).attr("x",10).attr("y",function(p,Y){if(Y>0)for(let u=0;u<Y;u++)return _+=S[Y-1][1],p[1]*b/2+_*b+F;else return p[1]*b/2+F}).attr("font-size",a.sectionFontSize).attr("class",function(p){for(const[Y,u]of U.entries())if(p[0]===u)return"sectionTitle sectionTitle"+Y%a.numberSectionStyles;return"sectionTitle"})}h(j,"vertLabels");function w(b,F,_,S){const p=r.db.getTodayMarker();if(p==="off")return;const Y=X.append("g").attr("class","today"),u=new Date,f=Y.append("line");f.attr("x1",O(u)+b).attr("x2",O(u)+b).attr("y1",a.titleTopMargin).attr("y2",S-a.titleTopMargin).attr("class","today"),p!==""&&f.attr("style",p.replace(/,/g,";"))}h(w,"drawToday");function H(b){const F={},_=[];for(let S=0,p=b.length;S<p;++S)Object.prototype.hasOwnProperty.call(F,b[S])||(F[b[S]]=!0,_.push(b[S]));return _}h(H,"checkUnique")},"draw"),Si={setConf:Di,draw:_i},Fi=h(t=>`
  .mermaid-main-font {
        font-family: ${t.fontFamily};
  }

  .exclude-range {
    fill: ${t.excludeBkgColor};
  }

  .section {
    stroke: none;
    opacity: 0.2;
  }

  .section0 {
    fill: ${t.sectionBkgColor};
  }

  .section2 {
    fill: ${t.sectionBkgColor2};
  }

  .section1,
  .section3 {
    fill: ${t.altSectionBkgColor};
    opacity: 0.2;
  }

  .sectionTitle0 {
    fill: ${t.titleColor};
  }

  .sectionTitle1 {
    fill: ${t.titleColor};
  }

  .sectionTitle2 {
    fill: ${t.titleColor};
  }

  .sectionTitle3 {
    fill: ${t.titleColor};
  }

  .sectionTitle {
    text-anchor: start;
    font-family: ${t.fontFamily};
  }


  /* Grid and axis */

  .grid .tick {
    stroke: ${t.gridColor};
    opacity: 0.8;
    shape-rendering: crispEdges;
  }

  .grid .tick text {
    font-family: ${t.fontFamily};
    fill: ${t.textColor};
  }

  .grid path {
    stroke-width: 0;
  }


  /* Today line */

  .today {
    fill: none;
    stroke: ${t.todayLineColor};
    stroke-width: 2px;
  }


  /* Task styling */

  /* Default task */

  .task {
    stroke-width: 2;
  }

  .taskText {
    text-anchor: middle;
    font-family: ${t.fontFamily};
  }

  .taskTextOutsideRight {
    fill: ${t.taskTextDarkColor};
    text-anchor: start;
    font-family: ${t.fontFamily};
  }

  .taskTextOutsideLeft {
    fill: ${t.taskTextDarkColor};
    text-anchor: end;
  }


  /* Special case clickable */

  .task.clickable {
    cursor: pointer;
  }

  .taskText.clickable {
    cursor: pointer;
    fill: ${t.taskTextClickableColor} !important;
    font-weight: bold;
  }

  .taskTextOutsideLeft.clickable {
    cursor: pointer;
    fill: ${t.taskTextClickableColor} !important;
    font-weight: bold;
  }

  .taskTextOutsideRight.clickable {
    cursor: pointer;
    fill: ${t.taskTextClickableColor} !important;
    font-weight: bold;
  }


  /* Specific task settings for the sections*/

  .taskText0,
  .taskText1,
  .taskText2,
  .taskText3 {
    fill: ${t.taskTextColor};
  }

  .task0,
  .task1,
  .task2,
  .task3 {
    fill: ${t.taskBkgColor};
    stroke: ${t.taskBorderColor};
  }

  .taskTextOutside0,
  .taskTextOutside2
  {
    fill: ${t.taskTextOutsideColor};
  }

  .taskTextOutside1,
  .taskTextOutside3 {
    fill: ${t.taskTextOutsideColor};
  }


  /* Active task */

  .active0,
  .active1,
  .active2,
  .active3 {
    fill: ${t.activeTaskBkgColor};
    stroke: ${t.activeTaskBorderColor};
  }

  .activeText0,
  .activeText1,
  .activeText2,
  .activeText3 {
    fill: ${t.taskTextDarkColor} !important;
  }


  /* Completed task */

  .done0,
  .done1,
  .done2,
  .done3 {
    stroke: ${t.doneTaskBorderColor};
    fill: ${t.doneTaskBkgColor};
    stroke-width: 2;
  }

  .doneText0,
  .doneText1,
  .doneText2,
  .doneText3 {
    fill: ${t.taskTextDarkColor} !important;
  }


  /* Tasks on the critical line */

  .crit0,
  .crit1,
  .crit2,
  .crit3 {
    stroke: ${t.critBorderColor};
    fill: ${t.critBkgColor};
    stroke-width: 2;
  }

  .activeCrit0,
  .activeCrit1,
  .activeCrit2,
  .activeCrit3 {
    stroke: ${t.critBorderColor};
    fill: ${t.activeTaskBkgColor};
    stroke-width: 2;
  }

  .doneCrit0,
  .doneCrit1,
  .doneCrit2,
  .doneCrit3 {
    stroke: ${t.critBorderColor};
    fill: ${t.doneTaskBkgColor};
    stroke-width: 2;
    cursor: pointer;
    shape-rendering: crispEdges;
  }

  .milestone {
    transform: rotate(45deg) scale(0.8,0.8);
  }

  .milestoneText {
    font-style: italic;
  }
  .doneCritText0,
  .doneCritText1,
  .doneCritText2,
  .doneCritText3 {
    fill: ${t.taskTextDarkColor} !important;
  }

  .activeCritText0,
  .activeCritText1,
  .activeCritText2,
  .activeCritText3 {
    fill: ${t.taskTextDarkColor} !important;
  }

  .titleText {
    text-anchor: middle;
    font-size: 18px;
    fill: ${t.titleColor||t.textColor};
    font-family: ${t.fontFamily};
  }
`,"getStyles"),Yi=Fi,Li={parser:za,db:Ci,renderer:Si,styles:Yi};export{Li as diagram};
//# sourceMappingURL=ganttDiagram-APWFNJXF.js.map
