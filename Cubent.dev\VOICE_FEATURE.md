# 🎤 Voice Input Feature for Cubent Extension

The Cubent VSCode extension now includes **voice-to-text input** powered by whisper.cpp, allowing you to speak your coding requests directly into the chat interface.

## ✨ Features

- **🎙️ Voice Recording**: Click the microphone button to start/stop recording
- **🔄 Real-time Transcription**: Speech is converted to text using local AI models
- **🔒 Privacy-First**: All processing happens locally - no data sent to external services
- **⚡ Fast & Accurate**: Uses OpenAI's Whisper model for high-quality transcription
- **🌐 Cross-Platform**: Works on Windows, macOS, and Linux

## 🚀 How to Use

1. **Open Cubent Chat**: Open the Cubent extension sidebar in VSCode
2. **Click Microphone**: Look for the 🎤 microphone icon in the chat input toolbar
3. **Start Recording**: Click the microphone to start recording (icon turns red)
4. **Speak Clearly**: Speak your coding request or question
5. **Stop Recording**: Click the microphone again to stop (icon shows loading spinner)
6. **Review Text**: The transcribed text appears in the chat input box
7. **Send Message**: Edit if needed, then send your message as usual

## 🎯 Voice Input Tips

### **Best Practices**
- **Speak Clearly**: Use a normal speaking pace and clear pronunciation
- **Quiet Environment**: Record in a quiet space for best accuracy
- **Short Requests**: Break long requests into shorter voice segments
- **Technical Terms**: Spell out complex variable names or technical terms

### **Example Voice Commands**
- *"Create a React component for a user profile card"*
- *"Fix the TypeScript error in the authentication service"*
- *"Add error handling to the API request function"*
- *"Explain how this sorting algorithm works"*
- *"Refactor this code to use async await instead of promises"*

## 🔧 Technical Details

### **Whisper.cpp Integration**
- **Model**: Uses `ggml-base.en.bin` (English-optimized base model)
- **Processing**: Local transcription with no internet required
- **Audio Format**: 16kHz WAV files for optimal quality
- **Latency**: ~2-5 seconds for typical voice messages

### **System Requirements**
- **Windows**: PowerShell for audio recording
- **macOS**: Built-in `rec` command or SoX
- **Linux**: ALSA `arecord` utility
- **Storage**: ~150MB for whisper.cpp binaries and models

### **Installation**
The voice feature is automatically installed when you build the extension:

```bash
npm run build  # Automatically runs whisper installation
```

Manual installation:
```bash
npm run install-whisper
```

## 🛠️ Troubleshooting

### **Common Issues**

**🎤 Microphone Not Working**
- Check system microphone permissions
- Ensure microphone is not used by other applications
- Try restarting VSCode

**📝 Poor Transcription Quality**
- Speak more clearly and slowly
- Reduce background noise
- Check microphone positioning

**⚠️ Installation Errors**
- Run `npm run install-whisper` manually
- Check internet connection for model downloads
- Verify system has required audio tools

**🔧 Manual Installation**
If automatic installation fails:

1. Download whisper.cpp from: https://github.com/ggml-org/whisper.cpp/releases
2. Extract binary to: `Cubent.dev/whisper/bin/`
3. Download base.en model to: `Cubent.dev/whisper/models/`
4. Restart VSCode

## 🎉 Benefits

### **For Developers**
- **⚡ Faster Input**: Speak faster than you can type
- **🤲 Hands-Free**: Keep hands on keyboard while describing issues
- **🧠 Natural Language**: Describe problems in natural speech
- **♿ Accessibility**: Great for developers with typing difficulties

### **For Teams**
- **📝 Better Documentation**: Easily describe complex code changes
- **🎯 Clearer Requests**: Natural language leads to better AI responses
- **⏱️ Time Saving**: Reduce time spent typing long explanations

## 🔮 Future Enhancements

- **🌍 Multi-language Support**: Support for non-English languages
- **🎛️ Voice Commands**: Direct voice commands for common actions
- **📊 Custom Models**: Support for domain-specific vocabulary
- **🔄 Continuous Recording**: Always-on voice detection
- **🎨 Voice Profiles**: Personalized voice recognition

## 📞 Support

If you encounter any issues with the voice feature:

1. Check the VSCode Developer Console for error messages
2. Verify whisper.cpp installation in `Cubent.dev/whisper/`
3. Test system microphone with other applications
4. Report issues at: https://github.com/LaxBloxBoy2/cubent-extension/issues

---

**🎤 Happy Voice Coding with Cubent!**
