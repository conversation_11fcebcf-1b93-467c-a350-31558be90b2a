{"version": 3, "file": "linear.js", "sources": ["../../../../node_modules/.pnpm/d3-array@3.2.4/node_modules/d3-array/src/ascending.js", "../../../../node_modules/.pnpm/d3-array@3.2.4/node_modules/d3-array/src/descending.js", "../../../../node_modules/.pnpm/d3-array@3.2.4/node_modules/d3-array/src/bisector.js", "../../../../node_modules/.pnpm/d3-array@3.2.4/node_modules/d3-array/src/number.js", "../../../../node_modules/.pnpm/d3-array@3.2.4/node_modules/d3-array/src/bisect.js", "../../../../node_modules/.pnpm/d3-array@3.2.4/node_modules/d3-array/src/ticks.js", "../../../../node_modules/.pnpm/d3-interpolate@3.0.1/node_modules/d3-interpolate/src/numberArray.js", "../../../../node_modules/.pnpm/d3-interpolate@3.0.1/node_modules/d3-interpolate/src/array.js", "../../../../node_modules/.pnpm/d3-interpolate@3.0.1/node_modules/d3-interpolate/src/date.js", "../../../../node_modules/.pnpm/d3-interpolate@3.0.1/node_modules/d3-interpolate/src/object.js", "../../../../node_modules/.pnpm/d3-interpolate@3.0.1/node_modules/d3-interpolate/src/value.js", "../../../../node_modules/.pnpm/d3-interpolate@3.0.1/node_modules/d3-interpolate/src/round.js", "../../../../node_modules/.pnpm/d3-format@3.1.0/node_modules/d3-format/src/formatDecimal.js", "../../../../node_modules/.pnpm/d3-format@3.1.0/node_modules/d3-format/src/exponent.js", "../../../../node_modules/.pnpm/d3-format@3.1.0/node_modules/d3-format/src/formatGroup.js", "../../../../node_modules/.pnpm/d3-format@3.1.0/node_modules/d3-format/src/formatNumerals.js", "../../../../node_modules/.pnpm/d3-format@3.1.0/node_modules/d3-format/src/formatSpecifier.js", "../../../../node_modules/.pnpm/d3-format@3.1.0/node_modules/d3-format/src/formatTrim.js", "../../../../node_modules/.pnpm/d3-format@3.1.0/node_modules/d3-format/src/formatPrefixAuto.js", "../../../../node_modules/.pnpm/d3-format@3.1.0/node_modules/d3-format/src/formatRounded.js", "../../../../node_modules/.pnpm/d3-format@3.1.0/node_modules/d3-format/src/formatTypes.js", "../../../../node_modules/.pnpm/d3-format@3.1.0/node_modules/d3-format/src/identity.js", "../../../../node_modules/.pnpm/d3-format@3.1.0/node_modules/d3-format/src/locale.js", "../../../../node_modules/.pnpm/d3-format@3.1.0/node_modules/d3-format/src/defaultLocale.js", "../../../../node_modules/.pnpm/d3-format@3.1.0/node_modules/d3-format/src/precisionFixed.js", "../../../../node_modules/.pnpm/d3-format@3.1.0/node_modules/d3-format/src/precisionPrefix.js", "../../../../node_modules/.pnpm/d3-format@3.1.0/node_modules/d3-format/src/precisionRound.js", "../../../../node_modules/.pnpm/d3-scale@4.0.2/node_modules/d3-scale/src/constant.js", "../../../../node_modules/.pnpm/d3-scale@4.0.2/node_modules/d3-scale/src/number.js", "../../../../node_modules/.pnpm/d3-scale@4.0.2/node_modules/d3-scale/src/continuous.js", "../../../../node_modules/.pnpm/d3-scale@4.0.2/node_modules/d3-scale/src/tickFormat.js", "../../../../node_modules/.pnpm/d3-scale@4.0.2/node_modules/d3-scale/src/linear.js"], "sourcesContent": ["export default function ascending(a, b) {\n  return a == null || b == null ? NaN : a < b ? -1 : a > b ? 1 : a >= b ? 0 : NaN;\n}\n", "export default function descending(a, b) {\n  return a == null || b == null ? NaN\n    : b < a ? -1\n    : b > a ? 1\n    : b >= a ? 0\n    : NaN;\n}\n", "import ascending from \"./ascending.js\";\nimport descending from \"./descending.js\";\n\nexport default function bisector(f) {\n  let compare1, compare2, delta;\n\n  // If an accessor is specified, promote it to a comparator. In this case we\n  // can test whether the search value is (self-) comparable. We can’t do this\n  // for a comparator (except for specific, known comparators) because we can’t\n  // tell if the comparator is symmetric, and an asymmetric comparator can’t be\n  // used to test whether a single value is comparable.\n  if (f.length !== 2) {\n    compare1 = ascending;\n    compare2 = (d, x) => ascending(f(d), x);\n    delta = (d, x) => f(d) - x;\n  } else {\n    compare1 = f === ascending || f === descending ? f : zero;\n    compare2 = f;\n    delta = f;\n  }\n\n  function left(a, x, lo = 0, hi = a.length) {\n    if (lo < hi) {\n      if (compare1(x, x) !== 0) return hi;\n      do {\n        const mid = (lo + hi) >>> 1;\n        if (compare2(a[mid], x) < 0) lo = mid + 1;\n        else hi = mid;\n      } while (lo < hi);\n    }\n    return lo;\n  }\n\n  function right(a, x, lo = 0, hi = a.length) {\n    if (lo < hi) {\n      if (compare1(x, x) !== 0) return hi;\n      do {\n        const mid = (lo + hi) >>> 1;\n        if (compare2(a[mid], x) <= 0) lo = mid + 1;\n        else hi = mid;\n      } while (lo < hi);\n    }\n    return lo;\n  }\n\n  function center(a, x, lo = 0, hi = a.length) {\n    const i = left(a, x, lo, hi - 1);\n    return i > lo && delta(a[i - 1], x) > -delta(a[i], x) ? i - 1 : i;\n  }\n\n  return {left, center, right};\n}\n\nfunction zero() {\n  return 0;\n}\n", "export default function number(x) {\n  return x === null ? NaN : +x;\n}\n\nexport function* numbers(values, valueof) {\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value != null && (value = +value) >= value) {\n        yield value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null && (value = +value) >= value) {\n        yield value;\n      }\n    }\n  }\n}\n", "import ascending from \"./ascending.js\";\nimport bisector from \"./bisector.js\";\nimport number from \"./number.js\";\n\nconst ascendingBisect = bisector(ascending);\nexport const bisectRight = ascendingBisect.right;\nexport const bisectLeft = ascendingBisect.left;\nexport const bisectCenter = bisector(number).center;\nexport default bisectRight;\n", "const e10 = Math.sqrt(50),\n    e5 = Math.sqrt(10),\n    e2 = Math.sqrt(2);\n\nfunction tickSpec(start, stop, count) {\n  const step = (stop - start) / Math.max(0, count),\n      power = Math.floor(Math.log10(step)),\n      error = step / Math.pow(10, power),\n      factor = error >= e10 ? 10 : error >= e5 ? 5 : error >= e2 ? 2 : 1;\n  let i1, i2, inc;\n  if (power < 0) {\n    inc = Math.pow(10, -power) / factor;\n    i1 = Math.round(start * inc);\n    i2 = Math.round(stop * inc);\n    if (i1 / inc < start) ++i1;\n    if (i2 / inc > stop) --i2;\n    inc = -inc;\n  } else {\n    inc = Math.pow(10, power) * factor;\n    i1 = Math.round(start / inc);\n    i2 = Math.round(stop / inc);\n    if (i1 * inc < start) ++i1;\n    if (i2 * inc > stop) --i2;\n  }\n  if (i2 < i1 && 0.5 <= count && count < 2) return tickSpec(start, stop, count * 2);\n  return [i1, i2, inc];\n}\n\nexport default function ticks(start, stop, count) {\n  stop = +stop, start = +start, count = +count;\n  if (!(count > 0)) return [];\n  if (start === stop) return [start];\n  const reverse = stop < start, [i1, i2, inc] = reverse ? tickSpec(stop, start, count) : tickSpec(start, stop, count);\n  if (!(i2 >= i1)) return [];\n  const n = i2 - i1 + 1, ticks = new Array(n);\n  if (reverse) {\n    if (inc < 0) for (let i = 0; i < n; ++i) ticks[i] = (i2 - i) / -inc;\n    else for (let i = 0; i < n; ++i) ticks[i] = (i2 - i) * inc;\n  } else {\n    if (inc < 0) for (let i = 0; i < n; ++i) ticks[i] = (i1 + i) / -inc;\n    else for (let i = 0; i < n; ++i) ticks[i] = (i1 + i) * inc;\n  }\n  return ticks;\n}\n\nexport function tickIncrement(start, stop, count) {\n  stop = +stop, start = +start, count = +count;\n  return tickSpec(start, stop, count)[2];\n}\n\nexport function tickStep(start, stop, count) {\n  stop = +stop, start = +start, count = +count;\n  const reverse = stop < start, inc = reverse ? tickIncrement(stop, start, count) : tickIncrement(start, stop, count);\n  return (reverse ? -1 : 1) * (inc < 0 ? 1 / -inc : inc);\n}\n", "export default function(a, b) {\n  if (!b) b = [];\n  var n = a ? Math.min(b.length, a.length) : 0,\n      c = b.slice(),\n      i;\n  return function(t) {\n    for (i = 0; i < n; ++i) c[i] = a[i] * (1 - t) + b[i] * t;\n    return c;\n  };\n}\n\nexport function isNumberArray(x) {\n  return ArrayBuffer.isView(x) && !(x instanceof DataView);\n}\n", "import value from \"./value.js\";\nimport numberArray, {isNumberArray} from \"./numberArray.js\";\n\nexport default function(a, b) {\n  return (isNumberArray(b) ? numberArray : genericArray)(a, b);\n}\n\nexport function genericArray(a, b) {\n  var nb = b ? b.length : 0,\n      na = a ? Math.min(nb, a.length) : 0,\n      x = new Array(na),\n      c = new Array(nb),\n      i;\n\n  for (i = 0; i < na; ++i) x[i] = value(a[i], b[i]);\n  for (; i < nb; ++i) c[i] = b[i];\n\n  return function(t) {\n    for (i = 0; i < na; ++i) c[i] = x[i](t);\n    return c;\n  };\n}\n", "export default function(a, b) {\n  var d = new Date;\n  return a = +a, b = +b, function(t) {\n    return d.setTime(a * (1 - t) + b * t), d;\n  };\n}\n", "import value from \"./value.js\";\n\nexport default function(a, b) {\n  var i = {},\n      c = {},\n      k;\n\n  if (a === null || typeof a !== \"object\") a = {};\n  if (b === null || typeof b !== \"object\") b = {};\n\n  for (k in b) {\n    if (k in a) {\n      i[k] = value(a[k], b[k]);\n    } else {\n      c[k] = b[k];\n    }\n  }\n\n  return function(t) {\n    for (k in i) c[k] = i[k](t);\n    return c;\n  };\n}\n", "import {color} from \"d3-color\";\nimport rgb from \"./rgb.js\";\nimport {genericArray} from \"./array.js\";\nimport date from \"./date.js\";\nimport number from \"./number.js\";\nimport object from \"./object.js\";\nimport string from \"./string.js\";\nimport constant from \"./constant.js\";\nimport numberArray, {isNumberArray} from \"./numberArray.js\";\n\nexport default function(a, b) {\n  var t = typeof b, c;\n  return b == null || t === \"boolean\" ? constant(b)\n      : (t === \"number\" ? number\n      : t === \"string\" ? ((c = color(b)) ? (b = c, rgb) : string)\n      : b instanceof color ? rgb\n      : b instanceof Date ? date\n      : isNumberArray(b) ? numberArray\n      : Array.isArray(b) ? genericArray\n      : typeof b.valueOf !== \"function\" && typeof b.toString !== \"function\" || isNaN(b) ? object\n      : number)(a, b);\n}\n", "export default function(a, b) {\n  return a = +a, b = +b, function(t) {\n    return Math.round(a * (1 - t) + b * t);\n  };\n}\n", "export default function(x) {\n  return Math.abs(x = Math.round(x)) >= 1e21\n      ? x.toLocaleString(\"en\").replace(/,/g, \"\")\n      : x.toString(10);\n}\n\n// Computes the decimal coefficient and exponent of the specified number x with\n// significant digits p, where x is positive and p is in [1, 21] or undefined.\n// For example, formatDecimalParts(1.23) returns [\"123\", 0].\nexport function formatDecimalParts(x, p) {\n  if ((i = (x = p ? x.toExponential(p - 1) : x.toExponential()).indexOf(\"e\")) < 0) return null; // NaN, ±Infinity\n  var i, coefficient = x.slice(0, i);\n\n  // The string returned by toExponential either has the form \\d\\.\\d+e[-+]\\d+\n  // (e.g., 1.2e+3) or the form \\de[-+]\\d+ (e.g., 1e+3).\n  return [\n    coefficient.length > 1 ? coefficient[0] + coefficient.slice(2) : coefficient,\n    +x.slice(i + 1)\n  ];\n}\n", "import {formatDecimalParts} from \"./formatDecimal.js\";\n\nexport default function(x) {\n  return x = formatDecimalParts(Math.abs(x)), x ? x[1] : NaN;\n}\n", "export default function(grouping, thousands) {\n  return function(value, width) {\n    var i = value.length,\n        t = [],\n        j = 0,\n        g = grouping[0],\n        length = 0;\n\n    while (i > 0 && g > 0) {\n      if (length + g + 1 > width) g = Math.max(1, width - length);\n      t.push(value.substring(i -= g, i + g));\n      if ((length += g + 1) > width) break;\n      g = grouping[j = (j + 1) % grouping.length];\n    }\n\n    return t.reverse().join(thousands);\n  };\n}\n", "export default function(numerals) {\n  return function(value) {\n    return value.replace(/[0-9]/g, function(i) {\n      return numerals[+i];\n    });\n  };\n}\n", "// [[fill]align][sign][symbol][0][width][,][.precision][~][type]\nvar re = /^(?:(.)?([<>=^]))?([+\\-( ])?([$#])?(0)?(\\d+)?(,)?(\\.\\d+)?(~)?([a-z%])?$/i;\n\nexport default function formatSpecifier(specifier) {\n  if (!(match = re.exec(specifier))) throw new Error(\"invalid format: \" + specifier);\n  var match;\n  return new FormatSpecifier({\n    fill: match[1],\n    align: match[2],\n    sign: match[3],\n    symbol: match[4],\n    zero: match[5],\n    width: match[6],\n    comma: match[7],\n    precision: match[8] && match[8].slice(1),\n    trim: match[9],\n    type: match[10]\n  });\n}\n\nformatSpecifier.prototype = FormatSpecifier.prototype; // instanceof\n\nexport function FormatSpecifier(specifier) {\n  this.fill = specifier.fill === undefined ? \" \" : specifier.fill + \"\";\n  this.align = specifier.align === undefined ? \">\" : specifier.align + \"\";\n  this.sign = specifier.sign === undefined ? \"-\" : specifier.sign + \"\";\n  this.symbol = specifier.symbol === undefined ? \"\" : specifier.symbol + \"\";\n  this.zero = !!specifier.zero;\n  this.width = specifier.width === undefined ? undefined : +specifier.width;\n  this.comma = !!specifier.comma;\n  this.precision = specifier.precision === undefined ? undefined : +specifier.precision;\n  this.trim = !!specifier.trim;\n  this.type = specifier.type === undefined ? \"\" : specifier.type + \"\";\n}\n\nFormatSpecifier.prototype.toString = function() {\n  return this.fill\n      + this.align\n      + this.sign\n      + this.symbol\n      + (this.zero ? \"0\" : \"\")\n      + (this.width === undefined ? \"\" : Math.max(1, this.width | 0))\n      + (this.comma ? \",\" : \"\")\n      + (this.precision === undefined ? \"\" : \".\" + Math.max(0, this.precision | 0))\n      + (this.trim ? \"~\" : \"\")\n      + this.type;\n};\n", "// Trims insignificant zeros, e.g., replaces 1.2000k with 1.2k.\nexport default function(s) {\n  out: for (var n = s.length, i = 1, i0 = -1, i1; i < n; ++i) {\n    switch (s[i]) {\n      case \".\": i0 = i1 = i; break;\n      case \"0\": if (i0 === 0) i0 = i; i1 = i; break;\n      default: if (!+s[i]) break out; if (i0 > 0) i0 = 0; break;\n    }\n  }\n  return i0 > 0 ? s.slice(0, i0) + s.slice(i1 + 1) : s;\n}\n", "import {formatDecimalParts} from \"./formatDecimal.js\";\n\nexport var prefixExponent;\n\nexport default function(x, p) {\n  var d = formatDecimalParts(x, p);\n  if (!d) return x + \"\";\n  var coefficient = d[0],\n      exponent = d[1],\n      i = exponent - (prefixExponent = Math.max(-8, Math.min(8, Math.floor(exponent / 3))) * 3) + 1,\n      n = coefficient.length;\n  return i === n ? coefficient\n      : i > n ? coefficient + new Array(i - n + 1).join(\"0\")\n      : i > 0 ? coefficient.slice(0, i) + \".\" + coefficient.slice(i)\n      : \"0.\" + new Array(1 - i).join(\"0\") + formatDecimalParts(x, Math.max(0, p + i - 1))[0]; // less than 1y!\n}\n", "import {formatDecimalParts} from \"./formatDecimal.js\";\n\nexport default function(x, p) {\n  var d = formatDecimalParts(x, p);\n  if (!d) return x + \"\";\n  var coefficient = d[0],\n      exponent = d[1];\n  return exponent < 0 ? \"0.\" + new Array(-exponent).join(\"0\") + coefficient\n      : coefficient.length > exponent + 1 ? coefficient.slice(0, exponent + 1) + \".\" + coefficient.slice(exponent + 1)\n      : coefficient + new Array(exponent - coefficient.length + 2).join(\"0\");\n}\n", "import formatDecimal from \"./formatDecimal.js\";\nimport formatPrefixAuto from \"./formatPrefixAuto.js\";\nimport formatRounded from \"./formatRounded.js\";\n\nexport default {\n  \"%\": (x, p) => (x * 100).toFixed(p),\n  \"b\": (x) => Math.round(x).toString(2),\n  \"c\": (x) => x + \"\",\n  \"d\": formatDecimal,\n  \"e\": (x, p) => x.toExponential(p),\n  \"f\": (x, p) => x.toFixed(p),\n  \"g\": (x, p) => x.toPrecision(p),\n  \"o\": (x) => Math.round(x).toString(8),\n  \"p\": (x, p) => formatRounded(x * 100, p),\n  \"r\": formatRounded,\n  \"s\": formatPrefixAuto,\n  \"X\": (x) => Math.round(x).toString(16).toUpperCase(),\n  \"x\": (x) => Math.round(x).toString(16)\n};\n", "export default function(x) {\n  return x;\n}\n", "import exponent from \"./exponent.js\";\nimport formatGroup from \"./formatGroup.js\";\nimport formatNumerals from \"./formatNumerals.js\";\nimport formatSpecifier from \"./formatSpecifier.js\";\nimport formatTrim from \"./formatTrim.js\";\nimport formatTypes from \"./formatTypes.js\";\nimport {prefixExponent} from \"./formatPrefixAuto.js\";\nimport identity from \"./identity.js\";\n\nvar map = Array.prototype.map,\n    prefixes = [\"y\",\"z\",\"a\",\"f\",\"p\",\"n\",\"µ\",\"m\",\"\",\"k\",\"M\",\"G\",\"T\",\"P\",\"E\",\"Z\",\"Y\"];\n\nexport default function(locale) {\n  var group = locale.grouping === undefined || locale.thousands === undefined ? identity : formatGroup(map.call(locale.grouping, Number), locale.thousands + \"\"),\n      currencyPrefix = locale.currency === undefined ? \"\" : locale.currency[0] + \"\",\n      currencySuffix = locale.currency === undefined ? \"\" : locale.currency[1] + \"\",\n      decimal = locale.decimal === undefined ? \".\" : locale.decimal + \"\",\n      numerals = locale.numerals === undefined ? identity : formatNumerals(map.call(locale.numerals, String)),\n      percent = locale.percent === undefined ? \"%\" : locale.percent + \"\",\n      minus = locale.minus === undefined ? \"−\" : locale.minus + \"\",\n      nan = locale.nan === undefined ? \"NaN\" : locale.nan + \"\";\n\n  function newFormat(specifier) {\n    specifier = formatSpecifier(specifier);\n\n    var fill = specifier.fill,\n        align = specifier.align,\n        sign = specifier.sign,\n        symbol = specifier.symbol,\n        zero = specifier.zero,\n        width = specifier.width,\n        comma = specifier.comma,\n        precision = specifier.precision,\n        trim = specifier.trim,\n        type = specifier.type;\n\n    // The \"n\" type is an alias for \",g\".\n    if (type === \"n\") comma = true, type = \"g\";\n\n    // The \"\" type, and any invalid type, is an alias for \".12~g\".\n    else if (!formatTypes[type]) precision === undefined && (precision = 12), trim = true, type = \"g\";\n\n    // If zero fill is specified, padding goes after sign and before digits.\n    if (zero || (fill === \"0\" && align === \"=\")) zero = true, fill = \"0\", align = \"=\";\n\n    // Compute the prefix and suffix.\n    // For SI-prefix, the suffix is lazily computed.\n    var prefix = symbol === \"$\" ? currencyPrefix : symbol === \"#\" && /[boxX]/.test(type) ? \"0\" + type.toLowerCase() : \"\",\n        suffix = symbol === \"$\" ? currencySuffix : /[%p]/.test(type) ? percent : \"\";\n\n    // What format function should we use?\n    // Is this an integer type?\n    // Can this type generate exponential notation?\n    var formatType = formatTypes[type],\n        maybeSuffix = /[defgprs%]/.test(type);\n\n    // Set the default precision if not specified,\n    // or clamp the specified precision to the supported range.\n    // For significant precision, it must be in [1, 21].\n    // For fixed precision, it must be in [0, 20].\n    precision = precision === undefined ? 6\n        : /[gprs]/.test(type) ? Math.max(1, Math.min(21, precision))\n        : Math.max(0, Math.min(20, precision));\n\n    function format(value) {\n      var valuePrefix = prefix,\n          valueSuffix = suffix,\n          i, n, c;\n\n      if (type === \"c\") {\n        valueSuffix = formatType(value) + valueSuffix;\n        value = \"\";\n      } else {\n        value = +value;\n\n        // Determine the sign. -0 is not less than 0, but 1 / -0 is!\n        var valueNegative = value < 0 || 1 / value < 0;\n\n        // Perform the initial formatting.\n        value = isNaN(value) ? nan : formatType(Math.abs(value), precision);\n\n        // Trim insignificant zeros.\n        if (trim) value = formatTrim(value);\n\n        // If a negative value rounds to zero after formatting, and no explicit positive sign is requested, hide the sign.\n        if (valueNegative && +value === 0 && sign !== \"+\") valueNegative = false;\n\n        // Compute the prefix and suffix.\n        valuePrefix = (valueNegative ? (sign === \"(\" ? sign : minus) : sign === \"-\" || sign === \"(\" ? \"\" : sign) + valuePrefix;\n        valueSuffix = (type === \"s\" ? prefixes[8 + prefixExponent / 3] : \"\") + valueSuffix + (valueNegative && sign === \"(\" ? \")\" : \"\");\n\n        // Break the formatted value into the integer “value” part that can be\n        // grouped, and fractional or exponential “suffix” part that is not.\n        if (maybeSuffix) {\n          i = -1, n = value.length;\n          while (++i < n) {\n            if (c = value.charCodeAt(i), 48 > c || c > 57) {\n              valueSuffix = (c === 46 ? decimal + value.slice(i + 1) : value.slice(i)) + valueSuffix;\n              value = value.slice(0, i);\n              break;\n            }\n          }\n        }\n      }\n\n      // If the fill character is not \"0\", grouping is applied before padding.\n      if (comma && !zero) value = group(value, Infinity);\n\n      // Compute the padding.\n      var length = valuePrefix.length + value.length + valueSuffix.length,\n          padding = length < width ? new Array(width - length + 1).join(fill) : \"\";\n\n      // If the fill character is \"0\", grouping is applied after padding.\n      if (comma && zero) value = group(padding + value, padding.length ? width - valueSuffix.length : Infinity), padding = \"\";\n\n      // Reconstruct the final output based on the desired alignment.\n      switch (align) {\n        case \"<\": value = valuePrefix + value + valueSuffix + padding; break;\n        case \"=\": value = valuePrefix + padding + value + valueSuffix; break;\n        case \"^\": value = padding.slice(0, length = padding.length >> 1) + valuePrefix + value + valueSuffix + padding.slice(length); break;\n        default: value = padding + valuePrefix + value + valueSuffix; break;\n      }\n\n      return numerals(value);\n    }\n\n    format.toString = function() {\n      return specifier + \"\";\n    };\n\n    return format;\n  }\n\n  function formatPrefix(specifier, value) {\n    var f = newFormat((specifier = formatSpecifier(specifier), specifier.type = \"f\", specifier)),\n        e = Math.max(-8, Math.min(8, Math.floor(exponent(value) / 3))) * 3,\n        k = Math.pow(10, -e),\n        prefix = prefixes[8 + e / 3];\n    return function(value) {\n      return f(k * value) + prefix;\n    };\n  }\n\n  return {\n    format: newFormat,\n    formatPrefix: formatPrefix\n  };\n}\n", "import formatLocale from \"./locale.js\";\n\nvar locale;\nexport var format;\nexport var formatPrefix;\n\ndefaultLocale({\n  thousands: \",\",\n  grouping: [3],\n  currency: [\"$\", \"\"]\n});\n\nexport default function defaultLocale(definition) {\n  locale = formatLocale(definition);\n  format = locale.format;\n  formatPrefix = locale.formatPrefix;\n  return locale;\n}\n", "import exponent from \"./exponent.js\";\n\nexport default function(step) {\n  return Math.max(0, -exponent(Math.abs(step)));\n}\n", "import exponent from \"./exponent.js\";\n\nexport default function(step, value) {\n  return Math.max(0, Math.max(-8, Math.min(8, Math.floor(exponent(value) / 3))) * 3 - exponent(Math.abs(step)));\n}\n", "import exponent from \"./exponent.js\";\n\nexport default function(step, max) {\n  step = Math.abs(step), max = Math.abs(max) - step;\n  return Math.max(0, exponent(max) - exponent(step)) + 1;\n}\n", "export default function constants(x) {\n  return function() {\n    return x;\n  };\n}\n", "export default function number(x) {\n  return +x;\n}\n", "import {bisect} from \"d3-array\";\nimport {interpolate as interpolateValue, interpolateNumber, interpolateRound} from \"d3-interpolate\";\nimport constant from \"./constant.js\";\nimport number from \"./number.js\";\n\nvar unit = [0, 1];\n\nexport function identity(x) {\n  return x;\n}\n\nfunction normalize(a, b) {\n  return (b -= (a = +a))\n      ? function(x) { return (x - a) / b; }\n      : constant(isNaN(b) ? NaN : 0.5);\n}\n\nfunction clamper(a, b) {\n  var t;\n  if (a > b) t = a, a = b, b = t;\n  return function(x) { return Math.max(a, Math.min(b, x)); };\n}\n\n// normalize(a, b)(x) takes a domain value x in [a,b] and returns the corresponding parameter t in [0,1].\n// interpolate(a, b)(t) takes a parameter t in [0,1] and returns the corresponding range value x in [a,b].\nfunction bimap(domain, range, interpolate) {\n  var d0 = domain[0], d1 = domain[1], r0 = range[0], r1 = range[1];\n  if (d1 < d0) d0 = normalize(d1, d0), r0 = interpolate(r1, r0);\n  else d0 = normalize(d0, d1), r0 = interpolate(r0, r1);\n  return function(x) { return r0(d0(x)); };\n}\n\nfunction polymap(domain, range, interpolate) {\n  var j = Math.min(domain.length, range.length) - 1,\n      d = new Array(j),\n      r = new Array(j),\n      i = -1;\n\n  // Reverse descending domains.\n  if (domain[j] < domain[0]) {\n    domain = domain.slice().reverse();\n    range = range.slice().reverse();\n  }\n\n  while (++i < j) {\n    d[i] = normalize(domain[i], domain[i + 1]);\n    r[i] = interpolate(range[i], range[i + 1]);\n  }\n\n  return function(x) {\n    var i = bisect(domain, x, 1, j) - 1;\n    return r[i](d[i](x));\n  };\n}\n\nexport function copy(source, target) {\n  return target\n      .domain(source.domain())\n      .range(source.range())\n      .interpolate(source.interpolate())\n      .clamp(source.clamp())\n      .unknown(source.unknown());\n}\n\nexport function transformer() {\n  var domain = unit,\n      range = unit,\n      interpolate = interpolateValue,\n      transform,\n      untransform,\n      unknown,\n      clamp = identity,\n      piecewise,\n      output,\n      input;\n\n  function rescale() {\n    var n = Math.min(domain.length, range.length);\n    if (clamp !== identity) clamp = clamper(domain[0], domain[n - 1]);\n    piecewise = n > 2 ? polymap : bimap;\n    output = input = null;\n    return scale;\n  }\n\n  function scale(x) {\n    return x == null || isNaN(x = +x) ? unknown : (output || (output = piecewise(domain.map(transform), range, interpolate)))(transform(clamp(x)));\n  }\n\n  scale.invert = function(y) {\n    return clamp(untransform((input || (input = piecewise(range, domain.map(transform), interpolateNumber)))(y)));\n  };\n\n  scale.domain = function(_) {\n    return arguments.length ? (domain = Array.from(_, number), rescale()) : domain.slice();\n  };\n\n  scale.range = function(_) {\n    return arguments.length ? (range = Array.from(_), rescale()) : range.slice();\n  };\n\n  scale.rangeRound = function(_) {\n    return range = Array.from(_), interpolate = interpolateRound, rescale();\n  };\n\n  scale.clamp = function(_) {\n    return arguments.length ? (clamp = _ ? true : identity, rescale()) : clamp !== identity;\n  };\n\n  scale.interpolate = function(_) {\n    return arguments.length ? (interpolate = _, rescale()) : interpolate;\n  };\n\n  scale.unknown = function(_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n\n  return function(t, u) {\n    transform = t, untransform = u;\n    return rescale();\n  };\n}\n\nexport default function continuous() {\n  return transformer()(identity, identity);\n}\n", "import {tickStep} from \"d3-array\";\nimport {format, formatPrefix, formatSpecifier, precisionFixed, precisionPrefix, precisionRound} from \"d3-format\";\n\nexport default function tickFormat(start, stop, count, specifier) {\n  var step = tickStep(start, stop, count),\n      precision;\n  specifier = formatSpecifier(specifier == null ? \",f\" : specifier);\n  switch (specifier.type) {\n    case \"s\": {\n      var value = Math.max(Math.abs(start), Math.abs(stop));\n      if (specifier.precision == null && !isNaN(precision = precisionPrefix(step, value))) specifier.precision = precision;\n      return formatPrefix(specifier, value);\n    }\n    case \"\":\n    case \"e\":\n    case \"g\":\n    case \"p\":\n    case \"r\": {\n      if (specifier.precision == null && !isNaN(precision = precisionRound(step, Math.max(Math.abs(start), Math.abs(stop))))) specifier.precision = precision - (specifier.type === \"e\");\n      break;\n    }\n    case \"f\":\n    case \"%\": {\n      if (specifier.precision == null && !isNaN(precision = precisionFixed(step))) specifier.precision = precision - (specifier.type === \"%\") * 2;\n      break;\n    }\n  }\n  return format(specifier);\n}\n", "import {ticks, tickIncrement} from \"d3-array\";\nimport continuous, {copy} from \"./continuous.js\";\nimport {initRange} from \"./init.js\";\nimport tickFormat from \"./tickFormat.js\";\n\nexport function linearish(scale) {\n  var domain = scale.domain;\n\n  scale.ticks = function(count) {\n    var d = domain();\n    return ticks(d[0], d[d.length - 1], count == null ? 10 : count);\n  };\n\n  scale.tickFormat = function(count, specifier) {\n    var d = domain();\n    return tickFormat(d[0], d[d.length - 1], count == null ? 10 : count, specifier);\n  };\n\n  scale.nice = function(count) {\n    if (count == null) count = 10;\n\n    var d = domain();\n    var i0 = 0;\n    var i1 = d.length - 1;\n    var start = d[i0];\n    var stop = d[i1];\n    var prestep;\n    var step;\n    var maxIter = 10;\n\n    if (stop < start) {\n      step = start, start = stop, stop = step;\n      step = i0, i0 = i1, i1 = step;\n    }\n    \n    while (maxIter-- > 0) {\n      step = tickIncrement(start, stop, count);\n      if (step === prestep) {\n        d[i0] = start\n        d[i1] = stop\n        return domain(d);\n      } else if (step > 0) {\n        start = Math.floor(start / step) * step;\n        stop = Math.ceil(stop / step) * step;\n      } else if (step < 0) {\n        start = Math.ceil(start * step) / step;\n        stop = Math.floor(stop * step) / step;\n      } else {\n        break;\n      }\n      prestep = step;\n    }\n\n    return scale;\n  };\n\n  return scale;\n}\n\nexport default function linear() {\n  var scale = continuous();\n\n  scale.copy = function() {\n    return copy(scale, linear());\n  };\n\n  initRange.apply(scale, arguments);\n\n  return linearish(scale);\n}\n"], "names": ["ascending", "a", "b", "descending", "bisector", "f", "compare1", "compare2", "delta", "d", "x", "zero", "left", "lo", "hi", "mid", "right", "center", "i", "number", "ascendingBisect", "bisectRight", "e10", "e5", "e2", "tickSpec", "start", "stop", "count", "step", "power", "error", "factor", "i1", "i2", "inc", "ticks", "reverse", "n", "tickIncrement", "tickStep", "numberArray", "c", "t", "isNumberArray", "genericArray", "nb", "na", "value", "date", "object", "k", "interpolate", "constant", "color", "rgb", "string", "interpolateRound", "formatDecimal", "formatDecimalParts", "p", "coefficient", "exponent", "formatGroup", "grouping", "thousands", "width", "j", "g", "length", "formatNumerals", "numerals", "re", "formatSpecifier", "specifier", "match", "FormatSpecifier", "formatTrim", "s", "out", "i0", "prefixExponent", "formatPrefixAuto", "formatRounded", "formatTypes", "identity$1", "map", "prefixes", "formatLocale", "locale", "group", "identity", "currencyPrefix", "currencySuffix", "decimal", "percent", "minus", "nan", "newFormat", "fill", "align", "sign", "symbol", "comma", "precision", "trim", "type", "prefix", "suffix", "formatType", "maybeSuffix", "format", "valuePrefix", "valueSuffix", "valueNegative", "padding", "formatPrefix", "e", "defaultLocale", "definition", "precisionFixed", "precisionPrefix", "precisionRound", "max", "constants", "unit", "normalize", "clamper", "bimap", "domain", "range", "d0", "d1", "r0", "r1", "polymap", "r", "bisect", "copy", "source", "target", "transformer", "interpolateV<PERSON>ue", "transform", "untransform", "unknown", "clamp", "piecewise", "output", "input", "rescale", "scale", "y", "interpolateNumber", "_", "u", "continuous", "tickFormat", "linearish", "prestep", "maxIter", "linear", "initRange"], "mappings": "iGAAe,SAASA,EAAUC,EAAGC,EAAG,CACtC,OAAOD,GAAK,MAAQC,GAAK,KAAO,IAAMD,EAAIC,EAAI,GAAKD,EAAIC,EAAI,EAAID,GAAKC,EAAI,EAAI,GAC9E,CCFe,SAASC,GAAWF,EAAGC,EAAG,CACvC,OAAOD,GAAK,MAAQC,GAAK,KAAO,IAC5BA,EAAID,EAAI,GACRC,EAAID,EAAI,EACRC,GAAKD,EAAI,EACT,GACN,CCHe,SAASG,EAASC,EAAG,CAClC,IAAIC,EAAUC,EAAUC,EAOpBH,EAAE,SAAW,GACfC,EAAWN,EACXO,EAAW,CAACE,EAAGC,IAAMV,EAAUK,EAAEI,CAAC,EAAGC,CAAC,EACtCF,EAAQ,CAACC,EAAGC,IAAML,EAAEI,CAAC,EAAIC,IAEzBJ,EAAWD,IAAML,GAAaK,IAAMF,GAAaE,EAAIM,GACrDJ,EAAWF,EACXG,EAAQH,GAGV,SAASO,EAAKX,EAAGS,EAAGG,EAAK,EAAGC,EAAKb,EAAE,OAAQ,CACzC,GAAIY,EAAKC,EAAI,CACX,GAAIR,EAASI,EAAGA,CAAC,IAAM,EAAG,OAAOI,EACjC,EAAG,CACD,MAAMC,EAAOF,EAAKC,IAAQ,EACtBP,EAASN,EAAEc,CAAG,EAAGL,CAAC,EAAI,EAAGG,EAAKE,EAAM,EACnCD,EAAKC,CACX,OAAQF,EAAKC,EACpB,CACI,OAAOD,CACX,CAEE,SAASG,EAAMf,EAAGS,EAAGG,EAAK,EAAGC,EAAKb,EAAE,OAAQ,CAC1C,GAAIY,EAAKC,EAAI,CACX,GAAIR,EAASI,EAAGA,CAAC,IAAM,EAAG,OAAOI,EACjC,EAAG,CACD,MAAMC,EAAOF,EAAKC,IAAQ,EACtBP,EAASN,EAAEc,CAAG,EAAGL,CAAC,GAAK,EAAGG,EAAKE,EAAM,EACpCD,EAAKC,CACX,OAAQF,EAAKC,EACpB,CACI,OAAOD,CACX,CAEE,SAASI,EAAOhB,EAAGS,EAAGG,EAAK,EAAGC,EAAKb,EAAE,OAAQ,CAC3C,MAAMiB,EAAIN,EAAKX,EAAGS,EAAGG,EAAIC,EAAK,CAAC,EAC/B,OAAOI,EAAIL,GAAML,EAAMP,EAAEiB,EAAI,CAAC,EAAGR,CAAC,EAAI,CAACF,EAAMP,EAAEiB,CAAC,EAAGR,CAAC,EAAIQ,EAAI,EAAIA,CACpE,CAEE,MAAO,CAAC,KAAAN,EAAM,OAAAK,EAAQ,MAAAD,CAAK,CAC7B,CAEA,SAASL,IAAO,CACd,MAAO,EACT,CCvDe,SAASQ,GAAOT,EAAG,CAChC,OAAOA,IAAM,KAAO,IAAM,CAACA,CAC7B,CCEA,MAAMU,GAAkBhB,EAASJ,CAAS,EAC7BqB,GAAcD,GAAgB,MAEfhB,EAASe,EAAM,EAAE,OCP7C,MAAMG,GAAM,KAAK,KAAK,EAAE,EACpBC,GAAK,KAAK,KAAK,EAAE,EACjBC,GAAK,KAAK,KAAK,CAAC,EAEpB,SAASC,EAASC,EAAOC,EAAMC,EAAO,CACpC,MAAMC,GAAQF,EAAOD,GAAS,KAAK,IAAI,EAAGE,CAAK,EAC3CE,EAAQ,KAAK,MAAM,KAAK,MAAMD,CAAI,CAAC,EACnCE,EAAQF,EAAO,KAAK,IAAI,GAAIC,CAAK,EACjCE,EAASD,GAAST,GAAM,GAAKS,GAASR,GAAK,EAAIQ,GAASP,GAAK,EAAI,EACrE,IAAIS,EAAIC,EAAIC,EAeZ,OAdIL,EAAQ,GACVK,EAAM,KAAK,IAAI,GAAI,CAACL,CAAK,EAAIE,EAC7BC,EAAK,KAAK,MAAMP,EAAQS,CAAG,EAC3BD,EAAK,KAAK,MAAMP,EAAOQ,CAAG,EACtBF,EAAKE,EAAMT,GAAO,EAAEO,EACpBC,EAAKC,EAAMR,GAAM,EAAEO,EACvBC,EAAM,CAACA,IAEPA,EAAM,KAAK,IAAI,GAAIL,CAAK,EAAIE,EAC5BC,EAAK,KAAK,MAAMP,EAAQS,CAAG,EAC3BD,EAAK,KAAK,MAAMP,EAAOQ,CAAG,EACtBF,EAAKE,EAAMT,GAAO,EAAEO,EACpBC,EAAKC,EAAMR,GAAM,EAAEO,GAErBA,EAAKD,GAAM,IAAOL,GAASA,EAAQ,EAAUH,EAASC,EAAOC,EAAMC,EAAQ,CAAC,EACzE,CAACK,EAAIC,EAAIC,CAAG,CACrB,CAEe,SAASC,GAAMV,EAAOC,EAAMC,EAAO,CAEhD,GADAD,EAAO,CAACA,EAAMD,EAAQ,CAACA,EAAOE,EAAQ,CAACA,EACnC,EAAEA,EAAQ,GAAI,MAAO,CAAE,EAC3B,GAAIF,IAAUC,EAAM,MAAO,CAACD,CAAK,EACjC,MAAMW,EAAUV,EAAOD,EAAO,CAACO,EAAIC,EAAIC,CAAG,EAAIE,EAAUZ,EAASE,EAAMD,EAAOE,CAAK,EAAIH,EAASC,EAAOC,EAAMC,CAAK,EAClH,GAAI,EAAEM,GAAMD,GAAK,MAAO,CAAE,EAC1B,MAAMK,EAAIJ,EAAKD,EAAK,EAAGG,EAAQ,IAAI,MAAME,CAAC,EAC1C,GAAID,EACF,GAAIF,EAAM,EAAG,QAASjB,EAAI,EAAGA,EAAIoB,EAAG,EAAEpB,EAAGkB,EAAMlB,CAAC,GAAKgB,EAAKhB,GAAK,CAACiB,MAC3D,SAASjB,EAAI,EAAGA,EAAIoB,EAAG,EAAEpB,EAAGkB,EAAMlB,CAAC,GAAKgB,EAAKhB,GAAKiB,UAEnDA,EAAM,EAAG,QAASjB,EAAI,EAAGA,EAAIoB,EAAG,EAAEpB,EAAGkB,EAAMlB,CAAC,GAAKe,EAAKf,GAAK,CAACiB,MAC3D,SAASjB,EAAI,EAAGA,EAAIoB,EAAG,EAAEpB,EAAGkB,EAAMlB,CAAC,GAAKe,EAAKf,GAAKiB,EAEzD,OAAOC,CACT,CAEO,SAASG,EAAcb,EAAOC,EAAMC,EAAO,CAChD,OAAAD,EAAO,CAACA,EAAMD,EAAQ,CAACA,EAAOE,EAAQ,CAACA,EAChCH,EAASC,EAAOC,EAAMC,CAAK,EAAE,CAAC,CACvC,CAEO,SAASY,GAASd,EAAOC,EAAMC,EAAO,CAC3CD,EAAO,CAACA,EAAMD,EAAQ,CAACA,EAAOE,EAAQ,CAACA,EACvC,MAAMS,EAAUV,EAAOD,EAAOS,EAAME,EAAUE,EAAcZ,EAAMD,EAAOE,CAAK,EAAIW,EAAcb,EAAOC,EAAMC,CAAK,EAClH,OAAQS,EAAU,GAAK,IAAMF,EAAM,EAAI,EAAI,CAACA,EAAMA,EACpD,CCtDe,SAAAM,GAASxC,EAAGC,EAAG,CACvBA,IAAGA,EAAI,CAAE,GACd,IAAIoC,EAAIrC,EAAI,KAAK,IAAIC,EAAE,OAAQD,EAAE,MAAM,EAAI,EACvCyC,EAAIxC,EAAE,MAAO,EACb,EACJ,OAAO,SAASyC,EAAG,CACjB,IAAK,EAAI,EAAG,EAAIL,EAAG,EAAE,EAAGI,EAAE,CAAC,EAAIzC,EAAE,CAAC,GAAK,EAAI0C,GAAKzC,EAAE,CAAC,EAAIyC,EACvD,OAAOD,CACR,CACH,CAEO,SAASE,GAAclC,EAAG,CAC/B,OAAO,YAAY,OAAOA,CAAC,GAAK,EAAEA,aAAa,SACjD,CCNO,SAASmC,GAAa5C,EAAGC,EAAG,CACjC,IAAI4C,EAAK5C,EAAIA,EAAE,OAAS,EACpB6C,EAAK9C,EAAI,KAAK,IAAI6C,EAAI7C,EAAE,MAAM,EAAI,EAClCS,EAAI,IAAI,MAAMqC,CAAE,EAChBL,EAAI,IAAI,MAAMI,CAAE,EAChB5B,EAEJ,IAAKA,EAAI,EAAGA,EAAI6B,EAAI,EAAE7B,EAAGR,EAAEQ,CAAC,EAAI8B,EAAM/C,EAAEiB,CAAC,EAAGhB,EAAEgB,CAAC,CAAC,EAChD,KAAOA,EAAI4B,EAAI,EAAE5B,EAAGwB,EAAExB,CAAC,EAAIhB,EAAEgB,CAAC,EAE9B,OAAO,SAASyB,EAAG,CACjB,IAAKzB,EAAI,EAAGA,EAAI6B,EAAI,EAAE7B,EAAGwB,EAAExB,CAAC,EAAIR,EAAEQ,CAAC,EAAEyB,CAAC,EACtC,OAAOD,CACR,CACH,CCrBe,SAAAO,GAAShD,EAAGC,EAAG,CAC5B,IAAIO,EAAI,IAAI,KACZ,OAAOR,EAAI,CAACA,EAAGC,EAAI,CAACA,EAAG,SAASyC,EAAG,CACjC,OAAOlC,EAAE,QAAQR,GAAK,EAAI0C,GAAKzC,EAAIyC,CAAC,EAAGlC,CACxC,CACH,CCHe,SAAAyC,GAASjD,EAAGC,EAAG,CAC5B,IAAIgB,EAAI,CAAE,EACNwB,EAAI,CAAE,EACNS,GAEAlD,IAAM,MAAQ,OAAOA,GAAM,YAAUA,EAAI,CAAE,IAC3CC,IAAM,MAAQ,OAAOA,GAAM,YAAUA,EAAI,CAAE,GAE/C,IAAKiD,KAAKjD,EACJiD,KAAKlD,EACPiB,EAAEiC,CAAC,EAAIH,EAAM/C,EAAEkD,CAAC,EAAGjD,EAAEiD,CAAC,CAAC,EAEvBT,EAAES,CAAC,EAAIjD,EAAEiD,CAAC,EAId,OAAO,SAASR,EAAG,CACjB,IAAKQ,KAAKjC,EAAGwB,EAAES,CAAC,EAAIjC,EAAEiC,CAAC,EAAER,CAAC,EAC1B,OAAOD,CACR,CACH,CCZe,SAAAU,EAASnD,EAAGC,EAAG,CAC5B,IAAIyC,EAAI,OAAOzC,EAAGwC,EAClB,OAAOxC,GAAK,MAAQyC,IAAM,UAAYU,GAASnD,CAAC,GACzCyC,IAAM,SAAWxB,EAClBwB,IAAM,UAAaD,EAAIY,EAAMpD,CAAC,IAAMA,EAAIwC,EAAGa,GAAOC,GAClDtD,aAAaoD,EAAQC,EACrBrD,aAAa,KAAO+C,GACpBL,GAAc1C,CAAC,EAAIuC,GACnB,MAAM,QAAQvC,CAAC,EAAI2C,GACnB,OAAO3C,EAAE,SAAY,YAAc,OAAOA,EAAE,UAAa,YAAc,MAAMA,CAAC,EAAIgD,GAClF/B,GAAQlB,EAAGC,CAAC,CACpB,CCrBe,SAAAuD,GAASxD,EAAGC,EAAG,CAC5B,OAAOD,EAAI,CAACA,EAAGC,EAAI,CAACA,EAAG,SAASyC,EAAG,CACjC,OAAO,KAAK,MAAM1C,GAAK,EAAI0C,GAAKzC,EAAIyC,CAAC,CACtC,CACH,CCJe,SAAQe,GAAChD,EAAG,CACzB,OAAO,KAAK,IAAIA,EAAI,KAAK,MAAMA,CAAC,CAAC,GAAK,KAChCA,EAAE,eAAe,IAAI,EAAE,QAAQ,KAAM,EAAE,EACvCA,EAAE,SAAS,EAAE,CACrB,CAKO,SAASiD,EAAmBjD,EAAGkD,EAAG,CACvC,IAAK1C,GAAKR,EAAIkD,EAAIlD,EAAE,cAAckD,EAAI,CAAC,EAAIlD,EAAE,cAAa,GAAI,QAAQ,GAAG,GAAK,EAAG,OAAO,KACxF,IAAIQ,EAAG2C,EAAcnD,EAAE,MAAM,EAAGQ,CAAC,EAIjC,MAAO,CACL2C,EAAY,OAAS,EAAIA,EAAY,CAAC,EAAIA,EAAY,MAAM,CAAC,EAAIA,EACjE,CAACnD,EAAE,MAAMQ,EAAI,CAAC,CACf,CACH,CCjBe,SAAQ4C,EAACpD,EAAG,CACzB,OAAOA,EAAIiD,EAAmB,KAAK,IAAIjD,CAAC,CAAC,EAAGA,EAAIA,EAAE,CAAC,EAAI,GACzD,CCJe,SAAAqD,GAASC,EAAUC,EAAW,CAC3C,OAAO,SAASjB,EAAOkB,EAAO,CAO5B,QANI,EAAIlB,EAAM,OACVL,EAAI,CAAE,EACNwB,EAAI,EACJC,EAAIJ,EAAS,CAAC,EACdK,EAAS,EAEN,EAAI,GAAKD,EAAI,IACdC,EAASD,EAAI,EAAIF,IAAOE,EAAI,KAAK,IAAI,EAAGF,EAAQG,CAAM,GAC1D1B,EAAE,KAAKK,EAAM,UAAU,GAAKoB,EAAG,EAAIA,CAAC,CAAC,EAChC,GAAAC,GAAUD,EAAI,GAAKF,KACxBE,EAAIJ,EAASG,GAAKA,EAAI,GAAKH,EAAS,MAAM,EAG5C,OAAOrB,EAAE,UAAU,KAAKsB,CAAS,CAClC,CACH,CCjBe,SAAQK,GAACC,EAAU,CAChC,OAAO,SAASvB,EAAO,CACrB,OAAOA,EAAM,QAAQ,SAAU,SAAS9B,EAAG,CACzC,OAAOqD,EAAS,CAACrD,CAAC,CACxB,CAAK,CACF,CACH,CCLA,IAAIsD,GAAK,2EAEM,SAASC,EAAgBC,EAAW,CACjD,GAAI,EAAEC,EAAQH,GAAG,KAAKE,CAAS,GAAI,MAAM,IAAI,MAAM,mBAAqBA,CAAS,EACjF,IAAIC,EACJ,OAAO,IAAIC,EAAgB,CACzB,KAAMD,EAAM,CAAC,EACb,MAAOA,EAAM,CAAC,EACd,KAAMA,EAAM,CAAC,EACb,OAAQA,EAAM,CAAC,EACf,KAAMA,EAAM,CAAC,EACb,MAAOA,EAAM,CAAC,EACd,MAAOA,EAAM,CAAC,EACd,UAAWA,EAAM,CAAC,GAAKA,EAAM,CAAC,EAAE,MAAM,CAAC,EACvC,KAAMA,EAAM,CAAC,EACb,KAAMA,EAAM,EAAE,CAClB,CAAG,CACH,CAEAF,EAAgB,UAAYG,EAAgB,UAErC,SAASA,EAAgBF,EAAW,CACzC,KAAK,KAAOA,EAAU,OAAS,OAAY,IAAMA,EAAU,KAAO,GAClE,KAAK,MAAQA,EAAU,QAAU,OAAY,IAAMA,EAAU,MAAQ,GACrE,KAAK,KAAOA,EAAU,OAAS,OAAY,IAAMA,EAAU,KAAO,GAClE,KAAK,OAASA,EAAU,SAAW,OAAY,GAAKA,EAAU,OAAS,GACvE,KAAK,KAAO,CAAC,CAACA,EAAU,KACxB,KAAK,MAAQA,EAAU,QAAU,OAAY,OAAY,CAACA,EAAU,MACpE,KAAK,MAAQ,CAAC,CAACA,EAAU,MACzB,KAAK,UAAYA,EAAU,YAAc,OAAY,OAAY,CAACA,EAAU,UAC5E,KAAK,KAAO,CAAC,CAACA,EAAU,KACxB,KAAK,KAAOA,EAAU,OAAS,OAAY,GAAKA,EAAU,KAAO,EACnE,CAEAE,EAAgB,UAAU,SAAW,UAAW,CAC9C,OAAO,KAAK,KACN,KAAK,MACL,KAAK,KACL,KAAK,QACJ,KAAK,KAAO,IAAM,KAClB,KAAK,QAAU,OAAY,GAAK,KAAK,IAAI,EAAG,KAAK,MAAQ,CAAC,IAC1D,KAAK,MAAQ,IAAM,KACnB,KAAK,YAAc,OAAY,GAAK,IAAM,KAAK,IAAI,EAAG,KAAK,UAAY,CAAC,IACxE,KAAK,KAAO,IAAM,IACnB,KAAK,IACb,EC7Ce,SAAQC,GAACC,EAAG,CACzBC,EAAK,QAASzC,EAAIwC,EAAE,OAAQ5D,EAAI,EAAG8D,EAAK,GAAI/C,EAAIf,EAAIoB,EAAG,EAAEpB,EACvD,OAAQ4D,EAAE5D,CAAC,EAAC,CACV,IAAK,IAAK8D,EAAK/C,EAAKf,EAAG,MACvB,IAAK,IAAS8D,IAAO,IAAGA,EAAK9D,GAAGe,EAAKf,EAAG,MACxC,QAAS,GAAI,CAAC,CAAC4D,EAAE5D,CAAC,EAAG,MAAM6D,EAASC,EAAK,IAAGA,EAAK,GAAG,KAC1D,CAEE,OAAOA,EAAK,EAAIF,EAAE,MAAM,EAAGE,CAAE,EAAIF,EAAE,MAAM7C,EAAK,CAAC,EAAI6C,CACrD,CCRO,IAAIG,GAEI,SAAAC,GAASxE,EAAGkD,EAAG,CAC5B,IAAInD,EAAIkD,EAAmBjD,EAAGkD,CAAC,EAC/B,GAAI,CAACnD,EAAG,OAAOC,EAAI,GACnB,IAAImD,EAAcpD,EAAE,CAAC,EACjBqD,EAAWrD,EAAE,CAAC,EACdS,EAAI4C,GAAYmB,GAAiB,KAAK,IAAI,GAAI,KAAK,IAAI,EAAG,KAAK,MAAMnB,EAAW,CAAC,CAAC,CAAC,EAAI,GAAK,EAC5FxB,EAAIuB,EAAY,OACpB,OAAO3C,IAAMoB,EAAIuB,EACX3C,EAAIoB,EAAIuB,EAAc,IAAI,MAAM3C,EAAIoB,EAAI,CAAC,EAAE,KAAK,GAAG,EACnDpB,EAAI,EAAI2C,EAAY,MAAM,EAAG3C,CAAC,EAAI,IAAM2C,EAAY,MAAM3C,CAAC,EAC3D,KAAO,IAAI,MAAM,EAAIA,CAAC,EAAE,KAAK,GAAG,EAAIyC,EAAmBjD,EAAG,KAAK,IAAI,EAAGkD,EAAI1C,EAAI,CAAC,CAAC,EAAE,CAAC,CAC3F,CCbe,SAAAiE,EAASzE,EAAGkD,EAAG,CAC5B,IAAInD,EAAIkD,EAAmBjD,EAAGkD,CAAC,EAC/B,GAAI,CAACnD,EAAG,OAAOC,EAAI,GACnB,IAAImD,EAAcpD,EAAE,CAAC,EACjBqD,EAAWrD,EAAE,CAAC,EAClB,OAAOqD,EAAW,EAAI,KAAO,IAAI,MAAM,CAACA,CAAQ,EAAE,KAAK,GAAG,EAAID,EACxDA,EAAY,OAASC,EAAW,EAAID,EAAY,MAAM,EAAGC,EAAW,CAAC,EAAI,IAAMD,EAAY,MAAMC,EAAW,CAAC,EAC7GD,EAAc,IAAI,MAAMC,EAAWD,EAAY,OAAS,CAAC,EAAE,KAAK,GAAG,CAC3E,CCNA,MAAeuB,EAAA,CACb,IAAK,CAAC1E,EAAGkD,KAAOlD,EAAI,KAAK,QAAQkD,CAAC,EAClC,EAAMlD,GAAM,KAAK,MAAMA,CAAC,EAAE,SAAS,CAAC,EACpC,EAAMA,GAAMA,EAAI,GAChB,EAAKgD,GACL,EAAK,CAAChD,EAAGkD,IAAMlD,EAAE,cAAckD,CAAC,EAChC,EAAK,CAAClD,EAAGkD,IAAMlD,EAAE,QAAQkD,CAAC,EAC1B,EAAK,CAAClD,EAAGkD,IAAMlD,EAAE,YAAYkD,CAAC,EAC9B,EAAMlD,GAAM,KAAK,MAAMA,CAAC,EAAE,SAAS,CAAC,EACpC,EAAK,CAACA,EAAGkD,IAAMuB,EAAczE,EAAI,IAAKkD,CAAC,EACvC,EAAKuB,EACL,EAAKD,GACL,EAAMxE,GAAM,KAAK,MAAMA,CAAC,EAAE,SAAS,EAAE,EAAE,YAAa,EACpD,EAAMA,GAAM,KAAK,MAAMA,CAAC,EAAE,SAAS,EAAE,CACvC,EClBe,SAAQ2E,EAAC3E,EAAG,CACzB,OAAOA,CACT,CCOA,IAAI4E,EAAM,MAAM,UAAU,IACtBC,EAAW,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG,EAEnE,SAAQC,GAACC,EAAQ,CAC9B,IAAIC,EAAQD,EAAO,WAAa,QAAaA,EAAO,YAAc,OAAYE,EAAW5B,GAAYuB,EAAI,KAAKG,EAAO,SAAU,MAAM,EAAGA,EAAO,UAAY,EAAE,EACzJG,EAAiBH,EAAO,WAAa,OAAY,GAAKA,EAAO,SAAS,CAAC,EAAI,GAC3EI,EAAiBJ,EAAO,WAAa,OAAY,GAAKA,EAAO,SAAS,CAAC,EAAI,GAC3EK,EAAUL,EAAO,UAAY,OAAY,IAAMA,EAAO,QAAU,GAChElB,EAAWkB,EAAO,WAAa,OAAYE,EAAWrB,GAAegB,EAAI,KAAKG,EAAO,SAAU,MAAM,CAAC,EACtGM,EAAUN,EAAO,UAAY,OAAY,IAAMA,EAAO,QAAU,GAChEO,EAAQP,EAAO,QAAU,OAAY,IAAMA,EAAO,MAAQ,GAC1DQ,EAAMR,EAAO,MAAQ,OAAY,MAAQA,EAAO,IAAM,GAE1D,SAASS,EAAUxB,EAAW,CAC5BA,EAAYD,EAAgBC,CAAS,EAErC,IAAIyB,EAAOzB,EAAU,KACjB0B,EAAQ1B,EAAU,MAClB2B,EAAO3B,EAAU,KACjB4B,EAAS5B,EAAU,OACnB/D,EAAO+D,EAAU,KACjBR,EAAQQ,EAAU,MAClB6B,EAAQ7B,EAAU,MAClB8B,EAAY9B,EAAU,UACtB+B,EAAO/B,EAAU,KACjBgC,EAAOhC,EAAU,KAGjBgC,IAAS,KAAKH,EAAQ,GAAMG,EAAO,KAG7BtB,EAAYsB,CAAI,IAAGF,IAAc,SAAcA,EAAY,IAAKC,EAAO,GAAMC,EAAO,MAG1F/F,GAASwF,IAAS,KAAOC,IAAU,OAAMzF,EAAO,GAAMwF,EAAO,IAAKC,EAAQ,KAI9E,IAAIO,GAASL,IAAW,IAAMV,EAAiBU,IAAW,KAAO,SAAS,KAAKI,CAAI,EAAI,IAAMA,EAAK,YAAa,EAAG,GAC9GE,GAASN,IAAW,IAAMT,EAAiB,OAAO,KAAKa,CAAI,EAAIX,EAAU,GAKzEc,EAAazB,EAAYsB,CAAI,EAC7BI,GAAc,aAAa,KAAKJ,CAAI,EAMxCF,EAAYA,IAAc,OAAY,EAChC,SAAS,KAAKE,CAAI,EAAI,KAAK,IAAI,EAAG,KAAK,IAAI,GAAIF,CAAS,CAAC,EACzD,KAAK,IAAI,EAAG,KAAK,IAAI,GAAIA,CAAS,CAAC,EAEzC,SAASO,EAAO/D,EAAO,CACrB,IAAIgE,EAAcL,GACdM,EAAcL,GACd1F,EAAGoB,EAAGI,EAEV,GAAIgE,IAAS,IACXO,EAAcJ,EAAW7D,CAAK,EAAIiE,EAClCjE,EAAQ,OACH,CACLA,EAAQ,CAACA,EAGT,IAAIkE,EAAgBlE,EAAQ,GAAK,EAAIA,EAAQ,EAiB7C,GAdAA,EAAQ,MAAMA,CAAK,EAAIiD,EAAMY,EAAW,KAAK,IAAI7D,CAAK,EAAGwD,CAAS,EAG9DC,IAAMzD,EAAQ6B,GAAW7B,CAAK,GAG9BkE,GAAiB,CAAClE,GAAU,GAAKqD,IAAS,MAAKa,EAAgB,IAGnEF,GAAeE,EAAiBb,IAAS,IAAMA,EAAOL,EAASK,IAAS,KAAOA,IAAS,IAAM,GAAKA,GAAQW,EAC3GC,GAAeP,IAAS,IAAMnB,EAAS,EAAIN,GAAiB,CAAC,EAAI,IAAMgC,GAAeC,GAAiBb,IAAS,IAAM,IAAM,IAIxHS,IAEF,IADA5F,EAAI,GAAIoB,EAAIU,EAAM,OACX,EAAE9B,EAAIoB,GACX,GAAII,EAAIM,EAAM,WAAW9B,CAAC,EAAG,GAAKwB,GAAKA,EAAI,GAAI,CAC7CuE,GAAevE,IAAM,GAAKoD,EAAU9C,EAAM,MAAM9B,EAAI,CAAC,EAAI8B,EAAM,MAAM9B,CAAC,GAAK+F,EAC3EjE,EAAQA,EAAM,MAAM,EAAG9B,CAAC,EACxB,KACd,EAGA,CAGUqF,GAAS,CAAC5F,IAAMqC,EAAQ0C,EAAM1C,EAAO,GAAQ,GAGjD,IAAIqB,EAAS2C,EAAY,OAAShE,EAAM,OAASiE,EAAY,OACzDE,EAAU9C,EAASH,EAAQ,IAAI,MAAMA,EAAQG,EAAS,CAAC,EAAE,KAAK8B,CAAI,EAAI,GAM1E,OAHII,GAAS5F,IAAMqC,EAAQ0C,EAAMyB,EAAUnE,EAAOmE,EAAQ,OAASjD,EAAQ+C,EAAY,OAAS,GAAQ,EAAGE,EAAU,IAG7Gf,EAAK,CACX,IAAK,IAAKpD,EAAQgE,EAAchE,EAAQiE,EAAcE,EAAS,MAC/D,IAAK,IAAKnE,EAAQgE,EAAcG,EAAUnE,EAAQiE,EAAa,MAC/D,IAAK,IAAKjE,EAAQmE,EAAQ,MAAM,EAAG9C,EAAS8C,EAAQ,QAAU,CAAC,EAAIH,EAAchE,EAAQiE,EAAcE,EAAQ,MAAM9C,CAAM,EAAG,MAC9H,QAASrB,EAAQmE,EAAUH,EAAchE,EAAQiE,EAAa,KACtE,CAEM,OAAO1C,EAASvB,CAAK,CAC3B,CAEI,OAAA+D,EAAO,SAAW,UAAW,CAC3B,OAAOrC,EAAY,EACpB,EAEMqC,CACX,CAEE,SAASK,EAAa1C,EAAW1B,EAAO,CACtC,IAAI3C,EAAI6F,GAAWxB,EAAYD,EAAgBC,CAAS,EAAGA,EAAU,KAAO,IAAKA,EAAW,EACxF2C,EAAI,KAAK,IAAI,GAAI,KAAK,IAAI,EAAG,KAAK,MAAMvD,EAASd,CAAK,EAAI,CAAC,CAAC,CAAC,EAAI,EACjEG,EAAI,KAAK,IAAI,GAAI,CAACkE,CAAC,EACnBV,EAASpB,EAAS,EAAI8B,EAAI,CAAC,EAC/B,OAAO,SAASrE,EAAO,CACrB,OAAO3C,EAAE8C,EAAIH,CAAK,EAAI2D,CACvB,CACL,CAEE,MAAO,CACL,OAAQT,EACR,aAAckB,CACf,CACH,CCjJA,IAAI3B,EACOsB,GACAK,GAEXE,GAAc,CACZ,UAAW,IACX,SAAU,CAAC,CAAC,EACZ,SAAU,CAAC,IAAK,EAAE,CACpB,CAAC,EAEc,SAASA,GAAcC,EAAY,CAChD,OAAA9B,EAASD,GAAa+B,CAAU,EAChCR,GAAStB,EAAO,OAChB2B,GAAe3B,EAAO,aACfA,CACT,CCfe,SAAQ+B,GAAC3F,EAAM,CAC5B,OAAO,KAAK,IAAI,EAAG,CAACiC,EAAS,KAAK,IAAIjC,CAAI,CAAC,CAAC,CAC9C,CCFe,SAAA4F,GAAS5F,EAAMmB,EAAO,CACnC,OAAO,KAAK,IAAI,EAAG,KAAK,IAAI,GAAI,KAAK,IAAI,EAAG,KAAK,MAAMc,EAASd,CAAK,EAAI,CAAC,CAAC,CAAC,EAAI,EAAIc,EAAS,KAAK,IAAIjC,CAAI,CAAC,CAAC,CAC9G,CCFe,SAAA6F,GAAS7F,EAAM8F,EAAK,CACjC,OAAA9F,EAAO,KAAK,IAAIA,CAAI,EAAG8F,EAAM,KAAK,IAAIA,CAAG,EAAI9F,EACtC,KAAK,IAAI,EAAGiC,EAAS6D,CAAG,EAAI7D,EAASjC,CAAI,CAAC,EAAI,CACvD,CCLe,SAAS+F,GAAUlH,EAAG,CACnC,OAAO,UAAW,CAChB,OAAOA,CACR,CACH,CCJe,SAASS,GAAOT,EAAG,CAChC,MAAO,CAACA,CACV,CCGA,IAAImH,EAAO,CAAC,EAAG,CAAC,EAET,SAASlC,EAASjF,EAAG,CAC1B,OAAOA,CACT,CAEA,SAASoH,EAAU7H,EAAGC,EAAG,CACvB,OAAQA,GAAMD,EAAI,CAACA,GACb,SAASS,EAAG,CAAE,OAAQA,EAAIT,GAAKC,CAAE,EACjCmD,GAAS,MAAMnD,CAAC,EAAI,IAAM,EAAG,CACrC,CAEA,SAAS6H,GAAQ9H,EAAGC,EAAG,CACrB,IAAIyC,EACJ,OAAI1C,EAAIC,IAAGyC,EAAI1C,EAAGA,EAAIC,EAAGA,EAAIyC,GACtB,SAASjC,EAAG,CAAE,OAAO,KAAK,IAAIT,EAAG,KAAK,IAAIC,EAAGQ,CAAC,CAAC,CAAI,CAC5D,CAIA,SAASsH,GAAMC,EAAQC,EAAO9E,EAAa,CACzC,IAAI+E,EAAKF,EAAO,CAAC,EAAGG,EAAKH,EAAO,CAAC,EAAGI,EAAKH,EAAM,CAAC,EAAGI,EAAKJ,EAAM,CAAC,EAC/D,OAAIE,EAAKD,GAAIA,EAAKL,EAAUM,EAAID,CAAE,EAAGE,EAAKjF,EAAYkF,EAAID,CAAE,IACvDF,EAAKL,EAAUK,EAAIC,CAAE,EAAGC,EAAKjF,EAAYiF,EAAIC,CAAE,GAC7C,SAAS5H,EAAG,CAAE,OAAO2H,EAAGF,EAAGzH,CAAC,CAAC,CAAI,CAC1C,CAEA,SAAS6H,GAAQN,EAAQC,EAAO9E,EAAa,CAC3C,IAAIe,EAAI,KAAK,IAAI8D,EAAO,OAAQC,EAAM,MAAM,EAAI,EAC5CzH,EAAI,IAAI,MAAM0D,CAAC,EACfqE,EAAI,IAAI,MAAMrE,CAAC,EACfjD,EAAI,GAQR,IALI+G,EAAO9D,CAAC,EAAI8D,EAAO,CAAC,IACtBA,EAASA,EAAO,MAAO,EAAC,QAAS,EACjCC,EAAQA,EAAM,MAAO,EAAC,QAAS,GAG1B,EAAEhH,EAAIiD,GACX1D,EAAES,CAAC,EAAI4G,EAAUG,EAAO/G,CAAC,EAAG+G,EAAO/G,EAAI,CAAC,CAAC,EACzCsH,EAAEtH,CAAC,EAAIkC,EAAY8E,EAAMhH,CAAC,EAAGgH,EAAMhH,EAAI,CAAC,CAAC,EAG3C,OAAO,SAASR,EAAG,CACjB,IAAIQ,EAAIuH,GAAOR,EAAQvH,EAAG,EAAGyD,CAAC,EAAI,EAClC,OAAOqE,EAAEtH,CAAC,EAAET,EAAES,CAAC,EAAER,CAAC,CAAC,CACpB,CACH,CAEO,SAASgI,GAAKC,EAAQC,EAAQ,CACnC,OAAOA,EACF,OAAOD,EAAO,OAAQ,CAAA,EACtB,MAAMA,EAAO,MAAO,CAAA,EACpB,YAAYA,EAAO,YAAa,CAAA,EAChC,MAAMA,EAAO,MAAO,CAAA,EACpB,QAAQA,EAAO,SAAS,CAC/B,CAEO,SAASE,IAAc,CAC5B,IAAIZ,EAASJ,EACTK,EAAQL,EACRzE,EAAc0F,EACdC,EACAC,EACAC,EACAC,EAAQvD,EACRwD,EACAC,EACAC,EAEJ,SAASC,GAAU,CACjB,IAAIhH,EAAI,KAAK,IAAI2F,EAAO,OAAQC,EAAM,MAAM,EAC5C,OAAIgB,IAAUvD,IAAUuD,EAAQnB,GAAQE,EAAO,CAAC,EAAGA,EAAO3F,EAAI,CAAC,CAAC,GAChE6G,EAAY7G,EAAI,EAAIiG,GAAUP,GAC9BoB,EAASC,EAAQ,KACVE,CACX,CAEE,SAASA,EAAM7I,EAAG,CAChB,OAAOA,GAAK,MAAQ,MAAMA,EAAI,CAACA,CAAC,EAAIuI,GAAWG,IAAWA,EAASD,EAAUlB,EAAO,IAAIc,CAAS,EAAGb,EAAO9E,CAAW,IAAI2F,EAAUG,EAAMxI,CAAC,CAAC,CAAC,CACjJ,CAEE,OAAA6I,EAAM,OAAS,SAASC,EAAG,CACzB,OAAON,EAAMF,GAAaK,IAAUA,EAAQF,EAAUjB,EAAOD,EAAO,IAAIc,CAAS,EAAGU,CAAiB,IAAID,CAAC,CAAC,CAAC,CAC7G,EAEDD,EAAM,OAAS,SAASG,EAAG,CACzB,OAAO,UAAU,QAAUzB,EAAS,MAAM,KAAKyB,EAAGvI,EAAM,EAAGmI,KAAarB,EAAO,MAAO,CACvF,EAEDsB,EAAM,MAAQ,SAASG,EAAG,CACxB,OAAO,UAAU,QAAUxB,EAAQ,MAAM,KAAKwB,CAAC,EAAGJ,EAAO,GAAMpB,EAAM,MAAO,CAC7E,EAEDqB,EAAM,WAAa,SAASG,EAAG,CAC7B,OAAOxB,EAAQ,MAAM,KAAKwB,CAAC,EAAGtG,EAAcK,GAAkB6F,EAAS,CACxE,EAEDC,EAAM,MAAQ,SAASG,EAAG,CACxB,OAAO,UAAU,QAAUR,EAAQQ,EAAI,GAAO/D,EAAU2D,KAAaJ,IAAUvD,CAChF,EAED4D,EAAM,YAAc,SAASG,EAAG,CAC9B,OAAO,UAAU,QAAUtG,EAAcsG,EAAGJ,EAAS,GAAIlG,CAC1D,EAEDmG,EAAM,QAAU,SAASG,EAAG,CAC1B,OAAO,UAAU,QAAUT,EAAUS,EAAGH,GAASN,CAClD,EAEM,SAAStG,EAAGgH,EAAG,CACpB,OAAAZ,EAAYpG,EAAGqG,EAAcW,EACtBL,EAAS,CACjB,CACH,CAEe,SAASM,IAAa,CACnC,OAAOf,GAAW,EAAGlD,EAAUA,CAAQ,CACzC,CCzHe,SAASkE,GAAWnI,EAAOC,EAAMC,EAAO8C,EAAW,CAChE,IAAI7C,EAAOW,GAASd,EAAOC,EAAMC,CAAK,EAClC4E,EAEJ,OADA9B,EAAYD,EAAgBC,GAAoB,IAAgB,EACxDA,EAAU,KAAI,CACpB,IAAK,IAAK,CACR,IAAI1B,EAAQ,KAAK,IAAI,KAAK,IAAItB,CAAK,EAAG,KAAK,IAAIC,CAAI,CAAC,EACpD,OAAI+C,EAAU,WAAa,MAAQ,CAAC,MAAM8B,EAAYiB,GAAgB5F,EAAMmB,CAAK,CAAC,IAAG0B,EAAU,UAAY8B,GACpGY,GAAa1C,EAAW1B,CAAK,CAC1C,CACI,IAAK,GACL,IAAK,IACL,IAAK,IACL,IAAK,IACL,IAAK,IAAK,CACJ0B,EAAU,WAAa,MAAQ,CAAC,MAAM8B,EAAYkB,GAAe7F,EAAM,KAAK,IAAI,KAAK,IAAIH,CAAK,EAAG,KAAK,IAAIC,CAAI,CAAC,CAAC,CAAC,IAAG+C,EAAU,UAAY8B,GAAa9B,EAAU,OAAS,MAC9K,KACN,CACI,IAAK,IACL,IAAK,IAAK,CACJA,EAAU,WAAa,MAAQ,CAAC,MAAM8B,EAAYgB,GAAe3F,CAAI,CAAC,IAAG6C,EAAU,UAAY8B,GAAa9B,EAAU,OAAS,KAAO,GAC1I,KACN,CACA,CACE,OAAOqC,GAAOrC,CAAS,CACzB,CCvBO,SAASoF,GAAUP,EAAO,CAC/B,IAAItB,EAASsB,EAAM,OAEnB,OAAAA,EAAM,MAAQ,SAAS3H,EAAO,CAC5B,IAAInB,EAAIwH,EAAQ,EAChB,OAAO7F,GAAM3B,EAAE,CAAC,EAAGA,EAAEA,EAAE,OAAS,CAAC,EAAGmB,GAAgB,EAAU,CAC/D,EAED2H,EAAM,WAAa,SAAS3H,EAAO8C,EAAW,CAC5C,IAAIjE,EAAIwH,EAAQ,EAChB,OAAO4B,GAAWpJ,EAAE,CAAC,EAAGA,EAAEA,EAAE,OAAS,CAAC,EAAGmB,GAAgB,GAAY8C,CAAS,CAC/E,EAED6E,EAAM,KAAO,SAAS3H,EAAO,CACvBA,GAAS,OAAMA,EAAQ,IAE3B,IAAInB,EAAIwH,EAAQ,EACZjD,EAAK,EACL/C,EAAKxB,EAAE,OAAS,EAChBiB,EAAQjB,EAAEuE,CAAE,EACZrD,EAAOlB,EAAEwB,CAAE,EACX8H,EACAlI,EACAmI,EAAU,GAOd,IALIrI,EAAOD,IACTG,EAAOH,EAAOA,EAAQC,EAAMA,EAAOE,EACnCA,EAAOmD,EAAIA,EAAK/C,EAAIA,EAAKJ,GAGpBmI,KAAY,GAAG,CAEpB,GADAnI,EAAOU,EAAcb,EAAOC,EAAMC,CAAK,EACnCC,IAASkI,EACX,OAAAtJ,EAAEuE,CAAE,EAAItD,EACRjB,EAAEwB,CAAE,EAAIN,EACDsG,EAAOxH,CAAC,EACV,GAAIoB,EAAO,EAChBH,EAAQ,KAAK,MAAMA,EAAQG,CAAI,EAAIA,EACnCF,EAAO,KAAK,KAAKA,EAAOE,CAAI,EAAIA,UACvBA,EAAO,EAChBH,EAAQ,KAAK,KAAKA,EAAQG,CAAI,EAAIA,EAClCF,EAAO,KAAK,MAAMA,EAAOE,CAAI,EAAIA,MAEjC,OAEFkI,EAAUlI,CAChB,CAEI,OAAO0H,CACR,EAEMA,CACT,CAEe,SAASU,IAAS,CAC/B,IAAIV,EAAQK,GAAY,EAExB,OAAAL,EAAM,KAAO,UAAW,CACtB,OAAOb,GAAKa,EAAOU,IAAQ,CAC5B,EAEDC,GAAU,MAAMX,EAAO,SAAS,EAEzBO,GAAUP,CAAK,CACxB", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31]}