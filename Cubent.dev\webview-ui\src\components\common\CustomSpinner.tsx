import React from "react"

interface CustomSpinnerProps {
	size?: number
	className?: string
}

export const CustomSpinner: React.FC<CustomSpinnerProps> = ({ size = 8, className = "" }) => {
	return (
		<svg
			width={size}
			height={size}
			viewBox="0 0 496 496"
			className={`animate-spin ${className}`}
			xmlns="http://www.w3.org/2000/svg">
			{/* Vertical lines */}
			<path
				d="M256,478.3c0,4.8-3.2,8.8-8,8.8l0,0c-4.8,0-8-4-8-8.8V341.5c0-4.8,3.2-8.8,8-8.8l0,0c4.8,0,8,4,8,8.8V478.3z"
				fill="currentColor"
				opacity="0.6"
			/>
			<path
				d="M496,239.9c0,4.8-3.2,8-8,8H352c-4.8,0-8-3.2-8-8l0,0c0-4.8,3.2-8,8-8h136C492.8,231.9,496,235.1,496,239.9L496,239.9z"
				fill="currentColor"
				opacity="0.85"
			/>
			<path
				d="M152,239.9c0,4.8-3.2,8-8,8H8c-4.8,0-8-3.2-8-8l0,0c0-4.8,3.2-8,8-8h136C148.8,231.9,152,235.1,152,239.9L152,239.9z"
				fill="currentColor"
				opacity="0.32"
			/>

			{/* Diagonal lines */}
			<path
				d="M326.4,172.7c-3.2,3.2-8.8,3.2-12,0l0,0c-3.2-3.2-3.2-8.8,0-12l96.8-96.8c3.2-3.2,8.8-3.2,12,0l0,0c3.2,3.2,3.2,8.8,0,12L326.4,172.7z"
				fill="currentColor"
				opacity="0.93"
			/>
			<path
				d="M84.8,414.3c-3.2,3.2-8.8,3.2-12,0l0,0c-3.2-3.2-3.2-8.8,0-12l96.8-96.8c3.2-3.2,8.8-3.2,12,0l0,0c3.2,3.2,3.2,8.8,0,12L84.8,414.3z"
				fill="currentColor"
				opacity="0.48"
			/>
			<path
				d="M314.4,317.5c-3.2-3.2-3.2-8.8,0-12l0,0c3.2-3.2,8.8-3.2,12,0l96.8,96.8c3.2,3.2,3.2,8.8,0,12l0,0c-3.2,3.2-8.8,3.2-12,0L314.4,317.5z"
				fill="currentColor"
				opacity="0.75"
			/>
			<path
				d="M72.8,75.1c-3.2-3.2-3.2-8,0-11.2l0,0c3.2-3.2,8.8-3.2,12,0l96.8,96.8c3.2,3.2,3.2,8.8,0,12l0,0c-3.2,3.2-8.8,3.2-12,0L72.8,75.1z"
				fill="currentColor"
				opacity="0.17"
			/>

			{/* Angled lines */}
			<path
				d="M295.2,147.1c-2.4,4.8-7.2,6.4-11.2,4.8l0,0c-4-1.6-6.4-6.4-4.8-11.2l52-126.4c1.6-4,6.4-6.4,11.2-4.8l0,0c4,1.6,6.4,6.4,4.8,11.2L295.2,147.1z"
				fill="currentColor"
				opacity="0.96"
			/>
			<path
				d="M164.8,463.9c-1.6,4-6.4,6.4-11.2,4.8l0,0c-4-1.6-6.4-6.4-4.8-11.2l52-126.4c1.6-4,6.4-6.4,11.2-4.8l0,0c4,1.6,6.4,6.4,4.8,11.2L164.8,463.9z"
				fill="currentColor"
				opacity="0.54"
			/>
			<path
				d="M340,285.5c-4-1.6-6.4-6.4-4.8-11.2l0,0c1.6-4,6.4-6.4,11.2-4.8l126.4,52c4,1.6,6.4,6.4,4.8,11.2l0,0c-1.6,4-6.4,6.4-11.2,4.8L340,285.5z"
				fill="currentColor"
				opacity="0.8"
			/>
			<path
				d="M23.2,155.1c-4-1.6-6.4-6.4-4.8-11.2l0,0c1.6-4,6.4-6.4,11.2-4.8L156,191.9c4,1.6,6.4,6.4,4.8,11.2l0,0c-1.6,4-6.4,6.4-11.2,4.8L23.2,155.1z"
				fill="currentColor"
				opacity="0.25"
			/>
			<path
				d="M345.6,207.1c-4,1.6-8.8,0-11.2-4.8l0,0c-1.6-4,0-9.6,4.8-11.2l126.4-52.8c4-1.6,9.6,0,11.2,4.8l0,0c1.6,4,0,9.6-4.8,11.2L345.6,207.1z"
				fill="currentColor"
				opacity="0.88"
			/>
			<path
				d="M30.4,339.1c-4,1.6-9.6,0-11.2-4.8l0,0c-1.6-4,0-9.6,4.8-11.2l126.4-52.8c4-1.6,8.8,0,11.2,4.8l0,0c1.6,4,0,9.6-4.8,11.2L30.4,339.1z"
				fill="currentColor"
				opacity="0.43"
			/>
			<path
				d="M280,336.7c-1.6-4,0-8.8,4.8-11.2l0,0c4-1.6,9.6,0,11.2,4.8l52.8,126.4c1.6,4,0,9.6-4.8,11.2l0,0c-4,1.6-9.6,0-11.2-4.8L280,336.7z"
				fill="currentColor"
				opacity="0.68"
			/>
			<path
				d="M148,20.7c-1.6-4,0-9.6,4.8-11.2l0,0c4-1.6,9.6,0,11.2,4.8l52,126.4c1.6,4,0,9.6-4.8,11.2l0,0c-4,1.6-9.6,0-11.2-4.8L148,20.7z"
				fill="currentColor"
				opacity="0.05"
			/>
		</svg>
	)
}
